# BDoggy 系统监控功能

## 功能概述

为 BDoggy 应用添加了完整的系统监控功能，可以实时监控和记录 macOS 系统的各项性能指标。

## 主要功能

### 1. 数据采集模块
- **系统基础信息**：设备型号、CPU 名称、内存大小、磁盘总容量、macOS 版本
- **CPU 监控**：实时 CPU 使用率、Top 5 占用进程
- **内存监控**：内存使用率、可用内存、Swap 使用情况
- **磁盘监控**：剩余磁盘空间、磁盘使用率
- **电池监控**：电池电量、健康状态、循环次数、电源状态
- **温度监控**：CPU/GPU 温度、风扇转速（框架已准备，需要进一步实现）
- **应用跟踪**：当前活跃前台应用的 bundle identifier 与名称

### 2. 数据存储模块
- **SQLite 数据库**：本地持久化存储
- **数据表结构**：
  - `system_info`：设备静态信息
  - `metrics_log`：动态性能数据
  - `app_usage`：应用使用记录
  - `monitor_config`：监控配置
- **数据管理**：支持按日期查询、数据清理、一键清除

### 3. 定时任务调度
- **可配置频率**：1分钟、5分钟、1小时
- **后台运行**：使用 Timer 实现定时采集
- **自动启动**：应用启动时自动恢复监控状态

### 4. 隐私合规与权限管理
- **权限检查**：辅助功能权限、完全磁盘访问权限
- **用户引导**：详细的权限设置指导
- **数据安全**：所有数据仅存储在本地 `~/Library/Application Support/BDoggy/`
- **数据控制**：支持一键清除所有数据

## 文件结构

```
BDoggy/
├── Models/
│   └── SystemMonitorModels.swift          # 数据模型定义
├── Utils/
│   ├── SystemInfoCollector.swift          # 系统信息采集器
│   ├── SystemMetricsCollector.swift       # 系统指标采集器
│   ├── SystemMonitorDatabase.swift        # 数据库管理器
│   ├── SystemMonitorManager.swift         # 监控管理器
│   └── PrivacyPermissionManager.swift     # 权限管理器
├── Views/
│   ├── SystemMonitorView.swift            # 主监控视图
│   └── Components/
│       └── SystemMonitorComponents.swift  # 监控组件
├── Extensions/
│   └── SystemMonitorExtensions.swift      # 扩展和工具函数
└── BDoggyApp.swift                        # 应用入口（已更新）
```

## 使用方法

### 1. 启动监控
1. 在主界面点击系统监控按钮（图表图标）
2. 在监控界面点击"开始"按钮
3. 根据提示授予必要权限

### 2. 配置监控
1. 点击设置按钮（齿轮图标）
2. 配置采集频率和监控项目
3. 设置数据保留天数

### 3. 查看数据
- **实时监控**：查看当前系统状态
- **历史趋势**：查看 CPU 和内存使用率趋势图
- **系统信息**：查看设备硬件信息和监控统计

### 4. 权限设置
如果需要监控活跃应用，需要授予辅助功能权限：
1. 系统偏好设置 → 安全性与隐私 → 隐私 → 辅助功能
2. 添加并勾选 BDoggy
3. 重启应用

## 技术实现

### 数据采集技术
- **IOKit**：硬件信息和电池状态
- **sysctl**：CPU、内存、系统信息
- **NSWorkspace**：活跃应用信息
- **FileManager**：磁盘使用情况

### 数据库设计
- **SQLite3**：轻量级本地数据库
- **索引优化**：时间戳索引提高查询性能
- **数据压缩**：JSON 序列化复杂数据结构

### 权限管理
- **AXIsProcessTrusted**：检查辅助功能权限
- **文件访问测试**：检查完全磁盘访问权限
- **用户引导**：详细的设置步骤说明

## 隐私保护

### 数据收集原则
- 仅收集系统性能指标，不涉及个人文件
- 应用跟踪功能需要用户明确授权
- 所有数据仅用于本地分析和显示

### 数据存储安全
- 数据存储在用户本地设备
- 存储路径：`~/Library/Application Support/BDoggy/`
- 支持用户随时删除所有数据

### 权限最小化
- 仅请求必要的系统权限
- 提供详细的权限用途说明
- 支持部分功能在无权限情况下运行

## 性能优化

### 采集优化
- 异步数据采集，避免阻塞主线程
- 智能采集频率，平衡精度和性能
- 错误处理和重试机制

### 存储优化
- 批量数据插入提高写入性能
- 定期清理过期数据控制存储空间
- 索引优化提高查询速度

### UI 优化
- 实时数据更新不影响用户交互
- 图表数据点数量限制避免性能问题
- 懒加载和虚拟化处理大量数据

## 扩展性

### 新增监控项目
1. 在 `SystemMetricsCollector` 中添加采集方法
2. 更新 `SystemMetrics` 模型
3. 修改数据库表结构
4. 添加相应的 UI 组件

### 新增数据源
1. 创建新的采集器类
2. 实现数据模型
3. 添加数据库表
4. 集成到监控管理器

### 导出功能
- 支持导出 CSV 格式数据
- 支持导出图表图片
- 支持数据备份和恢复

## 故障排除

### 常见问题
1. **权限问题**：按照应用内指导重新设置权限
2. **数据库错误**：尝试清除数据或重启应用
3. **采集失败**：检查系统状态，重启监控功能

### 日志调试
- 使用 `Logger.systemMonitor()` 查看详细日志
- 检查控制台输出了解错误信息
- 联系技术支持获取帮助

## 版本信息
- **版本**：1.0.0
- **兼容性**：macOS 14+
- **依赖**：Swift 5+, SwiftUI, SQLite3
- **更新日期**：2024年12月11日
