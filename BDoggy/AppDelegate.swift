//
//  AppDelegate.swift
//  BDoggy
//
//  Created by K4 on 2024/12/9.
//

import Cocoa
import StoreKit
import SwiftUI

// @main
class AppDelegate: NSObject, NSApplicationDelegate {
    private var statusBarController: StatusBarController?
    
    private func initializeFirstLaunch() {
        let defaults = UserDefaults.standard
        let hasLaunched = defaults.bool(forKey: Constants.HasLaunchedBeforeKey)
        
        if !hasLaunched {
            let accountToken = UserManager.appAccountToken
            // 标记已启动
            defaults.setValue(true, forKey: Constants.HasLaunchedBeforeKey)
            // 其他首次启动初始化操作
            Logger.info("首次启动初始化完成", accountToken)
        }
        
        Logger.debug("账号Token：", UserManager.appAccountToken)
    }

    func applicationDidFinishLaunching(_ aNotification: Notification) {
        // 首次启动初始化
        initializeFirstLaunch()

        // 初始化增强版系统监控
        SystemMonitorInitializer.shared.initialize()

        statusBarController = StatusBarController()
        statusBarController?.start()
        // 检查应用更新
        checkForUpdates()

        // 延迟启动系统监控，确保应用完全加载
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            SystemMonitorInitializer.shared.startMonitoring()
        }
    }
    
    func applicationWillTerminate(_ aNotification: Notification) {
        // 停止系统监控
        SystemMonitorInitializer.shared.stopMonitoring()
        statusBarController?.stop()
    }
    
    @objc func quitApp() {
        NSApplication.shared.terminate(self)
    }
    
    // 检查应用更新
    private func checkForUpdates() {
        Task {
            do {
                // 使用 SKStoreProductViewController 检查更新
                let bundleID = Bundle.main.bundleIdentifier ?? ""
                
                // 获取应用在 App Store 的信息
                let lookupURL = URL(string: "https://itunes.apple.com/lookup?bundleId=\(bundleID)")!
                
                let (data, _) = try await URLSession.shared.data(from: lookupURL)
                
                if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let results = json["results"] as? [[String: Any]],
                   let appInfo = results.first,
                   let storeVersion = appInfo["version"] as? String
                {
                    // 获取当前应用版本
                    let currentVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? ""
                    
                    Logger.info("当前版本：\(storeVersion),远端版本：", currentVersion)
                    // 比较版本号
                    if compareVersions(storeVersion, currentVersion) > 0 {
                        // 有新版本可用，显示更新提示
                        await MainActor.run {
                            showUpdateAlert(storeVersion: storeVersion)
                        }
                    }
                }
                
                Logger.info("检查更新完成")
            } catch {
                Logger.error("检查更新失败: \(error.localizedDescription)")
            }
        }
    }
    
    // 比较版本号
    private func compareVersions(_ version1: String, _ version2: String) -> Int {
        let components1 = version1.components(separatedBy: ".").map { Int($0) ?? 0 }
        let components2 = version2.components(separatedBy: ".").map { Int($0) ?? 0 }
        
        for i in 0 ..< max(components1.count, components2.count) {
            let v1 = i < components1.count ? components1[i] : 0
            let v2 = i < components2.count ? components2[i] : 0
            
            if v1 > v2 {
                return 1
            } else if v1 < v2 {
                return -1
            }
        }
        
        return 0
    }
    
    // 显示更新提示
    private func showUpdateAlert(storeVersion: String) {
        let alert = NSAlert()
        alert.messageText = "app_update_available".localized
        alert.informativeText = String(format: "app_update_message".localized, storeVersion)
        alert.alertStyle = .informational
        
        alert.addButton(withTitle: "update".localized)
        alert.addButton(withTitle: "cancel".localized)
        
        let response = alert.runModal()
        
        if response == .alertFirstButtonReturn {
            // 用户点击了"更新"按钮
            openAppStoreForUpdate()
        }
    }
    
    // 打开 App Store 更新页面
    private func openAppStoreForUpdate() {
        if let bundleID = Bundle.main.bundleIdentifier,
           let appStoreURL = URL(string: "macappstore://apps.apple.com/app/id\(bundleID)")
        {
            NSWorkspace.shared.open(appStoreURL)
        } else {
            // 备用方案：打开通用的 Mac App Store 页面
            if let url = URL(string: "macappstore://") {
                NSWorkspace.shared.open(url)
            }
        }
    }
}

@Observable
class StatusBarController {
    // MARK: - Properties

    private let statusItem: NSStatusItem
    private let popOver = NSPopover()
    private var hostingController: AutoResizingHostingController<MenuBarView>?
    
    private var animationFrames: [NSImage] = []
    private var frameIndex = 0
    private var timeInterval: Int = 100
    private let timerManager: GCDTimerManager
    private let fileHelper = FileHelper.shared
    private let frameIntervalCalculator: FrameIntervalCalculator
    
    private var isRunning = false
    private var isUpdating = false
    
    private var usageObserver: NSObjectProtocol?
    private var changeObserver: NSObjectProtocol?
    
    // MARK: - Constants

    static let notificationChangeIcons = Notification.Name("notificationChangeIcons")
    private let totalMaxInterval: Int = Constants.totalMaxInterval
    private let frameMinInterval: Int = Constants.frameMinInterval
    
    private var viewModel = SystemUsageModel()
    
    // MARK: - Initialization

    init() {
        timerManager = GCDTimerManager(interval: 300)
        statusItem = NSStatusBar.system.statusItem(withLength: NSStatusItem.variableLength)
        frameIntervalCalculator = FrameIntervalCalculator(
            totalMaxInterval: Constants.totalMaxInterval,
            frameMinInterval: Constants.frameMinInterval
        )
//        guard let button = statusItem.button else { return }
//        let image = NSImage(named: "Doggy0")
//        button.image = image
        setupInitialState()
        configureStatusItem()
    }
    
    func showView() {}
    
    private func setupInitialState() {
        let folderName = UserDefaults.standard.string(forKey: Constants.useIconKey) ?? "Doggy"
        loadImages(from: folderName)
    }
    
    private func configureStatusItem() {
        guard let button = statusItem.button, !animationFrames.isEmpty else { return }
        button.image = animationFrames[0]
        // 设置图片的缩放行为
        button.imagePosition = .imageOnly
        button.imageScaling = NSImageScaling.scaleProportionallyDown
        button.action = #selector(toggleMenu)
        button.target = self
        button.toolTip = "Doggy Doggy!~"
    }
    
    // MARK: - Public Methods

    func start() {
        guard !isRunning else { return }
        isRunning = true
        startAnimation()
        setupObservers()
        SystemUsageMonitor.shared.startMonitoring()
    }
    
    func stop() {
        guard isRunning else { return }
        isRunning = false
        stopAnimation()
        removeObservers()
        SystemUsageMonitor.shared.stopMonitoring()
        animationFrames.removeAll()
        frameIndex = 0
    }
    
    func restart() {
        stop()
        setupInitialState()
        start()
    }
    
    // MARK: - Private Methods

    private func loadImages(from folderName: String) {
        animationFrames.removeAll()
        
        if folderName == "Doggy" {
            loadDefaultImages()
            return
        }
        
        loadCustomImages(from: folderName)
    }
    
    private func loadDefaultImages() {
        for i in 0 ... 17 {
            if let image = NSImage(named: "Doggy\(i)") {
                // 获取原始图片尺寸
                let originalSize = image.size
                // 计算基于高度的等比例缩放
                let targetHeight: CGFloat = 26.0 // 状态栏标准高度
                let scale = targetHeight / originalSize.height
                let targetWidth = originalSize.width * scale
                image.size = NSSize(width: targetWidth, height: targetHeight)
                image.resizingMode = .stretch
                animationFrames.append(image)
            }
        }
    }
    
    private func loadCustomImages(from folderName: String) {
        let result = fileHelper.scanFolder(from: folderName)
        if let folderPath = result.folderPath, !result.imageFileNames.isEmpty {
            for fileName in result.imageFileNames {
                if let image = NSImage(contentsOf: folderPath.appendingPathComponent(fileName)) {
                    // 获取原始图片尺寸
                    let originalSize = image.size
                    // 计算基于高度的等比例缩放
                    let targetHeight: CGFloat = 26.0 // 状态栏标准高度
                    let scale = targetHeight / originalSize.height
                    let targetWidth = originalSize.width * scale
                    image.size = NSSize(width: targetWidth, height: targetHeight)
                    image.resizingMode = .stretch
                    animationFrames.append(image)
                }
            }
        } else {
            loadDefaultImages()
        }
    }
    
    private func setupObservers() {
        usageObserver = NotificationCenter.default.addObserver(
            forName: SystemUsageMonitor.notificationName,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            self?.handleUsageUpdate(notification)
        }
        
        changeObserver = NotificationCenter.default.addObserver(
            forName: Self.notificationChangeIcons,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            self?.handleIconChange(notification)
        }
    }
    
    private func removeObservers() {
        if let usageObserver = usageObserver {
            NotificationCenter.default.removeObserver(usageObserver)
        }
        if let changeObserver = changeObserver {
            NotificationCenter.default.removeObserver(changeObserver)
        }
    }
    
    private func startAnimation() {
        guard !animationFrames.isEmpty else { return }
        timerManager.startTimer { [weak self] in
            self?.updateIcon()
        }
    }
    
    private func stopAnimation() {
        timerManager.stopTimer()
    }
    
    private func updateIcon() {
        guard let button = statusItem.button else { return }
        frameIndex = (frameIndex + 1) % animationFrames.count
        button.image = animationFrames[frameIndex]
    }
    
    @objc private func toggleMenu(sender: AnyObject) {
        if hostingController == nil {
            setupPopover()
        }
        
        if popOver.isShown {
            popOver.performClose(sender)
        } else {
            showPopover()
        }
    }
    
    private func setupPopover() {
        hostingController = AutoResizingHostingController(
            rootView: MenuBarView(mainViewModel: viewModel),
            popover: popOver
        )
        
        if let hostingController = hostingController {
            hostingController.view.window?.level = .statusBar
            popOver.behavior = .transient
            popOver.animates = true
            popOver.contentViewController = hostingController
            popOver.contentSize = hostingController.view.fittingSize
        }
    }
    
    private func showPopover() {
        guard let button = statusItem.button else { return }
        popOver.show(relativeTo: button.bounds, of: button, preferredEdge: .minY)
        popOver.contentViewController?.view.window?.makeKey()
    }
    
    private func handleUsageUpdate(_ notification: Notification) {
        guard isRunning, !isUpdating,
              let usageData = notification.userInfo as? [String: Any],
              let totalUsage = usageData["total"] as? Double else { return }
        
        isUpdating = true
        defer { isUpdating = false }
        
        // 使用计算器计算帧间隔
        timeInterval = frameIntervalCalculator.calculateInterval(
            totalUsage: totalUsage,
            frameCount: animationFrames.count
        )
        
        timerManager.updateIntervalMilliseconds(to: timeInterval)
    }
    
    private func handleIconChange(_ notification: Notification) {
        guard let usageData = notification.userInfo as? [String: String],
              let value = usageData[Constants.Notification_ChangeFrameKey] else { return }
        
        UserDefaults.standard.setValue(value, forKey: Constants.useIconKey)
        restart()
    }
    
    deinit {
        removeObservers()
    }
}

class AutoResizingHostingController<Content: View>: NSHostingController<Content> {
    private var popOver: NSPopover?
    private var sizeObserver: NSKeyValueObservation?
    
    init(rootView: Content, popover: NSPopover) {
        popOver = popover
        super.init(rootView: rootView)
    }
    
    @available(*, unavailable)
    @objc dynamic required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
     
    override func viewDidLoad() {
        super.viewDidLoad()
    }
    
    override func viewDidAppear() {
        super.viewDidAppear()
        // 视图出现时，立即调整大小
        let fittingSize = view.fittingSize
        if let popOver = popOver {
            popOver.contentSize = fittingSize
//            preferredContentSize = fittingSize
        }
    }

    override func updateViewConstraints() {
        super.updateViewConstraints()
        let fittingSize = view.fittingSize
        if let popOver = popOver {
            popOver.contentSize = fittingSize
//            preferredContentSize = fittingSize
        }
    }

    deinit {
        sizeObserver?.invalidate()
    }
}
