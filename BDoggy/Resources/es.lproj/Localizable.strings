"registered_runners" = "Runners Registrados:";
"runner_name" = "Nombre del Runner:";
"type_name" = "Escribir Nombre";
"preconditions" = "Precondiciones:";
"format_png" = "Formato: PNG";
"size_limit" = "Tamaño: < 30KB";
"height_limit" = "Altura: 10~100px";
"width_limit" = "Ancho: 10~100px";
"frame_limit" = "Hasta 30 cuadros";
"frames" = "Cuadros";
"preview" = "Vista Previa";
"register" = "Registrar";
"error" = "Error";
"ok" = "Aceptar";
"error_max_images" = "El número total de imágenes no puede exceder 30";
"error_format" = "Solo se admiten los formatos: %@";
"error_size" = "El tamaño de la imagen no puede exceder 30KB";
"error_height" = "La altura de la imagen debe estar entre 10-100 píxeles";
"error_width" = "El ancho de la imagen debe estar entre 10-100 píxeles";
"error_image_selection" = "Error en la selección de imagen: %@";
// MoreView
"back" = "Volver";
"runner_maker" = "Runner Maker";
"settings" = "Ajustes";
"about" = "Acerca de";
"quit_app" = "Salir de BDoggy";
// StoreView
"unpurchased_items" = "Artículos no Comprados";
"purchased_items" = "Artículos Comprados";
"downloaded_items" = "Descargados";
// StoreItemView
"download" = "Descargar";
"downloaded" = "Descargado";
"purchase" = "Comprar";
// SettingView
"settings_launch" = "Inicio";
"settings_launch_description" = "Iniciar BDoggy automáticamente al iniciar sesión.";
"settings_general" = "General";
"settings_language" = "Idioma";
"settings_language_description" = "Elija su idioma preferido";
"settings_restart_title" = "Idioma Cambiado";
"settings_restart_message" = "La configuración del idioma se aplicará después de reiniciar la aplicación.";
"settings_restart_now" = "Reiniciar Ahora";
"settings_restart_later" = "Más Tarde";
"settings_cancel" = "Cancelar";
// AboutView
"support_page" = "Página de Soporte";
"copyright" = "© 2024-2028 Knight4 LIU";
//Windows
"runner_maker_title" = "Runner Maker";
"runner_store_title" = "Runner Store";
// 通用
"back" = "Volver";
"loading" = "Cargando...";
"error" = "Error";
"no_data_available" = "No hay datos disponibles";
"hello_world" = "¡Hola, mundo!";
// Runner 相关
"runner_description" = "Animación de Runner personalizada";
// 商店相关
"no_description" = "Sin descripción";
"restore_purchased" = "Restaurar compras";
"restore" = "Restaurar";
"available" = "Disponible";
"unavailable" = "No disponible";
"purchase_tip" = "Consejo de Compra";
"purchase_maker_permission" = "Por favor, vaya a Runner Store para comprar el permiso de RunnerMaker";
"go_to_store" = "Ir a Store";
"permission_granted" = "Permiso concedido";
"purchase_permission_hint" = "Por favor, vaya a Runner Store para comprar el permiso de RunnerMaker";
"Memory Pressure" = "Presión de memoria";
"Low" = "Baja";
"Medium" = "Media";
"High" = "Alta";
"Yes" = "Sí";
"No" = "No";
"Enjoy Your Doggy" = "¡Disfruta tu perrito!～";
// RunnerMaker
"created_by" = "Creado por Knight4 LIU";
"no_frames_to_save" = "No hay marcos para guardar";
// 新增的本地化字符串
"invalid_runner_name" = "Nombre de Runner no válido";
"resource_not_found" = "Recurso no encontrado";
"please_select_runner" = "Por favor, seleccione un Runner primero";
"failed_create_directory" = "Error al crear el directorio";
"failed_copy_file" = "Error al copiar el archivo";
"unknown_error" = "Error desconocido";
"gif_creation_failed" = "Error al crear GIF";
"delete_old_files_failed" = "Error al eliminar archivos antiguos";
"cannot_delete_runner_when_used" = "No se puede eliminar este Runner mientras está en uso";

// Contributors related
"contributors" = "Colaboradores";
"contributors_title" = "Colaboradores de BDoggy";
"contributors_description" = "Gracias a las siguientes personas que contribuyeron al proyecto BDoggy";
"thank_contributors" = "Gracias a todos los colaboradores por su dedicación y apoyo.";
//relacionado con la ayuda
"help_activity_monitor" = "Abrir Monitor de Actividad";
"help_runner" = "Abrir Lista de Runners";
"help_runner_store" = "Abrir Runner Store";
"help_runner_maker" = "Abrir Runner Maker";
"help_gif_splitter" = "Divisor de GIF";
"help_more" = "Más";
"help_dev" = "En Desarrollo...";
"feature_developing_title" = "Función en Desarrollo";
"feature_developing_message" = "Esta función está en desarrollo, por favor manténgase atento.";

//Gif splitter
"gif_splitter" = "Divisor de GIF";

// GIF Splitter View
"gif_splitter_loading" = "Cargando...";
"gif_splitter_select_gif" = "Seleccione un archivo GIF para comenzar";
"gif_splitter_frames_count" = "Total %d cuadros";
"gif_splitter_frame_interval" = "Intervalo de cuadros %.1f seg";
"gif_splitter_preview_control" = "Control de velocidad de vista previa";
"gif_splitter_save_settings" = "Configuración de guardado";
"gif_splitter_folder_name" = "Nombre de carpeta:";
"gif_splitter_folder_name_placeholder" = "Ingrese nombre de carpeta para guardar";
"gif_splitter_save_location" = "Ubicación de guardado:";
"gif_splitter_default_location" = "/Downloads";
"gif_splitter_save_frames" = "Guardar cuadros";
"gif_splitter_clear" = "Limpiar";
"gif_splitter_select_gif_file" = "Seleccionar archivo GIF";
"gif_splitter_reselect_gif_file" = "Volver a seleccionar archivo GIF";
"gif_splitter_alert_title" = "Aviso";
"gif_splitter_ok" = "Aceptar";

// GIF Splitter ViewModel
"gif_splitter_select_save_location" = "Seleccionar ubicación de guardado";
"gif_splitter_error_no_frames" = "No hay cuadros para guardar";
"gif_splitter_error_no_folder_name" = "Por favor ingrese un nombre de carpeta";
"gif_splitter_save_success" = "Cuadros guardados exitosamente en: %@";
"gif_splitter_save_error" = "Error al guardar cuadros: %@";
"gif_splitter_load_error" = "Error al cargar archivo GIF";
// GIF Composer
"gif_composer" = "Compositor de GIF";
"gif_composer_loading" = "Cargando...";
"gif_composer_select_images" = "Seleccione imágenes para comenzar";
"gif_composer_images_count" = "%d Imágenes";
"gif_composer_preview_control" = "Control de velocidad de vista previa";
"gif_composer_image_list" = "Lista de imágenes";
"gif_composer_frame_interval" = "Intervalo de frames";
"gif_composer_save_settings" = "Configuración de guardado";
"gif_composer_file_name" = "Nombre del archivo:";
"gif_composer_file_name_placeholder" = "Ingrese el nombre del archivo para guardar";
"gif_composer_save_location" = "Ubicación de guardado:";
"gif_composer_default_location" = "/Downloads";
"gif_composer_create_gif" = "Crear GIF";
"gif_composer_clear" = "Limpiar";
"gif_composer_select_images_button" = "Seleccionar imágenes";
"gif_composer_alert_title" = "Alerta";
"gif_composer_ok" = "Aceptar";
"gif_composer_error_no_images" = "No hay imágenes para componer";
"gif_composer_error_no_file_name" = "Por favor ingrese un nombre de archivo";
"gif_composer_error_invalid_interval" = "El intervalo de frames debe estar entre 30ms y 1000ms";
"gif_composer_select_save_location" = "Seleccionar ubicación de guardado";
"gif_composer_save_success" = "GIF guardado en: %@";
"gif_composer_save_error" = "Error al crear GIF";
"gif_composer_preview_playing" = "Previsualizando";
"gif_composer_preview_paused" = "Vista previa pausada";
"gif_composer_preview_start" = "Iniciar vista previa";
"gif_composer_preview_stop" = "Detener vista previa";
"gif_composer_preview_tip" = "No se pueden reordenar o eliminar imágenes durante la vista previa. Por favor detenga la vista previa primero";
"gif_composer_drag_to_reorder" = "Arrastrar para reordenar";
"gif_composer_frame_interval_ms" = "Intervalo de frames %d ms";
"gif_composer_advanced_settings" = "Configuración avanzada";
"gif_composer_loop_count" = "Número de repeticiones";
"gif_composer_loop_infinite" = "Infinito";
"gif_composer_loop_times" = "%d veces";
"gif_composer_color_depth" = "Profundidad de color";
"gif_composer_global_color_map" = "Usar mapa de color global";
"gif_composer_default_location_tips" = "Guardar por defecto en la carpeta de Descargas";
