"registered_runners" = "已注册的Runners:";
"runner_name" = "Runner名称:";
"type_name" = "输入名称";
"preconditions" = "条件：";
"format_png" = "格式：PNG";
"size_limit" = "大小：< 30KB";
"height_limit" = "高度：10~100px";
"width_limit" = "宽度：10~100px";
"frame_limit" = "最多30帧";
"frames" = "帧图片";
"preview" = "预览";
"register" = "注册";
"error" = "错误";
"ok" = "确定";
"error_max_images" = "总共最多只能导入30张图片";
"error_format" = "只支持以下格式的图片：%@";
"error_size" = "图片大小不能超过30KB";
"error_height" = "图片高度必须在10-100像素之间";
"error_width" = "图片宽度必须在10-100像素之间";
"error_image_selection" = "图片选择错误：%@";
// MoreView
"back" = "返回";
"runner_maker" = "图标制作器";
"settings" = "设置";
"about" = "关于";
"quit_app" = "退出BDoggy";
// StoreView
"unpurchased_items" = "未购买项目";
"purchased_items" = "已购买项目";
"downloaded_items" = "已下载项目";
// StoreItemView
"download" = "下载";
"downloaded" = "已下载";
"purchase" = "购买";
// SettingView
"settings_launch" = "启动";
"settings_launch_description" = "开机时自动启动BDoggy。";
"settings_general" = "通用";
"settings_language" = "语言";
"settings_language_description" = "选择您偏好的语言";
"settings_restart_title" = "语言已更改";
"settings_restart_message" = "语言设置将在重启应用后生效。";
"settings_restart_now" = "立即重启";
"settings_restart_later" = "稍后";
"settings_cancel" = "取消";
// AboutView
"support_page" = "获取帮助";
"copyright" = "© 2024-2028 Knight4 LIU";
//Windows
"runner_maker_title" = "图标制作器";
"runner_store_title" = "商店";
// 通用
"back" = "返回";
"loading" = "加载中...";
"error" = "错误";
"no_data_available" = "没有可用数据";
"hello_world" = "你好，世界！";
// Runner 相关
"runner_description" = "自定义跑者动画";
// 商店相关
"no_description" = "无描述";
"restore_purchased" = "恢复购买";
"restore" = "恢复";
"available" = "可用";
"unavailable" = "不可用";
"purchase_tip" = "购买提示";
"purchase_maker_permission" = "请前往Runner Store购买RunnerMaker的权限";
"go_to_store" = "前往商店";
"permission_granted" = "已获得制作权限";
"purchase_permission_hint" = "请前往Runner Store购买RunnerMaker的权限";
"Memory Pressure" = "内存压力";
"Low" = "低";
"Medium" = "中";
"High" = "高";
"Yes" = "是";
"No" = "否";
"Enjoy Your Doggy" = "祝您狗子开心！～";
// RunnerMaker
"created_by" = "Created by Knight4 LIU";
"no_frames_to_save" = "没有可保存的帧";
// 新增的本地化字符串
"invalid_runner_name" = "无效的跑者名称";
"resource_not_found" = "找不到要删除的资源";
"delete_failed" = "删除失败";
"failed_create_directory" = "创建目录失败";
"failed_copy_file" = "复制文件失败";
"unknown_error" = "未知错误";
"gif_creation_failed" = "GIF创建失败";
"delete_old_files_failed" = "删除旧文件失败";
"please_select_runner" = "请先选择要删除的跑者";
"cannot_delete_runner_when_used" = "无法删除正在使用中的跑者";
// 应用更新相关
"app_update_available" = "有可用更新";
"app_update_message" = "BDoggy 有新版本 (%@) 可用。您想现在更新吗？";
"update" = "更新";
"cancel" = "取消";
// 贡献者相关
"contributors" = "贡献者";
"contributors_title" = "BDoggy 贡献者";
"contributors_description" = "感谢以下人员为 BDoggy 项目做出的贡献";
"thank_contributors" = "感谢所有贡献者的付出与支持!~";
//Gif splitter
"gif_splitter" = "GIF拆帧工具";

// GIF Splitter
"gif_splitter_loading" = "正在加载...";
"gif_splitter_select_gif" = "选择GIF文件开始";
"gif_splitter_frames_count" = "共 %d 帧";
"gif_splitter_frame_interval" = "帧间隔 %.1f秒";
"gif_splitter_preview_control" = "预览速度控制";
"gif_splitter_save_settings" = "保存设置";
"gif_splitter_folder_name" = "文件夹名称:";
"gif_splitter_folder_name_placeholder" = "输入保存的文件夹名称";
"gif_splitter_save_location" = "保存位置:";
"gif_splitter_default_location" = "/Downloads";
"gif_splitter_save_frames" = "保存拆分帧";
"gif_splitter_clear" = "清空";
"gif_splitter_reselect_gif_file" = "重新选择GIF文件";
"gif_splitter_select_gif_file" = "选择GIF文件";
"gif_splitter_alert_title" = "提示";
"gif_splitter_ok" = "确定";
"gif_splitter_load_error" = "无法加载GIF文件";
"gif_splitter_error_no_frames" = "没有可保存的帧";
"gif_splitter_error_no_folder_name" = "请输入保存文件夹名称";
"gif_splitter_select_save_location" = "选择保存位置";
"gif_splitter_save_success" = "帧已保存到: %@";
"gif_splitter_save_error" = "保存帧失败: %@";

// GIF Composer
"gif_composer" = "GIF合成工具";
"gif_composer_loading" = "正在加载...";
"gif_composer_select_images" = "选择图片开始";
"gif_composer_images_count" = "共 %d 张图片";
"gif_composer_preview_control" = "预览速度控制";
"gif_composer_image_list" = "图片列表";
"gif_composer_frame_interval" = "帧间隔时长";
"gif_composer_save_settings" = "保存设置";
"gif_composer_file_name" = "文件名称:";
"gif_composer_file_name_placeholder" = "输入保存的文件名称";
"gif_composer_save_location" = "保存位置:";
"gif_composer_default_location" = "/Downloads";
"gif_composer_create_gif" = "创建GIF";
"gif_composer_clear" = "清空";
"gif_composer_select_images_button" = "选择图片";
"gif_composer_alert_title" = "提示";
"gif_composer_ok" = "确定";
"gif_composer_error_no_images" = "没有可合成的图片";
"gif_composer_error_no_file_name" = "请输入保存文件名称";
"gif_composer_error_invalid_interval" = "帧间隔必须在30毫秒到1000毫秒之间";
"gif_composer_select_save_location" = "选择保存位置";
"gif_composer_save_success" = "GIF已保存到: %@";
"gif_composer_save_error" = "创建GIF失败";
"gif_composer_preview_playing" = "预览中";
"gif_composer_preview_paused" = "预览已暂停";
"gif_composer_preview_start" = "开始预览";
"gif_composer_preview_stop" = "停止预览";
"gif_composer_preview_tip" = "预览中无法排序或删除图片，请先停止预览";
"gif_composer_drag_to_reorder" = "拖拽可重新排序";
"gif_composer_default_location_tips" = "預設儲存至「下載」資料夾";
