"registered_runners" = "登録済みのRunners:";
"runner_name" = "Runner名:";
"type_name" = "名前を入力";
"preconditions" = "前提条件：";
"format_png" = "フォーマット：PNG";
"size_limit" = "サイズ：< 30KB";
"height_limit" = "高さ：10~100px";
"width_limit" = "幅：10~100px";
"frame_limit" = "最大30フレーム";
"frames" = "フレーム";
"preview" = "プレビュー";
"register" = "登録";
"error" = "エラー";
"ok" = "OK";
"error_max_images" = "画像は合計30枚まで追加できます";
"error_format" = "対応フォーマット：%@";
"error_size" = "画像サイズは30KB以下にしてください";
"error_height" = "画像の高さは10〜100ピクセルの範囲内にしてください";
"error_width" = "画像の幅は10〜100ピクセルの範囲内にしてください";
"error_image_selection" = "画像選択エラー：%@";
// MoreView
"back" = "戻る";
"runner_maker" = "Runner Maker";
"settings" = "設定";
"about" = "について";
"quit_app" = "BDoggy を終了";
// StoreView
"unpurchased_items" = "未購入アイテム";
"purchased_items" = "購入済みアイテム";
"downloaded_items" = "ダウンロード済み";
// StoreItemView
"download" = "ダウンロード";
"downloaded" = "ダウンロード済み";
"purchase" = "購入";
// SettingView
"settings_launch" = "起動";
"settings_launch_description" = "ログイン時に BDoggy を自動的に起動します。";
"settings_general" = "一般";
"settings_language" = "言語";
"settings_language_description" = "希望する言語を選択してください";
"settings_restart_title" = "言語が変更されました";
"settings_restart_message" = "言語設定を反映するにはアプリを再起動する必要があります。";
"settings_restart_now" = "今すぐ再起動";
"settings_restart_later" = "後で";
"settings_cancel" = "キャンセル";
// AboutView
"support_page" = "サポートページ";
"copyright" = "© 2024-2028 Knight4 LIU";
//Windows
"runner_maker_title" = "Runner Maker";
"runner_store_title" = "Runner Store";
// 通用
"back" = "戻る";
"loading" = "読み込み中...";
"error" = "エラー";
"no_data_available" = "データがありません";
"hello_world" = "こんにちは、世界！";
// Runner 相关
"runner_description" = "カスタムRunnerアニメーション";
// 商店相关
"no_description" = "説明なし";
"restore_purchased" = "購入を復元";
"restore" = "復元";
"available" = "利用可能";
"unavailable" = "利用不可";
"purchase_tip" = "購入ヒント";
"purchase_maker_permission" = "Runner StoreでRunnerMakerの権限を購入してください";
"go_to_store" = "Storeに行く";
"permission_granted" = "権限が付与されました";
"purchase_permission_hint" = "Runner StoreでRunnerMakerの権限を購入してください";
"Memory Pressure" = "メモリ負荷";
"Low" = "低";
"Medium" = "中";
"High" = "高";
"Yes" = "はい";
"No" = "いいえ";
"Enjoy Your Doggy" = "Enjoy Your Doggy！～";
// RunnerMaker
"created_by" = "Created by Knight4 LIU";
"no_frames_to_save" = "保存するフレームがありません";
// 新增的本地化字符串
"invalid_runner_name" = "無効なRunner名";
"resource_not_found" = "リソースが見つかりません";
"please_select_runner" = "Runnerを先に選択してください";
"delete_failed" = "削除に失敗しました";
"failed_create_directory" = "ディレクトリの作成に失敗しました";
"failed_copy_file" = "ファイルのコピーに失敗しました";
"unknown_error" = "不明なエラー";
"gif_creation_failed" = "GIFの作成に失敗しました";
"delete_old_files_failed" = "古いファイルの削除に失敗しました";
"cannot_delete_runner_when_used" = "使用中のRunnerは削除できません";
// 应用更新相关
"app_update_available" = "アップデートが利用可能";
"app_update_message" = "BDoggyの新しいバージョン（%@）が利用可能です。今すぐアップデートしますか？";
"update" = "アップデート";
"cancel" = "キャンセル";
// Contributors related
"contributors" = "貢献者";
"contributors_title" = "BDoggy 貢献者";
"contributors_description" = "BDoggyプロジェクトに貢献してくださった以下の方々に感謝します";
"thank_contributors" = "すべての貢献者の献身と支援に感謝します。";
//help related
"help_activity_monitor" = "アクティビティモニタを開く";
"help_runner" = "Runnerリストを開く";
"help_runner_store" = "Storeを開く";
"help_runner_maker" = "Runner Makerを開く";
"help_gif_splitter" = "GIF分割ツール";
"help_more" = "その他";
"help_dev" = "開発中...";
"feature_developing_title" = "機能開発中";
"feature_developing_message" = "この機能は現在開発中です。しばらくお待ちください。";
//Gif splitter
"gif_splitter" = "GIF分割ツール";

// GIF Splitter View
"gif_splitter_loading" = "読み込み中...";
"gif_splitter_select_gif" = "GIFファイルを選択して開始";
"gif_splitter_frames_count" = "合計 %d フレーム";
"gif_splitter_frame_interval" = "フレーム間隔 %.1f 秒";
"gif_splitter_preview_control" = "プレビュー速度コントロール";
"gif_splitter_save_settings" = "保存設定";
"gif_splitter_folder_name" = "フォルダ名:";
"gif_splitter_folder_name_placeholder" = "保存するフォルダ名を入力";
"gif_splitter_save_location" = "保存場所:";
"gif_splitter_default_location" = "/Downloads";
"gif_splitter_save_frames" = "フレームを保存";
"gif_splitter_clear" = "クリア";
"gif_splitter_select_gif_file" = "GIFファイルを選択";
"gif_splitter_reselect_gif_file" = "GIFファイルを再選択";
"gif_splitter_alert_title" = "お知らせ";
"gif_splitter_ok" = "OK";

// GIF Splitter ViewModel
"gif_splitter_select_save_location" = "保存場所を選択";
"gif_splitter_error_no_frames" = "保存するフレームがありません";
"gif_splitter_error_no_folder_name" = "フォルダ名を入力してください";
"gif_splitter_save_success" = "フレームが正常に保存されました: %@";
"gif_splitter_save_error" = "フレーム保存エラー: %@";
"gif_splitter_load_error" = "GIFファイルの読み込みに失敗しました";
// GIF Composer
"gif_composer" = "GIF作成ツール";
"gif_composer_loading" = "読み込み中...";
"gif_composer_select_images" = "画像を選択して開始";
"gif_composer_images_count" = "合計 %d 枚";
"gif_composer_preview_control" = "プレビュー速度コントロール";
"gif_composer_image_list" = "画像リスト";
"gif_composer_frame_interval" = "フレーム間隔";
"gif_composer_frame_interval_ms" = "フレーム間隔 %d ミリ秒";
"gif_composer_advanced_settings" = "詳細設定";
"gif_composer_loop_count" = "ループ回数";
"gif_composer_loop_infinite" = "無限";
"gif_composer_loop_times" = "%d 回";
"gif_composer_color_depth" = "色深度";
"gif_composer_global_color_map" = "グローバルカラーマップを使用";
"gif_composer_save_settings" = "保存設定";
"gif_composer_file_name" = "ファイル名:";
"gif_composer_file_name_placeholder" = "保存するファイル名を入力";
"gif_composer_save_location" = "保存場所:";
"gif_composer_default_location" = "/Downloads";
"gif_composer_create_gif" = "GIFを作成";
"gif_composer_clear" = "クリア";
"gif_composer_select_images_button" = "画像を選択";
"gif_composer_alert_title" = "お知らせ";
"gif_composer_ok" = "OK";
"gif_composer_error_no_images" = "合成する画像がありません";
"gif_composer_error_no_file_name" = "ファイル名を入力してください";
"gif_composer_error_invalid_interval" = "フレーム間隔は30ミリ秒から1000ミリ秒の間でなければなりません";
"gif_composer_select_save_location" = "保存場所を選択";
"gif_composer_save_success" = "GIFが保存されました: %@";
"gif_composer_save_error" = "GIFの作成に失敗しました";
"gif_composer_preview_playing" = "プレビュー中";
"gif_composer_preview_paused" = "プレビュー一時停止";
"gif_composer_preview_start" = "プレビュー開始";
"gif_composer_preview_stop" = "プレビュー停止";
"gif_composer_preview_tip" = "プレビュー中は画像の並べ替えや削除ができません。先にプレビューを停止してください";
"gif_composer_drag_to_reorder" = "ドラッグして並べ替え";
"gif_composer_default_location_tips" = "デフォルトで「ダウンロード」フォルダに保存";
