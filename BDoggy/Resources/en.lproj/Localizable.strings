"registered_runners" = "Registered Runners:";
"runner_name" = "Runner Name:";
"type_name" = "Type Name";
"preconditions" = "Preconditions:";
"format_png" = "Format: PNG";
"size_limit" = "Size: < 30KB";
"height_limit" = "Height: 10~100px";
"width_limit" = "Width: 10~100px";
"frame_limit" = "Up to 30 frames";
"frames" = "Frames";
"preview" = "Preview";
"register" = "Register";
"error" = "Error";
"ok" = "OK";
"error_max_images" = "Total number of images cannot exceed 30";
"error_format" = "Only support formats: %@";
"error_size" = "Image size cannot exceed 30KB";
"error_height" = "Image height must be between 10-100 pixels";
"error_width" = "Image width must be between 10-100 pixels";
"error_image_selection" = "Image selection error: %@";
// MoreView
"back" = "Back";
"runner_maker" = "Runner Maker";
"settings" = "Settings";
"about" = "About";
"quit_app" = "Quit BDoggy";
// StoreView
"unpurchased_items" = "Unpurchased Items";
"purchased_items" = "Purchased Items";
"downloaded_items" = "Downloaded Items";
// StoreItemView
"download" = "Download";
"downloaded" = "Downloaded";
"purchase" = "Purchase";
// SettingView
"settings_launch" = "Launch";
"settings_launch_description" = "Launch BDoggy automatically at login.";
"settings_general" = "General";
"settings_language" = "Language";
"settings_language_description" = "Choose your preferred language";
"settings_restart_title" = "Language Changed";
"settings_restart_message" = "The language setting will take effect after restarting the application.";
"settings_restart_now" = "Restart Now";
"settings_restart_later" = "Later";
"settings_cancel" = "Cancel";
// AboutView
"support_page" = "Support Page";
"copyright" = "© 2024-2028 Knight4 LIU";
//Windows
"runner_maker_title" = "Runner Maker";
"runner_store_title" = "Runner Store";
// 通用
"back" = "Back";
"loading" = "Loading...";
"error" = "Error";
"no_data_available" = "No data available.";
"hello_world" = "Hello, world!";
// Runner 相关
"runner_description" = "Custom Runner animation";
// 商店相关
"no_description" = "No description";
"restore_purchased" = "Restore Purchased";
"restore" = "Restore";
"available" = "Available";
"unavailable" = "Unavailable";
"purchase_tip" = "Purchase Tip";
"purchase_maker_permission" = "Please go to Runner Store to purchase RunnerMaker permission";
"go_to_store" = "Go to Store";
"permission_granted" = "Permission granted";
"purchase_permission_hint" = "Please go to Runner Store to purchase RunnerMaker permission";
"Memory Pressure" = "Pressure";
"Low" = "Low";
"Medium" = "Medium";
"High" = "High";
"Yes" = "Yes";
"No" = "No";
"Enjoy Your Doggy" = "Enjoy Your Doggy!～";
// RunnerMaker
"created_by" = "Created by Knight4 LIU";
"no_frames_to_save" = "No frames to save";
// 新增的本地化字符串
"invalid_runner_name" = "Invalid Runner name";
"resource_not_found" = "Resource not found";
"delete_failed" = "Delete failed";
"failed_create_directory" = "Failed to create directory";
"failed_copy_file" = "Failed to copy file";
"unknown_error" = "Unknown error";
"gif_creation_failed" = "Failed to create GIF";
"delete_old_files_failed" = "Failed to delete old files";
"please_select_runner" = "Please select a Runner first";
"cannot_delete_runner_when_used" = "Cannot delete this Runner when it is using";
// 应用更新相关
"app_update_available" = "Update Available";
"app_update_message" = "A new version (%@) of BDoggy is available. Would you like to update now?";
"update" = "Update";
"cancel" = "Cancel";
// Contributors related
"contributors" = "Contributors";
"contributors_title" = "BDoggy Contributors";
"contributors_description" = "Thanks to the following people who contributed to the BDoggy project";
"thank_contributors" = "Thanks to all contributors for their dedication and support.";
//help related
"help_activity_monitor" = "Open Activity Monitor";
"help_runner" = "Open Runner List";
"help_runner_store" = "Open Store";
"help_runner_maker" = "Open Runner Maker";
"help_gif_splitter" = "GIF Splitter";
"help_more" = "More";
"help_dev" = "Under Development...";
"feature_developing_title" = "Feature Developing";
"feature_developing_message" = "This feature is under development, please stay tuned.";
//Gif splitter
"gif_splitter" = "GIF Splitter";

// GIF Splitter View
"gif_splitter_loading" = "Loading...";
"gif_splitter_select_gif" = "Select GIF file to start";
"gif_splitter_frames_count" = "Total %d frames";
"gif_splitter_frame_interval" = "Frame interval %.1f sec";
"gif_splitter_preview_control" = "Preview Speed Control";
"gif_splitter_save_settings" = "Save Settings";
"gif_splitter_folder_name" = "Folder Name:";
"gif_splitter_folder_name_placeholder" = "Enter folder name to save";
"gif_splitter_save_location" = "Save Location:";
"gif_splitter_default_location" = "/Downloads";
"gif_splitter_save_frames" = "Save Frames";
"gif_splitter_clear" = "Clear";
"gif_splitter_select_gif_file" = "Select GIF File";
"gif_splitter_reselect_gif_file" = "Reselect GIF File";
"gif_splitter_alert_title" = "Message";
"gif_splitter_ok" = "OK";
"gif_splitter_load_error" = "Failed to load GIF file";
"gif_splitter_error_no_frames" = "No frames to save";
"gif_splitter_error_no_folder_name" = "Please enter a folder name";
"gif_splitter_select_save_location" = "Select Save Location";
"gif_splitter_save_success" = "Frames saved to %@";
"gif_splitter_save_error" = "Failed to save frames: %@";

// GIF Composer
"gif_composer" = "GIF Composer";
"gif_composer_loading" = "Loading...";
"gif_composer_select_images" = "Select images to start";
"gif_composer_images_count" = "%d Images";
"gif_composer_preview_control" = "Preview Speed Control";
"gif_composer_image_list" = "Image List";
"gif_composer_frame_interval" = "Frame Interval";
"gif_composer_frame_interval_ms" = "Frame Interval %d ms";
"gif_composer_save_settings" = "Save Settings";
"gif_composer_file_name" = "File Name:";
"gif_composer_file_name_placeholder" = "Enter file name to save";
"gif_composer_save_location" = "Save Location:";
"gif_composer_default_location" = "/Downloads";
"gif_composer_create_gif" = "Create GIF";
"gif_composer_clear" = "Clear";
"gif_composer_select_images_button" = "Select Images";
"gif_composer_alert_title" = "Alert";
"gif_composer_ok" = "OK";
"gif_composer_error_no_images" = "No images to compose";
"gif_composer_error_no_file_name" = "Please enter a file name";
"gif_composer_error_invalid_interval" = "Frame interval must be between 30ms and 1000ms";
"gif_composer_select_save_location" = "Select Save Location";
"gif_composer_save_success" = "GIF saved to: %@";
"gif_composer_save_error" = "Failed to create GIF";
"gif_composer_preview_playing" = "Previewing";
"gif_composer_preview_paused" = "Preview paused";
"gif_composer_preview_start" = "Start Preview";
"gif_composer_preview_stop" = "Stop Preview";
"gif_composer_preview_tip" = "Cannot reorder or delete images during preview. Please stop preview first";
"gif_composer_drag_to_reorder" = "Drag to reorder";
"gif_composer_advanced_settings" = "Advanced Settings";
"gif_composer_loop_count" = "Loop Count";
"gif_composer_loop_infinite" = "Infinite";
"gif_composer_loop_times" = "Loop %d times";
"gif_composer_color_depth" = "Color Depth";
"gif_composer_default_location_tips" = "Default save into Downloads folder";
