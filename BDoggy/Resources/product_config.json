{"products": {"com.doggy.BDoggy.nonConsumable.RunnerMaker": {"downloadUrls": [], "displayIcon": "copywriting.gif", "groupName": "RunnerMaker", "productType": "service", "publisher": "system", "order": 0}, "com.doggy.BDoggy.nonConsumable.Alika": {"productType": "runner", "displayIcon": "Alika.gif", "publisher": "system", "order": 4, "downloadUrls": ["Alika.gif", "Alika0.png", "Alika1.png", "Alika10.png", "Alika11.png", "Alika12.png", "Alika13.png", "Alika14.png", "Alika15.png", "Alika2.png", "Alika3.png", "Alika4.png", "Alika5.png", "Alika6.png", "Alika7.png", "Alika8.png", "Alika9.png"], "groupName": "Ali<PERSON>"}, "com.doggy.BDoggy.nonConsumable.Bombay": {"downloadUrls": ["Bombay.gif", "Bombay0.png", "Bombay1.png", "Bombay10.png", "Bombay11.png", "Bombay12.png", "Bombay13.png", "Bombay14.png", "Bombay15.png", "Bombay2.png", "Bombay3.png", "Bombay4.png", "Bombay5.png", "Bombay6.png", "Bombay7.png", "Bombay8.png", "Bombay9.png"], "displayIcon": "Bombay.gif", "publisher": "system", "productType": "runner", "order": 2, "groupName": "Bombay"}, "com.doggy.BDoggy.nonConsumable.PinkStar": {"productType": "runner", "order": 3, "displayIcon": "PinkStar.gif", "downloadUrls": ["PinkStar.gif", "PinkStar0.png", "PinkStar1.png", "PinkStar10.png", "PinkStar11.png", "PinkStar12.png", "PinkStar13.png", "PinkStar14.png", "PinkStar15.png", "PinkStar2.png", "PinkStar3.png", "PinkStar4.png", "PinkStar5.png", "PinkStar6.png", "PinkStar7.png", "PinkStar8.png", "PinkStar9.png"], "publisher": "system", "groupName": "PinkStar"}, "com.doggy.BDoggy.nonConsumable.King": {"downloadUrls": ["King.gif", "King0.png", "King1.png", "King10.png", "King11.png", "King12.png", "King13.png", "King14.png", "King15.png", "King2.png", "King3.png", "King4.png", "King5.png", "King6.png", "King7.png", "King8.png", "King9.png"], "productType": "runner", "groupName": "King", "displayIcon": "King.gif", "publisher": "system", "order": 4}, "com.doggy.BDoggy.nonConsumable.JungleHero": {"productType": "runner", "order": 4, "displayIcon": "JungleHero.gif", "publisher": "system", "groupName": "JungleHero", "downloadUrls": ["JungleHero.gif", "JungleHero0.png", "JungleHero1.png", "JungleHero10.png", "JungleHero11.png", "JungleHero12.png", "JungleHero13.png", "JungleHero14.png", "JungleHero15.png", "JungleHero2.png", "JungleHero3.png", "JungleHero4.png", "JungleHero5.png", "JungleHero6.png", "JungleHero7.png", "JungleHero8.png", "JungleHero9.png"]}, "com.doggy.BDoggy.nonConsumable.WhiteBoar": {"order": 3, "productType": "runner", "displayIcon": "WhiteBoar.gif", "downloadUrls": ["WhiteBoar.gif", "WhiteBoar0.png", "WhiteBoar1.png", "WhiteBoar10.png", "WhiteBoar11.png", "WhiteBoar12.png", "WhiteBoar13.png", "WhiteBoar14.png", "WhiteBoar15.png", "WhiteBoar16.png", "WhiteBoar17.png", "WhiteBoar2.png", "WhiteBoar3.png", "WhiteBoar4.png", "WhiteBoar5.png", "WhiteBoar6.png", "WhiteBoar7.png", "WhiteBoar8.png", "WhiteBoar9.png"], "publisher": "system", "groupName": "WhiteBoar"}, "com.doggy.BDoggy.nonConsumable.Snail": {"downloadUrls": ["Snail.gif", "Snail0.png", "Snail1.png", "Snail10.png", "Snail11.png", "Snail12.png", "Snail13.png", "Snail14.png", "Snail15.png", "Snail2.png", "Snail3.png", "Snail4.png", "Snail5.png", "Snail6.png", "Snail7.png", "Snail8.png", "Snail9.png"], "publisher": "system", "order": 3, "displayIcon": "Snail.gif", "groupName": "Snail", "productType": "runner"}, "com.doggy.BDoggy.nonConsumable.DancingGuy": {"displayIcon": "DancingGuy.gif", "groupName": "<PERSON><PERSON><PERSON>", "order": 7, "publisher": "system", "productType": "runner", "downloadUrls": ["DancingGuy.gif", "DancingGuy0.png", "DancingGuy1.png", "DancingGuy10.png", "DancingGuy11.png", "DancingGuy12.png", "DancingGuy2.png", "DancingGuy3.png", "DancingGuy4.png", "DancingGuy5.png", "DancingGuy6.png", "DancingGuy7.png", "DancingGuy8.png", "DancingGuy9.png"]}, "com.doggy.BDoggy.nonConsumable.Fox": {"downloadUrls": ["Fox.gif", "Fox0.png", "Fox1.png", "Fox10.png", "Fox11.png", "Fox12.png", "Fox13.png", "Fox14.png", "Fox15.png", "Fox16.png", "Fox17.png", "Fox2.png", "Fox3.png", "Fox4.png", "Fox5.png", "Fox6.png", "Fox7.png", "Fox8.png", "Fox9.png"], "displayIcon": "Fox.gif", "publisher": "system", "groupName": "Fox", "productType": "runner", "order": 1}, "com.doggy.BDoggy.nonConsumable.KongFuGrandpa": {"downloadUrls": ["KongFuGrandpa.gif", "KongFuGrandpa0.png", "KongFuGrandpa1.png", "KongFuGrandpa10.png", "KongFuGrandpa11.png", "KongFuGrandpa12.png", "KongFuGrandpa13.png", "KongFuGrandpa14.png", "KongFuGrandpa15.png", "KongFuGrandpa2.png", "KongFuGrandpa3.png", "KongFuGrandpa4.png", "KongFuGrandpa5.png", "KongFuGrandpa6.png", "KongFuGrandpa7.png", "KongFuGrandpa8.png", "KongFuGrandpa9.png"], "displayIcon": "KongFuGrandpa.gif", "publisher": "system", "groupName": "KongFuGrandpa", "productType": "runner", "order": 4}, "com.doggy.BDoggy.nonConsumable.Warrior": {"downloadUrls": ["Warrior.gif", "Warrior0.png", "Warrior1.png", "Warrior10.png", "Warrior11.png", "Warrior12.png", "Warrior13.png", "Warrior14.png", "Warrior15.png", "Warrior2.png", "Warrior3.png", "Warrior4.png", "Warrior5.png", "Warrior6.png", "Warrior7.png", "Warrior8.png", "Warrior9.png"], "groupName": "Warrior", "displayIcon": "Warrior.gif", "productType": "runner", "publisher": "system", "order": 6}, "com.doggy.BDoggy.nonConsumable.British": {"groupName": "British", "productType": "runner", "downloadUrls": ["British.gif", "British0.png", "British1.png", "British10.png", "British11.png", "British12.png", "British13.png", "British14.png", "British15.png", "British2.png", "British3.png", "British4.png", "British5.png", "British6.png", "British7.png", "British8.png", "British9.png"], "displayIcon": "British.gif", "order": 2, "publisher": "system"}, "com.doggy.BDoggy.nonConsumable.BlackWoman": {"groupName": "BlackWoman", "productType": "runner", "downloadUrls": ["BlackWoman.gif", "BlackWoman0.png", "BlackWoman1.png", "BlackWoman10.png", "BlackWoman11.png", "BlackWoman12.png", "BlackWoman13.png", "BlackWoman14.png", "BlackWoman15.png", "BlackWoman2.png", "BlackWoman3.png", "BlackWoman4.png", "BlackWoman5.png", "BlackWoman6.png", "BlackWoman7.png", "BlackWoman8.png", "BlackWoman9.png"], "publisher": "system", "order": 6, "displayIcon": "BlackWoman.gif"}}, "system": [{"productType": "runner", "downloadUrls": ["Wizzard.gif", "Wizzard0.png", "Wizzard1.png", "Wizzard10.png", "Wizzard11.png", "Wizzard12.png", "Wizzard13.png", "Wizzard14.png", "Wizzard15.png", "Wizzard2.png", "Wizzard3.png", "Wizzard4.png", "Wizzard5.png", "Wizzard6.png", "Wizzard7.png", "Wizzard8.png", "Wizzard9.png"], "order": 4, "publisher": "system", "groupName": "Wizzard", "displayIcon": "Wizzard.gif"}, {"downloadUrls": ["Captain.gif", "Captain0.png", "Captain1.png", "Captain10.png", "Captain11.png", "Captain12.png", "Captain13.png", "Captain14.png", "Captain15.png", "Captain16.png", "Captain17.png", "Captain2.png", "Captain3.png", "Captain4.png", "Captain5.png", "Captain6.png", "Captain7.png", "Captain8.png", "Captain9.png"], "displayIcon": "Captain.gif", "groupName": "Captain", "order": 4, "productType": "runner", "publisher": "system"}, {"downloadUrls": ["StrongKnight.gif", "StrongKnight0.png", "StrongKnight1.png", "StrongKnight10.png", "StrongKnight11.png", "StrongKnight12.png", "StrongKnight13.png", "StrongKnight14.png", "StrongKnight15.png", "StrongKnight16.png", "StrongKnight17.png", "StrongKnight18.png", "StrongKnight19.png", "StrongKnight2.png", "StrongKnight3.png", "StrongKnight4.png", "StrongKnight5.png", "StrongKnight6.png", "StrongKnight7.png", "StrongKnight8.png", "StrongKnight9.png"], "groupName": "StrongKnight", "publisher": "system", "order": 4, "productType": "runner", "displayIcon": "StrongKnight.gif"}, {"order": 2, "productType": "runner", "downloadUrls": ["Cat2D.gif", "Cat2D0.png", "Cat2D1.png", "Cat2D10.png", "Cat2D11.png", "Cat2D12.png", "Cat2D13.png", "Cat2D14.png", "Cat2D15.png", "Cat2D2.png", "Cat2D3.png", "Cat2D4.png", "Cat2D5.png", "Cat2D6.png", "Cat2D7.png", "Cat2D8.png", "Cat2D9.png"], "displayIcon": "Cat2D.gif", "groupName": "Cat2D", "publisher": "system"}, {"displayIcon": "Pumpkin.gif", "downloadUrls": ["Pumpkin.gif", "Pumpkin0.png", "Pumpkin1.png", "Pumpkin10.png", "Pumpkin11.png", "Pumpkin12.png", "Pumpkin13.png", "Pumpkin14.png", "Pumpkin15.png", "Pumpkin16.png", "Pumpkin17.png", "Pumpkin18.png", "Pumpkin19.png", "Pumpkin2.png", "Pumpkin3.png", "Pumpkin4.png", "Pumpkin5.png", "Pumpkin6.png", "Pumpkin7.png", "Pumpkin8.png", "Pumpkin9.png"], "groupName": "<PERSON><PERSON><PERSON>", "productType": "runner", "order": 4, "publisher": "system"}, {"groupName": "<PERSON><PERSON>", "downloadUrls": ["Boar.gif", "Boar0.png", "Boar1.png", "Boar10.png", "Boar11.png", "Boar12.png", "Boar13.png", "Boar14.png", "Boar15.png", "Boar16.png", "Boar17.png", "Boar2.png", "Boar3.png", "Boar4.png", "Boar5.png", "Boar6.png", "Boar7.png", "Boar8.png", "Boar9.png"], "productType": "runner", "displayIcon": "Boar.gif", "order": 3, "publisher": "system"}, {"publisher": "system", "downloadUrls": ["Zombie.gif", "Zombie0.png", "Zombie1.png", "Zombie10.png", "Zombie11.png", "Zombie12.png", "Zombie13.png", "Zombie14.png", "Zombie15.png", "Zombie2.png", "Zombie3.png", "Zombie4.png", "Zombie5.png", "Zombie6.png", "Zombie7.png", "Zombie8.png", "Zombie9.png"], "groupName": "Zombie", "productType": "runner", "displayIcon": "Zombie.gif", "order": 4}, {"productType": "runner", "groupName": "<PERSON>ress", "downloadUrls": ["Huntress.gif", "Huntress0.png", "Huntress1.png", "Huntress10.png", "Huntress11.png", "Huntress12.png", "Huntress13.png", "Huntress14.png", "Huntress15.png", "Huntress2.png", "Huntress3.png", "Huntress4.png", "Huntress5.png", "Huntress6.png", "Huntress7.png", "Huntress8.png", "Huntress9.png"], "order": 4, "displayIcon": "Huntress.gif", "publisher": "system"}, {"productType": "runner", "displayIcon": "Ragdoll.gif", "publisher": "system", "order": 2, "downloadUrls": ["Ragdoll.gif", "Ragdoll0.png", "Ragdoll1.png", "Ragdoll10.png", "Ragdoll11.png", "Ragdoll12.png", "Ragdoll13.png", "Ragdoll14.png", "Ragdoll15.png", "Ragdoll2.png", "Ragdoll3.png", "Ragdoll4.png", "Ragdoll5.png", "Ragdoll6.png", "Ragdoll7.png", "Ragdoll8.png", "Ragdoll9.png"], "groupName": "<PERSON><PERSON><PERSON><PERSON>"}, {"downloadUrls": ["RedKnight.gif", "RedKnight0.png", "RedKnight1.png", "RedKnight10.png", "RedKnight11.png", "RedKnight12.png", "RedKnight13.png", "RedKnight14.png", "RedKnight15.png", "RedKnight2.png", "RedKnight3.png", "RedKnight4.png", "RedKnight5.png", "RedKnight6.png", "RedKnight7.png", "RedKnight8.png", "RedKnight9.png"], "groupName": "RedKnight", "displayIcon": "RedKnight.gif", "publisher": "system", "order": 4, "productType": "runner"}, {"downloadUrls": ["Siamese.gif", "Siamese0.png", "Siamese1.png", "Siamese10.png", "Siamese11.png", "Siamese12.png", "Siamese13.png", "Siamese14.png", "Siamese15.png", "Siamese2.png", "Siamese3.png", "Siamese4.png", "Siamese5.png", "Siamese6.png", "Siamese7.png", "Siamese8.png", "Siamese9.png"], "displayIcon": "Siamese.gif", "publisher": "system", "groupName": "Siamese", "productType": "runner", "order": 2}, {"groupName": "<PERSON>", "productType": "runner", "order": 4, "downloadUrls": ["Knight.gif", "Knight0.png", "Knight1.png", "Knight10.png", "Knight11.png", "Knight12.png", "Knight13.png", "Knight14.png", "Knight15.png", "Knight2.png", "Knight3.png", "Knight4.png", "Knight5.png", "Knight6.png", "Knight7.png", "Knight8.png", "Knight9.png"], "displayIcon": "Knight.gif", "publisher": "system"}, {"displayIcon": "Knight2D.gif", "productType": "runner", "downloadUrls": ["Knight2D.gif", "Knight2D0.png", "Knight2D1.png", "Knight2D10.png", "Knight2D11.png", "Knight2D12.png", "Knight2D13.png", "Knight2D14.png", "Knight2D15.png", "Knight2D2.png", "Knight2D3.png", "Knight2D4.png", "Knight2D5.png", "Knight2D6.png", "Knight2D7.png", "Knight2D8.png", "Knight2D9.png"], "publisher": "system", "groupName": "Knight2D", "order": 4}, {"productType": "runner", "publisher": "system", "displayIcon": "BlackBoar.gif", "downloadUrls": ["BlackBoar.gif", "BlackBoar0.png", "BlackBoar1.png", "BlackBoar10.png", "BlackBoar11.png", "BlackBoar12.png", "BlackBoar13.png", "BlackBoar14.png", "BlackBoar15.png", "BlackBoar16.png", "BlackBoar17.png", "BlackBoar2.png", "BlackBoar3.png", "BlackBoar4.png", "BlackBoar5.png", "BlackBoar6.png", "BlackBoar7.png", "BlackBoar8.png", "BlackBoar9.png"], "groupName": "BlackBoar", "order": 3}, {"downloadUrls": ["Wizard2D.gif", "Wizard2D0.png", "Wizard2D1.png", "Wizard2D10.png", "Wizard2D11.png", "Wizard2D12.png", "Wizard2D13.png", "Wizard2D14.png", "Wizard2D15.png", "Wizard2D2.png", "Wizard2D3.png", "Wizard2D4.png", "Wizard2D5.png", "Wizard2D6.png", "Wizard2D7.png", "Wizard2D8.png", "Wizard2D9.png"], "publisher": "system", "productType": "runner", "order": 4, "displayIcon": "Wizard2D.gif", "groupName": "Wizard2D"}, {"downloadUrls": ["BlackMan.gif", "BlackMan0.png", "BlackMan1.png", "BlackMan10.png", "BlackMan11.png", "BlackMan12.png", "BlackMan13.png", "BlackMan14.png", "BlackMan15.png", "BlackMan2.png", "BlackMan3.png", "BlackMan4.png", "BlackMan5.png", "BlackMan6.png", "BlackMan7.png", "BlackMan8.png", "BlackMan9.png"], "displayIcon": "BlackMan.gif", "groupName": "BlackMan", "productType": "runner", "order": 6, "publisher": "system"}, {"downloadUrls": ["SkaterBoy.gif", "SkaterBoy0.png", "SkaterBoy1.png", "SkaterBoy10.png", "SkaterBoy11.png", "SkaterBoy12.png", "SkaterBoy13.png", "SkaterBoy14.png", "SkaterBoy15.png", "SkaterBoy16.png", "SkaterBoy17.png", "SkaterBoy18.png", "SkaterBoy19.png", "SkaterBoy2.png", "SkaterBoy20.png", "SkaterBoy21.png", "SkaterBoy22.png", "SkaterBoy23.png", "SkaterBoy24.png", "SkaterBoy25.png", "SkaterBoy26.png", "SkaterBoy27.png", "SkaterBoy28.png", "SkaterBoy29.png", "SkaterBoy3.png", "SkaterBoy30.png", "SkaterBoy31.png", "SkaterBoy32.png", "SkaterBoy33.png", "SkaterBoy34.png", "SkaterBoy35.png", "SkaterBoy36.png", "SkaterBoy37.png", "SkaterBoy38.png", "SkaterBoy39.png", "SkaterBoy4.png", "SkaterBoy40.png", "SkaterBoy41.png", "SkaterBoy42.png", "SkaterBoy43.png", "SkaterBoy44.png", "SkaterBoy5.png", "SkaterBoy6.png", "SkaterBoy7.png", "SkaterBoy8.png", "SkaterBoy9.png"], "publisher": "system", "productType": "runner", "displayIcon": "SkaterBoy.gif", "order": 7, "groupName": "SkaterBoy"}, {"order": 3, "publisher": "system", "productType": "runner", "displayIcon": "Lizard.gif", "groupName": "Lizard", "downloadUrls": ["Lizard.gif", "Lizard0.png", "Lizard1.png", "Lizard10.png", "Lizard11.png", "Lizard12.png", "Lizard13.png", "Lizard14.png", "Lizard15.png", "Lizard16.png", "Lizard17.png", "Lizard18.png", "Lizard19.png", "Lizard2.png", "Lizard3.png", "Lizard4.png", "Lizard5.png", "Lizard6.png", "Lizard7.png", "Lizard8.png", "Lizard9.png"]}, {"productType": "runner", "downloadUrls": ["Chicken.gif", "Chicken0.png", "Chicken1.png", "Chicken10.png", "Chicken11.png", "Chicken12.png", "Chicken13.png", "Chicken2.png", "Chicken3.png", "Chicken4.png", "Chicken5.png", "Chicken6.png", "Chicken7.png", "Chicken8.png", "Chicken9.png"], "displayIcon": "Chicken.gif", "publisher": "system", "groupName": "Chicken", "order": 3}, {"publisher": "system", "order": 1, "productType": "runner", "downloadUrls": ["Doggy.gif", "Doggy0.png", "Doggy1.png", "Doggy10.png", "Doggy11.png", "Doggy12.png", "Doggy13.png", "Doggy14.png", "Doggy15.png", "Doggy16.png", "Doggy17.png", "Doggy2.png", "Doggy3.png", "Doggy4.png", "Doggy5.png", "Doggy6.png", "Doggy7.png", "Doggy8.png", "Doggy9.png"], "displayIcon": "Doggy.gif", "groupName": "Doggy"}, {"publisher": "system", "displayIcon": "Ginger.gif", "productType": "runner", "downloadUrls": ["Ginger.gif", "Ginger0.png", "Ginger1.png", "Ginger10.png", "Ginger11.png", "Ginger12.png", "Ginger13.png", "Ginger14.png", "Ginger15.png", "Ginger2.png", "Ginger3.png", "Ginger4.png", "Ginger5.png", "Ginger6.png", "Ginger7.png", "Ginger8.png", "Ginger9.png"], "groupName": "<PERSON>", "order": 2}, {"downloadUrls": ["Tabby.gif", "Tabby0.png", "Tabby1.png", "Tabby10.png", "Tabby11.png", "Tabby12.png", "Tabby13.png", "Tabby14.png", "Tabby15.png", "Tabby2.png", "Tabby3.png", "Tabby4.png", "Tabby5.png", "Tabby6.png", "Tabby7.png", "Tabby8.png", "Tabby9.png"], "displayIcon": "Tabby.gif", "publisher": "system", "groupName": "<PERSON><PERSON>", "productType": "runner", "order": 2}, {"productType": "runner", "order": 3, "displayIcon": "Crabby.gif", "downloadUrls": ["Crabby.gif", "Crabby0.png", "Crabby1.png", "Crabby10.png", "Crabby11.png", "Crabby12.png", "Crabby13.png", "Crabby14.png", "Crabby15.png", "Crabby16.png", "Crabby17.png", "Crabby2.png", "Crabby3.png", "Crabby4.png", "Crabby5.png", "Crabby6.png", "Crabby7.png", "Crabby8.png", "Crabby9.png"], "publisher": "system", "groupName": "<PERSON><PERSON><PERSON>"}]}