{"appPolicies": {"eula": "", "policies": [{"locale": "en_US", "policyText": "", "policyURL": ""}]}, "identifier": "4DA7BF2B", "nonRenewingSubscriptions": [], "products": [{"displayPrice": "0.99", "familyShareable": false, "internalID": "6742220638", "localizations": [{"description": "System<PERSON>unner", "displayName": "System<PERSON>unner", "locale": "en_US"}], "productID": "202502201514", "referenceName": "System<PERSON>unner", "type": "NonConsumable"}], "settings": {"_applicationInternalID": "6742220542", "_developerTeamID": "DW8HSJN94S", "_failTransactionsEnabled": false, "_lastSynchronizedDate": 761741076.165714, "_locale": "en_US", "_storefront": "USA", "_storeKitErrors": [{"current": {"index": 2, "type": "generic"}, "enabled": false, "name": "Load Products"}, {"current": null, "enabled": false, "name": "Purchase"}, {"current": null, "enabled": false, "name": "Verification"}, {"current": null, "enabled": false, "name": "App Store Sync"}, {"current": null, "enabled": false, "name": "Subscription Status"}, {"current": null, "enabled": false, "name": "App Transaction"}, {"current": null, "enabled": false, "name": "Manage Subscriptions Sheet"}, {"current": null, "enabled": false, "name": "Refund Request Sheet"}, {"current": null, "enabled": false, "name": "Offer Code Redeem Sheet"}]}, "subscriptionGroups": [], "version": {"major": 4, "minor": 0}}