# BDoggy 日志系统迁移回滚方案

## 概述

本文档提供了BDoggy日志系统迁移的完整回滚方案，确保在迁移过程中出现问题时能够快速恢复到原始状态。

## 回滚触发条件

在以下情况下应考虑执行回滚：

1. **编译错误**：迁移后项目无法编译
2. **功能异常**：关键功能出现异常或崩溃
3. **性能问题**：日志记录性能显著下降
4. **测试失败**：单元测试或集成测试大量失败
5. **生产问题**：在生产环境中发现严重问题

## 回滚准备

### 1. 备份验证

在执行回滚前，确认备份文件的完整性：

```bash
# 检查备份目录
ls -la BDoggy/Backup/

# 验证备份文件
for file in BDoggy/Backup/*/; do
    echo "检查备份: $file"
    ls -la "$file"
done
```

### 2. 当前状态记录

记录当前的迁移状态，便于后续分析：

```bash
# 记录当前UnifiedLogger使用情况
grep -r "UnifiedLogger\." BDoggy/ --include="*.swift" > current_unified_usage.txt

# 记录剩余的Logger调用
grep -r "Logger\." BDoggy/ --include="*.swift" > remaining_logger_usage.txt

# 记录编译错误（如果有）
xcodebuild -project BDoggy.xcodeproj -scheme BDoggy build 2>&1 | tee build_errors.log
```

## 回滚方案

### 方案A：完全回滚（推荐）

适用于需要完全恢复到迁移前状态的情况。

#### 步骤1：停止应用

```bash
# 如果应用正在运行，先停止
pkill -f "BDoggy"
```

#### 步骤2：恢复原始文件

```bash
#!/bin/bash

# 设置变量
PROJECT_ROOT="/path/to/BDoggy"
BACKUP_DIR="$PROJECT_ROOT/Backup/最新备份目录"

# 恢复已修改的文件
MODIFIED_FILES=(
    "BDoggyApp.swift"
    "Utils/SystemInfoCollector.swift"
    "Database/OptimizedSystemMonitorDatabase.swift"
    "Utils/SystemMonitorQuickStart.swift"
    "Utils/SystemMonitorScheduler.swift"
    "Utils/SystemMonitorInitializer.swift"
    "Utils/EnhancedSystemInfoCollector.swift"
    "Database/DatabaseMigrationManager.swift"
)

echo "开始恢复原始文件..."
for file in "${MODIFIED_FILES[@]}"; do
    if [ -f "$BACKUP_DIR/$file" ]; then
        cp "$BACKUP_DIR/$file" "$PROJECT_ROOT/$file"
        echo "✅ 已恢复: $file"
    else
        echo "⚠️ 备份文件不存在: $file"
    fi
done
```

#### 步骤3：移除新增文件

```bash
# 移除UnifiedLogger相关文件
rm -f "$PROJECT_ROOT/Utils/UnifiedLogger.swift"
rm -f "$PROJECT_ROOT/Tests/UnifiedLoggerTests.swift"
rm -f "$PROJECT_ROOT/Scripts/migrate_logger.sh"
rm -f "$PROJECT_ROOT/Documentation/LoggerMigrationGuide.md"
rm -f "$PROJECT_ROOT/Documentation/RollbackPlan.md"

echo "✅ 已移除新增文件"
```

#### 步骤4：验证回滚

```bash
# 编译验证
xcodebuild -project BDoggy.xcodeproj -scheme BDoggy build

# 运行测试
xcodebuild test -project BDoggy.xcodeproj -scheme BDoggy

# 检查Logger调用
grep -r "UnifiedLogger\." BDoggy/ --include="*.swift" || echo "✅ 无UnifiedLogger调用"
grep -r "Logger\." BDoggy/ --include="*.swift" | wc -l
```

### 方案B：部分回滚

适用于只有部分文件出现问题的情况。

#### 步骤1：识别问题文件

```bash
# 检查编译错误，识别问题文件
xcodebuild -project BDoggy.xcodeproj -scheme BDoggy build 2>&1 | grep "error:" | awk '{print $1}' | sort | uniq
```

#### 步骤2：选择性恢复

```bash
# 只恢复有问题的文件
PROBLEM_FILES=(
    "Utils/SystemMonitorQuickStart.swift"  # 示例
)

for file in "${PROBLEM_FILES[@]}"; do
    if [ -f "$BACKUP_DIR/$file" ]; then
        cp "$BACKUP_DIR/$file" "$PROJECT_ROOT/$file"
        echo "✅ 已恢复问题文件: $file"
    fi
done
```

#### 步骤3：保留成功的迁移

保持已成功迁移的文件不变，只回滚有问题的部分。

### 方案C：渐进式回滚

适用于需要逐步回滚的情况。

#### 阶段1：回滚复杂文件

```bash
# 先回滚最复杂的文件
cp "$BACKUP_DIR/Database/OptimizedSystemMonitorDatabase.swift" "$PROJECT_ROOT/Database/"
cp "$BACKUP_DIR/Utils/SystemMonitorQuickStart.swift" "$PROJECT_ROOT/Utils/"
```

#### 阶段2：测试验证

```bash
# 编译测试
xcodebuild -project BDoggy.xcodeproj -scheme BDoggy build
```

#### 阶段3：根据结果决定下一步

- 如果编译成功，保持当前状态
- 如果仍有问题，继续回滚其他文件

## 回滚后验证清单

### 功能验证

- [ ] 应用能够正常启动
- [ ] 日志记录功能正常
- [ ] 系统监控功能正常
- [ ] 数据库操作正常
- [ ] 所有测试通过

### 性能验证

- [ ] 日志记录性能正常
- [ ] 内存使用正常
- [ ] CPU使用正常

### 代码验证

- [ ] 无编译错误
- [ ] 无编译警告
- [ ] 代码风格一致

## 问题分析和预防

### 常见回滚原因

1. **API不兼容**：UnifiedLogger的API与原Logger不完全兼容
2. **性能问题**：新的日志系统性能不如原系统
3. **功能缺失**：某些原有功能在新系统中缺失
4. **测试失败**：新系统导致测试用例失败

### 预防措施

1. **充分测试**：在迁移前进行充分的单元测试和集成测试
2. **分阶段迁移**：不要一次性迁移所有文件
3. **性能基准**：建立性能基准，监控迁移后的性能变化
4. **回滚演练**：在正式迁移前进行回滚演练

## 回滚脚本

### 自动回滚脚本

```bash
#!/bin/bash
# rollback.sh - 自动回滚脚本

set -e

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
BACKUP_DIR="$1"

if [ -z "$BACKUP_DIR" ]; then
    echo "用法: $0 <备份目录>"
    exit 1
fi

if [ ! -d "$BACKUP_DIR" ]; then
    echo "错误: 备份目录不存在: $BACKUP_DIR"
    exit 1
fi

echo "🔄 开始回滚到: $BACKUP_DIR"

# 恢复文件
find "$BACKUP_DIR" -name "*.swift" | while read backup_file; do
    relative_path="${backup_file#$BACKUP_DIR/}"
    target_file="$PROJECT_ROOT/$relative_path"
    
    if [ -f "$backup_file" ]; then
        cp "$backup_file" "$target_file"
        echo "✅ 已恢复: $relative_path"
    fi
done

# 移除新增文件
rm -f "$PROJECT_ROOT/Utils/UnifiedLogger.swift"
rm -f "$PROJECT_ROOT/Tests/UnifiedLoggerTests.swift"

# 验证
echo "🔍 验证回滚结果..."
if xcodebuild -project "$PROJECT_ROOT/BDoggy.xcodeproj" -scheme BDoggy build > /dev/null 2>&1; then
    echo "✅ 回滚成功，项目可以正常编译"
else
    echo "❌ 回滚后仍有编译问题，请手动检查"
    exit 1
fi

echo "🎉 回滚完成！"
```

### 使用方法

```bash
# 赋予执行权限
chmod +x rollback.sh

# 执行回滚
./rollback.sh /path/to/backup/directory
```

## 联系和支持

如果在回滚过程中遇到问题：

1. 检查本文档的常见问题部分
2. 查看备份文件的完整性
3. 联系开发团队获取支持
4. 保留错误日志和状态信息

## 总结

回滚方案是迁移过程中的重要安全网。通过充分的准备和清晰的步骤，可以确保在出现问题时能够快速、安全地恢复到稳定状态。

记住：**宁可谨慎回滚，也不要冒险继续有问题的迁移。**
