# BDoggy 日志系统迁移指南

## 概述

本指南详细说明如何从现有的Logger.swift和EnhancedLogger.swift迁移到统一的UnifiedLogger.swift系统。

## 迁移策略

### 阶段1：向后兼容迁移（推荐）

UnifiedLogger.swift完全兼容现有Logger.swift的API，可以直接替换：

```swift
// 现有代码保持不变
Logger.info("系统启动")
Logger.error("发生错误")
Logger.measure("操作") { /* 代码 */ }

// 替换为
UnifiedLogger.info("系统启动")
UnifiedLogger.error("发生错误")
UnifiedLogger.measure("操作") { /* 代码 */ }
```

### 阶段2：功能增强迁移（可选）

逐步使用新的增强功能：

```swift
// 添加分类支持
UnifiedLogger.info("数据库连接成功", category: "Database")
UnifiedLogger.error("查询失败", category: "Database")

// 添加错误对象支持
UnifiedLogger.error("网络请求失败", category: "Network", error: networkError)

// 使用增强的性能监控
UnifiedLogger.measureTime("数据库查询", category: "Database") {
    // 数据库操作
}

// 使用数据库专用日志
UnifiedLogger.logDatabaseOperation("INSERT", table: "users", rowsAffected: 1, executionTimeMs: 25.5)
```

## 文件级迁移计划

### 1. BDoggyApp.swift
**现有代码：**
```swift
import Logger

Logger.setupForDevelopment()
Logger.info("应用启动")
```

**迁移后：**
```swift
import UnifiedLogger

UnifiedLogger.setupForDevelopment()
UnifiedLogger.info("应用启动", category: "App")
```

**工作量：** 低（2处修改）

### 2. SystemMonitorQuickStart.swift
**现有代码：**
```swift
Logger.info("开始系统诊断")
Logger.warning("CPU使用率较高: \(cpuUsage)%")
Logger.success("诊断完成")
Logger.error("诊断失败: \(error)")
```

**迁移后：**
```swift
UnifiedLogger.info("开始系统诊断", category: "SystemMonitor")
UnifiedLogger.warning("CPU使用率较高: \(cpuUsage)%", category: "SystemMonitor")
UnifiedLogger.success("诊断完成", category: "SystemMonitor")
UnifiedLogger.error("诊断失败", category: "SystemMonitor", error: error)
```

**工作量：** 中（50+处修改，建议批量替换）

### 3. OptimizedSystemMonitorDatabase.swift
**现有代码（混合使用）：**
```swift
import os.log
private let logger = Logger(subsystem: "BDoggy", category: "Database")
private let enhancedLogger = EnhancedLogger.shared

// Logger.swift调用
logger.info("数据库连接成功")

// EnhancedLogger.swift调用
enhancedLogger.measureTime("查询操作", category: "Database") { }
enhancedLogger.logDatabaseOperation("INSERT", table: "users")
```

**迁移后：**
```swift
// 统一使用UnifiedLogger
UnifiedLogger.info("数据库连接成功", category: "Database")
UnifiedLogger.measureTime("查询操作", category: "Database") { }
UnifiedLogger.logDatabaseOperation("INSERT", table: "users")
```

**工作量：** 中（25+处修改，需要仔细处理）

### 4. SystemInfoCollector.swift
**现有代码：**
```swift
Logger.error("获取系统信息失败: \(error)")
Logger.info("系统信息收集完成")
```

**迁移后：**
```swift
UnifiedLogger.error("获取系统信息失败", category: "SystemInfo", error: error)
UnifiedLogger.info("系统信息收集完成", category: "SystemInfo")
```

**工作量：** 低（8处修改）

## 分类(Category)规范

为了保持日志的一致性，建议使用以下分类：

| 分类 | 用途 | 示例 |
|------|------|------|
| `App` | 应用级别事件 | 启动、关闭、配置 |
| `Database` | 数据库操作 | 连接、查询、迁移 |
| `SystemMonitor` | 系统监控 | 数据采集、分析 |
| `SystemInfo` | 系统信息 | 硬件信息、系统状态 |
| `Network` | 网络操作 | 请求、响应、错误 |
| `Performance` | 性能监控 | 计时、基准测试 |
| `UI` | 用户界面 | 界面事件、用户操作 |
| `General` | 通用日志 | 默认分类 |

## 迁移步骤

### 步骤1：准备工作
1. 备份现有代码
2. 确保所有测试通过
3. 创建新的分支进行迁移

### 步骤2：添加UnifiedLogger
1. 将UnifiedLogger.swift添加到项目
2. 确保编译通过
3. 运行基础测试

### 步骤3：逐文件迁移
1. 从影响最小的文件开始（如BDoggyApp.swift）
2. 使用查找替换功能批量修改
3. 添加适当的category参数
4. 测试每个文件的迁移结果

### 步骤4：处理混合使用文件
1. 重点处理OptimizedSystemMonitorDatabase.swift
2. 移除EnhancedLogger的导入和使用
3. 统一使用UnifiedLogger的API
4. 验证数据库操作日志的正确性

### 步骤5：测试验证
1. 运行所有单元测试
2. 检查日志输出格式
3. 验证分类功能
4. 测试性能监控功能

### 步骤6：清理工作
1. 移除Logger.swift和EnhancedLogger.swift（可选）
2. 更新导入语句
3. 更新文档和注释

## 批量替换脚本

### 基础替换
```bash
# 替换基础日志调用
find . -name "*.swift" -exec sed -i '' 's/Logger\./UnifiedLogger\./g' {} \;

# 添加import语句
find . -name "*.swift" -exec sed -i '' 's/import Logger/import UnifiedLogger/g' {} \;
```

### 高级替换（需要手动验证）
```bash
# 为info调用添加category
sed -i '' 's/UnifiedLogger\.info(\([^)]*\))/UnifiedLogger.info(\1, category: "General")/g' *.swift

# 为error调用添加category
sed -i '' 's/UnifiedLogger\.error(\([^)]*\))/UnifiedLogger.error(\1, category: "General")/g' *.swift
```

## 测试验证清单

### 功能测试
- [ ] 基础日志记录正常工作
- [ ] 日志级别过滤正确
- [ ] 文件日志输出正常
- [ ] 控制台日志显示正确
- [ ] 性能监控功能正常
- [ ] 错误统计功能正常

### 兼容性测试
- [ ] 现有Logger.swift调用正常工作
- [ ] 配置方法正常工作
- [ ] 导出功能正常工作
- [ ] 统计功能正常工作

### 性能测试
- [ ] 日志记录性能无明显下降
- [ ] 内存使用正常
- [ ] 文件I/O性能正常

## 回滚方案

如果迁移过程中出现问题，可以按以下步骤回滚：

1. **恢复备份代码**
   ```bash
   git checkout backup-branch
   ```

2. **移除UnifiedLogger**
   - 删除UnifiedLogger.swift文件
   - 恢复原有的import语句

3. **验证回滚**
   - 运行所有测试
   - 确保功能正常

## 常见问题

### Q: 迁移后性能是否会受影响？
A: UnifiedLogger在设计时考虑了性能优化，正常使用下性能影响微乎其微。

### Q: 是否必须使用category参数？
A: 不是必须的。category参数有默认值"General"，现有代码可以不修改直接使用。

### Q: 如何处理EnhancedLogger的特有功能？
A: UnifiedLogger已经集成了EnhancedLogger的所有核心功能，可以直接替换。

### Q: 迁移过程中如何确保日志不丢失？
A: 建议在低峰期进行迁移，并在迁移前后对比日志输出，确保一致性。

## 最佳实践

1. **渐进式迁移**：不要一次性修改所有文件，逐个文件进行迁移和测试
2. **分类一致性**：在团队内部统一分类命名规范
3. **错误处理**：充分利用error参数，提供更详细的错误信息
4. **性能监控**：在关键操作中使用measureTime方法
5. **定期清理**：设置合适的日志清理策略，避免磁盘空间问题

## 支持和帮助

如果在迁移过程中遇到问题，请：
1. 查看本指南的常见问题部分
2. 检查UnifiedLogger.swift的注释和文档
3. 运行相关的单元测试
4. 联系开发团队获取支持
