//
//  BDoggyApp.swift
//  BDoggy
//
//  Created by K4 on 2024/12/9.
//

import Foundation
import SwiftUI

@main
struct BDoggyApp: App {
    @NSApplicationDelegateAdaptor(AppDelegate.self) var appDelegate
    @StateObject private var windowLifecycleManager = WindowLifecycleManager() // 管理窗口生命周期

    // 控制各窗口的 ID
    static var mainWindowID = "main"
    static var runnerMakerWindowID = "runner-maker"
    static var runnerStoreWindowID = "runner-store"
    static var contributorsWindowID = "contributors"
    static var gifSplitterWindowID = "gif-splitter"
    static var gifComposerWindowID = "gif-composer"
    static var systemMonitorWindowID = "system-monitor"

    init() {
        // 初始化系统监控模块
//        SystemMonitorInitializer.shared.initialize()
        setupLogging()
    }

    /// 设置日志系统
    private func setupLogging() {
        #if DEBUG
            Logger.setupForDevelopment()
        #else
            Logger.setupForProduction()
        #endif
        Logger.info("日志系统配置完成")
    }

    var body: some Scene {
        WindowGroup("BDoggy", id: BDoggyApp.mainWindowID) {
            ContentView()
                .frame(minWidth: 400, minHeight: 450)
        }
        .windowResizability(.contentSize)
        .windowToolbarStyle(UnifiedWindowToolbarStyle())

        Window("runner_maker_title".localized, id: BDoggyApp.runnerMakerWindowID) {
            RunnerMakerView()
                .onAppear {
                    if let window = NSApplication.shared.windows.first(where: { $0.identifier?.rawValue == BDoggyApp.runnerMakerWindowID }) {
                        // 初始设置
                        window.collectionBehavior = [.fullScreenAuxiliary]
                        NSApplication.shared.activate(ignoringOtherApps: true)
                        windowLifecycleManager.registerWindow(id: BDoggyApp.runnerMakerWindowID)
                    }
                }
                .onDisappear {
                    if let window = NSApplication.shared.windows.first(where: { $0.identifier?.rawValue == BDoggyApp.runnerMakerWindowID }) {
                        NotificationCenter.default.removeObserver(window, name: NSWindow.didBecomeKeyNotification, object: nil)
                    }
                }
        }
        .windowResizability(.contentSize)
        .windowToolbarStyle(UnifiedWindowToolbarStyle())

        Window("runner_store_title".localized, id: BDoggyApp.runnerStoreWindowID) {
            StoreAppStoreView()
                .frame(minWidth: 400, idealWidth: 400, maxWidth: 800, minHeight: 350, idealHeight: 350, maxHeight: 700)
                .onAppear {
                    if let window = NSApplication.shared.windows.first(where: { $0.identifier?.rawValue == BDoggyApp.runnerStoreWindowID }) {
                        window.styleMask.remove(.resizable) // 禁止调整大小
                        window.collectionBehavior = [.fullScreenAuxiliary]
                        NSApplication.shared.activate(ignoringOtherApps: true)
                        windowLifecycleManager.registerWindow(id: BDoggyApp.runnerStoreWindowID)
                    }
                }
                .onDisappear {
                    if let window = NSApplication.shared.windows.first(where: { $0.identifier?.rawValue == BDoggyApp.runnerStoreWindowID }) {
                        NotificationCenter.default.removeObserver(window, name: NSWindow.didBecomeKeyNotification, object: nil)
                    }
                }
        }
        .windowResizability(.contentSize)
        .windowToolbarStyle(UnifiedWindowToolbarStyle())

        Window("contributors".localized, id: BDoggyApp.contributorsWindowID) {
            ContributorsView()
                .frame(minWidth: 400, idealWidth: 400, maxWidth: 500, minHeight: 600, idealHeight: 600, maxHeight: 700)
                .onAppear {
                    if let window = NSApplication.shared.windows.first(where: { $0.identifier?.rawValue == BDoggyApp.contributorsWindowID }) {
                        window.styleMask.remove(.resizable) // 禁止调整大小
                        window.collectionBehavior = [.fullScreenAuxiliary]
                        NSApplication.shared.activate(ignoringOtherApps: true)
                        windowLifecycleManager.registerWindow(id: BDoggyApp.contributorsWindowID)
                    }
                }
                .onDisappear {
                    if let window = NSApplication.shared.windows.first(where: { $0.identifier?.rawValue == BDoggyApp.contributorsWindowID }) {
                        NotificationCenter.default.removeObserver(window, name: NSWindow.didBecomeKeyNotification, object: nil)
                    }
                }
        }
        .windowResizability(.contentSize)
        .windowToolbarStyle(UnifiedWindowToolbarStyle())

        Window("gif_splitter".localized, id: BDoggyApp.gifSplitterWindowID) {
            GIFSplitterView()
                .frame(minWidth: 500, idealWidth: 500, maxWidth: 600, minHeight: 600, idealHeight: 600, maxHeight: 700)
                .onAppear {
                    if let window = NSApplication.shared.windows.first(where: { $0.identifier?.rawValue == BDoggyApp.gifSplitterWindowID }) {
                        window.collectionBehavior = [.fullScreenAuxiliary]
                        NSApplication.shared.activate(ignoringOtherApps: true)
                        windowLifecycleManager.registerWindow(id: BDoggyApp.gifSplitterWindowID)
                    }
                }
                .onDisappear {
                    if let window = NSApplication.shared.windows.first(where: { $0.identifier?.rawValue == BDoggyApp.gifSplitterWindowID }) {
                        NotificationCenter.default.removeObserver(window, name: NSWindow.didBecomeKeyNotification, object: nil)
                    }
                }
        }
        .windowResizability(.contentSize)
        .windowToolbarStyle(UnifiedWindowToolbarStyle())

        Window("gif_composer".localized, id: BDoggyApp.gifComposerWindowID) {
            GIFComposerView()
                .frame(minWidth: 900, idealWidth: 1000, maxWidth: 1100, minHeight: 850, idealHeight: 850, maxHeight: 1000)
                .onAppear {
                    if let window = NSApplication.shared.windows.first(where: { $0.identifier?.rawValue == BDoggyApp.gifComposerWindowID }) {
                        window.collectionBehavior = [.fullScreenAuxiliary]
                        NSApplication.shared.activate(ignoringOtherApps: true)
                        windowLifecycleManager.registerWindow(id: BDoggyApp.gifComposerWindowID)
                    }
                }
                .onDisappear {
                    if let window = NSApplication.shared.windows.first(where: { $0.identifier?.rawValue == BDoggyApp.gifComposerWindowID }) {
                        NotificationCenter.default.removeObserver(window, name: NSWindow.didBecomeKeyNotification, object: nil)
                    }
                }
        }
        .windowResizability(.contentSize)
        .windowToolbarStyle(UnifiedWindowToolbarStyle())

        Window("系统监控", id: BDoggyApp.systemMonitorWindowID) {
            EnhancedSystemMonitorView()
                .frame(minWidth: 900, idealWidth: 1000, maxWidth: 1100, minHeight: 850, idealHeight: 850, maxHeight: 1000)
                .onAppear {
                    if let window = NSApplication.shared.windows.first(where: { $0.identifier?.rawValue == BDoggyApp.systemMonitorWindowID }) {
                        window.collectionBehavior = [.fullScreenAuxiliary]
                        NSApplication.shared.activate(ignoringOtherApps: true)
                        windowLifecycleManager.registerWindow(id: BDoggyApp.systemMonitorWindowID)
                    }
                }
                .onDisappear {
                    if let window = NSApplication.shared.windows.first(where: { $0.identifier?.rawValue == BDoggyApp.systemMonitorWindowID }) {
                        NotificationCenter.default.removeObserver(window, name: NSWindow.didBecomeKeyNotification, object: nil)
                    }
                }
        }
        .windowResizability(.contentSize)
        .windowToolbarStyle(UnifiedWindowToolbarStyle())

        #if os(macOS)
            Settings {
                SettingsView()
            }
        #endif
    }
}

class WindowLifecycleManager: NSObject, ObservableObject, NSWindowDelegate {
    private var windows = [String: NSWindow]() // 存储每个窗口的引用

    // 注册窗口并设置委托
    func registerWindow(id: String) {
        DispatchQueue.main.async {
            if let window = NSApplication.shared.windows.first(where: { $0.identifier?.rawValue == id }) {
                window.delegate = self // 设置委托
                self.windows[id] = window // 保存窗口
            }
        }
    }

    // 反注册窗口委托
    func unregisterWindow(id: String) {
        DispatchQueue.main.async {
            if let window = self.windows[id] {
                window.delegate = nil // 反注册委托
                self.windows.removeValue(forKey: id) // 移除窗口
            }
        }
    }

    // 监听窗口恢复到前台事件
    func windowDidBecomeKey(_ notification: Notification) {
        if let window = notification.object as? NSWindow {
//            Logger.debug("窗口恢复到前台: \(window.identifier?.rawValue)")
            // 在这里执行窗口恢复时的操作
            NSApplication.shared.activate(ignoringOtherApps: true)
            // 通过 NotificationCenter 发送焦点获取事件
            NotificationCenter.default.post(name: .windowDidGetFocus, object: window)
        }
    }

    func windowDidBecomeMain(_ notification: Notification) {}

    func windowDidExpose(_ notification: Notification) {}

    func windowWillClose(_ notification: Notification) {
        if let window = notification.object as? NSWindow,
           let windowID = window.identifier?.rawValue
        {
            // 处理特定窗口的关闭事件
            if windowID == BDoggyApp.gifSplitterWindowID {
                // 发送窗口关闭通知，GIFSplitterView可以通过监听此通知来清理资源
                NotificationCenter.default.post(name: .windowWillCloseGIFSplitter, object: nil)
            } else if windowID == BDoggyApp.runnerMakerWindowID {
                NotificationCenter.default.post(name: .windowWillCloseRunnerMaker, object: nil)
            } else if windowID == BDoggyApp.runnerStoreWindowID {
                NotificationCenter.default.post(name: .windowWillCloseRunnerStore, object: nil)
            } else if windowID == BDoggyApp.gifComposerWindowID {
                NotificationCenter.default.post(name: .windowWillCloseGIFComposer, object: nil)
            } else if windowID == BDoggyApp.systemMonitorWindowID {
                NotificationCenter.default.post(name: .windowWillCloseSystemMonitor, object: nil)
            }

            // 移除窗口引用
            unregisterWindow(id: windowID)
        }
    }
}

extension Notification.Name {
    // 定义自定义通知
    static let windowDidGetFocus = Notification.Name("windowDidGetFocus")
    static let windowWillCloseGIFSplitter = Notification.Name("windowWillCloseGIFSplitter")
    static let windowWillCloseRunnerMaker = Notification.Name("windowWillCloseRunnerMaker")
    static let windowWillCloseRunnerStore = Notification.Name("windowWillCloseRunnerStore")
    static let windowWillCloseGIFComposer = Notification.Name("windowWillCloseGIFComposer")
    static let windowWillCloseSystemMonitor = Notification.Name("windowWillCloseSystemMonitor")
}
