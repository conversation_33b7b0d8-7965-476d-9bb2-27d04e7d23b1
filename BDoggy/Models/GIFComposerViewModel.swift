import SwiftUI
import UniformTypeIdentifiers

class GIFComposerViewModel: ObservableObject {
    // 选择的图片数组
    @Published var selectedImages: [(image: NSImage, index: Int, name: String)] = []
    // 帧间隔时间（毫秒）
    @Published var frameInterval: Double = 200 // 默认200毫秒
    // 保存文件夹名称
    @Published var saveFileName: String = "Composed_GIF"
    // 保存目录URL
    @Published var saveDirectoryURL: URL?
    // 加载状态
    @Published var isLoading: Bool = false
    // 错误提示
    @Published var showErrorAlert: Bool = false
    @Published var errorMessage: String = ""
    @Published var currentPreviewFrame: NSImage?
    // 预览状态
    @Published var isPreviewPlaying: Bool = false
    
    // GIF配置选项
    @Published var loopCount: Int = 0 // 0表示无限循环
    @Published var colorDepth: Int = 8 // 颜色深度，默认8位
    
    // 预览动画相关
    private var frameIndex = 0
    private let timerManager = GCDTimerManager(interval: 200)
    private let lastSaveDirectoryKey = "GIFComposer.LastSaveDirectory"
    private let directoryBookmark = "GIFComposer.DirectoryBookmark"
    private let downloadsDirectory = FileManager.default.urls(for: .downloadsDirectory, in: .userDomainMask).first!
    
    init() {}
    
    deinit {
        stopAnimation()
    }
    
    // 选择多张图片
    func selectImages() {
        let panel = NSOpenPanel()
        panel.allowsMultipleSelection = true
        panel.canChooseDirectories = false
        panel.canChooseFiles = true
        panel.allowedContentTypes = [UTType.png, UTType.jpeg]
        
        panel.begin { [weak self] response in
            guard let self = self else { return }
            
            if response == .OK, !panel.urls.isEmpty {
                self.addImages(panel.urls)
            }
        }
    }
    
    // 添加图片到列表
    func addImages(_ urls: [URL]) {
        isLoading = true
        
        Task { @MainActor in
            for url in urls {
                if let image = NSImage(contentsOf: url) {
                    // 添加到已选图片列表
                    let newIndex = selectedImages.count
                    selectedImages.append((image: image, index: newIndex, name: url.lastPathComponent))
                }
            }
            
            // 如果是第一次添加图片，设置预览
            if selectedImages.count > 0 && currentPreviewFrame == nil {
                currentPreviewFrame = selectedImages.first?.image
                startAnimation()
            }
            
            isLoading = false
        }
    }
    
    // 移动图片位置（上移）
    func moveImageUp(at index: Int) {
        guard index > 0 && index < selectedImages.count else { return }
        guard !isPreviewPlaying else { return }
        
        // 交换位置
        selectedImages.swapAt(index, index - 1)
        
        // 更新索引
        for i in 0..<selectedImages.count {
            selectedImages[i].index = i
        }
    }
    
    // 移动图片位置（下移）
    func moveImageDown(at index: Int) {
        guard index >= 0 && index < selectedImages.count - 1 else { return }
        guard !isPreviewPlaying else { return }
        
        // 交换位置
        selectedImages.swapAt(index, index + 1)
        
        // 更新索引
        for i in 0..<selectedImages.count {
            selectedImages[i].index = i
        }
    }
    
    // 拖拽排序图片
    func moveImage(from source: Int, to destination: Int) {
        guard source >= 0 && source < selectedImages.count else { return }
        guard destination >= 0 && destination < selectedImages.count else { return }
        guard !isPreviewPlaying else { return }
        guard source != destination else { return }
        
        // 获取要移动的图片
        let image = selectedImages.remove(at: source)
        
        // 插入到新位置
        selectedImages.insert(image, at: destination)
        
        // 更新索引
        for i in 0..<selectedImages.count {
            selectedImages[i].index = i
        }
    }
    
    // 删除图片
    func removeImage(at index: Int) {
        guard index >= 0 && index < selectedImages.count else { return }
        guard !isPreviewPlaying else { return }
        
        // 删除图片
        selectedImages.remove(at: index)
        
        // 更新索引
        for i in 0..<selectedImages.count {
            selectedImages[i].index = i
        }
        
        // 如果还有图片，继续预览
        if !selectedImages.isEmpty {
            currentPreviewFrame = selectedImages.first?.image
        } else {
            currentPreviewFrame = nil
            stopAnimation()
        }
    }
    
    // 选择保存目录
    func selectSaveDirectory() {
        let panel = NSOpenPanel()
        panel.allowsMultipleSelection = false
        panel.canChooseDirectories = true
        panel.canChooseFiles = false
        panel.canCreateDirectories = true
        panel.prompt = "gif_composer_select_save_location".localized
        
        // 如果有上次保存的目录，设置为初始目录
        if let savedURL = saveDirectoryURL {
            panel.directoryURL = savedURL
        }
        
        panel.begin { [weak self] response in
            guard let self = self else { return }
            
            if response == .OK, let url = panel.url {
                self.saveDirectoryURL = url
            }
        }
    }
    
    // 创建并保存GIF
    func createAndSaveGIF() {
        guard !selectedImages.isEmpty else {
            errorMessage = "gif_composer_error_no_images".localized
            showErrorAlert = true
            return
        }
        
        guard !saveFileName.isEmpty else {
            errorMessage = "gif_composer_error_no_file_name".localized
            showErrorAlert = true
            return
        }
        
        // 验证帧间隔是否在有效范围内
        if frameInterval < 30 || frameInterval > 1000 {
            errorMessage = "gif_composer_error_invalid_interval".localized
            showErrorAlert = true
            return
        }
        
        // 构建保存路径
        let saveDirectory: URL
        
        if let directoryURL = saveDirectoryURL {
            saveDirectory = directoryURL
        } else {
            saveDirectory = downloadsDirectory
        }
        
        // 确保文件名有.gif后缀
        var fileName = saveFileName
        if !fileName.lowercased().hasSuffix(".gif") {
            fileName += ".gif"
        }
        
        let fileURL = saveDirectory.appendingPathComponent(fileName)
        
        // 准备图片和延迟数组
        let images = selectedImages.map { $0.image }
        let delays = Array(repeating: Double(frameInterval) / 1000.0, count: selectedImages.count) // 转换为秒
        
        // 创建GIF配置
        let configuration = GIFHelper.GIFConfiguration(
            loopCount: loopCount,
            colorModel: kCGImagePropertyColorModelRGB,
            depth: colorDepth,
            hasGlobalColorMap: true
        )
        
        // 创建并保存GIF
        let isSuccess = GIFHelper.createGIF(from: images, delays: delays, url: fileURL, configuration: configuration)
        
        if isSuccess {
            errorMessage = "gif_composer_save_success".localized(with: fileURL.path)
            showErrorAlert = true
            
            // 如果是通过直接保存而没有先选择目录，也记录保存位置
            if saveDirectoryURL == nil {
                saveDirectoryURL = saveDirectory
                UserDefaults.standard.set(saveDirectoryURL?.path, forKey: lastSaveDirectoryKey)
            }
        } else {
            errorMessage = "gif_composer_save_error".localized
            showErrorAlert = true
        }
    }
    
    // 清空当前数据
    func clean() {
        isLoading = false
        selectedImages = []
        saveFileName = "Composed_GIF"
        // 不清除saveDirectoryURL，保留用户上次选择的保存位置
        stopAnimation()
        currentPreviewFrame = nil
        isPreviewPlaying = false
    }
    
    // 动画预览相关方法
    private func updatePreviewFrame() {
        guard !selectedImages.isEmpty else { return }
        frameIndex = (frameIndex + 1) % selectedImages.count
        currentPreviewFrame = selectedImages[frameIndex].image
    }
    
    func startAnimation() {
        guard !selectedImages.isEmpty else { return }
        isPreviewPlaying = true
        updatePreviewSpeed()
        timerManager.startTimer { [weak self] in
            self?.updatePreviewFrame()
        }
    }
    
    func updatePreviewSpeed() {
        let speed = Int(frameInterval)
        timerManager.updateIntervalMilliseconds(to: speed)
    }
    
    func stopAnimation() {
        isPreviewPlaying = false
        timerManager.stopTimer()
    }
}
