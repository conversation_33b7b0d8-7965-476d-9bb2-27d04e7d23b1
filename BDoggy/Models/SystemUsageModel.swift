//
//  的属性变化可以被观察到.swift
//  BDoggy
//
//  Created by K4 on 2024/12/10.
//

import Combine
import SwiftUI

// 使用 @Observable 来使 struct 的属性变化可以被观察到
@Observable
final class SystemUsageModel {
    var cpuTotal: Double = 0
    var cpuIdle: Double = 0
    var cpuUser: Double = 0
    var cpuSystem: Double = 0
    var totalMemory: Double = 0
    var availableMemory: Double = 0
    var usedMemoryPercentage: Double = 0
    var usedMemory: Double = 0
    var activeMemory: Double = 0
    var inactiveMemory: Double = 0
    var wiredMemory: Double = 0
    var freeMemory: Double = 0
    var appMemory: Double = 0
    var compressedMemory: Double = 0
    var memoryPressure: Double = 0

    var isCharging: Bool = false
    var isPluggedIn: Bool = false
    var cycleCount: Double = 0
    var temperature: Double = 0
    var cycleLifePercentage: Double = 0
    var powerSourceState: String = "Battery"

    var ipAddress: String = "unknown"
    var networkType: String = "unknown"
    var uploadSpeed: Double = 0.0
    var downloadSpeed: Double = 0.0

    private var isUpdating = false

    // 初始化时注册为通知观察者
    init() {
        NotificationCenter.default.addObserver(
            forName: SystemUsageMonitor.notificationName,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            guard let self = self else { return }
            guard !self.isUpdating else {
                Logger.debug("Already updating, skipping this request.")
                return
            }
            isUpdating = true
            if let userInfo = notification.userInfo as? [String: Any] {
                self.updateUsage(userInfo)
            }
            isUpdating = false
        }
    }

    // 更新 usagePercentage 的方法
    private func updateUsage(_ newUsage: [String: Any]) {
        cpuTotal = (newUsage["total"] as? Double) ?? 0.0
        cpuUser = (newUsage["user"] as? Double) ?? 0.0
        cpuSystem = (newUsage["system"] as? Double) ?? 0.0
        cpuIdle = (newUsage["idle"] as? Double) ?? 0.0
        totalMemory = (newUsage["totalMemory"] as? Double) ?? 0.0
        usedMemory = (newUsage["usedMemory"] as? Double) ?? 0.0
        availableMemory = (newUsage["availableMemory"] as? Double) ?? 0.0
        activeMemory = (newUsage["activeMemory"] as? Double) ?? 0.0
        inactiveMemory = (newUsage["inactiveMemory"] as? Double) ?? 0.0
        wiredMemory = (newUsage["wiredMemory"] as? Double) ?? 0.0
        freeMemory = (newUsage["freeMemory"] as? Double) ?? 0.0
        appMemory = (newUsage["appMemory"] as? Double) ?? 0.0
        memoryPressure = (newUsage["memoryPressure"] as? Double) ?? 0.0
        compressedMemory = (newUsage["compressedMemory"] as? Double) ?? 0.0
        usedMemoryPercentage = (newUsage["usedMemoryPercentage"] as? Double) ?? 0.0
        isCharging = (newUsage["isCharging"] as? Bool) ?? false
        isPluggedIn = (newUsage["isPluggedIn"] as? Bool) ?? false
        cycleCount = (newUsage["cycleCount"] as? Double) ?? 0.0
        temperature = (newUsage["temperature"] as? Double) ?? 0.0
        cycleLifePercentage = (newUsage["cycleLifePercentage"] as? Double) ?? 0.0
        powerSourceState = (newUsage["powerSourceState"] as? String) ?? "Battery"
        ipAddress = (newUsage["ipAddress"] as? String) ?? "unknown"
        networkType = (newUsage["networkType"] as? String) ?? "unknown"
        uploadSpeed = (newUsage["uploadSpeed"] as? Double) ?? 0.0
        downloadSpeed = (newUsage["downloadSpeed"] as? Double) ?? 0.0
    }

    // 确保在对象销毁时移除观察者
    deinit {
        NotificationCenter.default.removeObserver(self, name: SystemUsageMonitor.notificationName, object: nil)
    }
}
