import SwiftUI
import UniformTypeIdentifiers

class GIFSplitterViewModel: ObservableObject {
    @Published var gifURL: URL?
    @Published var frames: [(image: NSImage, index: Int)] = []
    @Published var saveFolderName: String = ""
    @Published var saveDirectoryURL: URL?
    @Published var isLoading: Bool = false
    @Published var showErrorAlert: Bool = false
    @Published var errorMessage: String = ""
    @Published var previewSpeed: Double = 1
    @Published var currentPreviewFrame: NSImage?
    @Published var originSpeed: Double = 0.2
    
    private var frameIndex = 0
    private let timerManager = GCDTimerManager(interval: 500)
    private let lastSaveDirectoryKey = "GIFSplitter.LastSaveDirectory"
    private let directoryBookmark = "GIFSplitter.DirectoryBookmark"
    private let downloadsDirectory = FileManager.default.urls(for: .downloadsDirectory, in: .userDomainMask).first!
    
    init() {}
    
    deinit {
        stopAnimation()
    }
    
    // 选择GIF文件
    func selectGIF() {
        let panel = NSOpenPanel()
        panel.allowsMultipleSelection = false
        panel.canChooseDirectories = false
        panel.canChooseFiles = true
        panel.allowedContentTypes = [UTType.gif]
        
        panel.begin { [weak self] response in
            guard let self = self else { return }
            
            if response == .OK, let url = panel.url {
                self.loadGIF(from: url)
            }
        }
    }
    
    // 加载GIF文件并拆分帧
    func loadGIF(from url: URL) {
        // 设置保存文件夹名称为GIF文件名（不带后缀）
        let fileName = url.lastPathComponent
        saveFolderName = fileName.components(separatedBy: ".").first ?? "frame"
        
        isLoading = true
        gifURL = url
        frames = []
        stopAnimation()
        currentPreviewFrame = nil
        
        Task { @MainActor in
            if let (loadedFrames, delays) = GIFHelper.loadGIF(from: url) {
                // 将加载的帧转换为我们需要的格式
                for (index, frame) in loadedFrames.enumerated() {
                    frames.append((image: frame, index: index))
                }
                originSpeed = delays[0]
                if !frames.isEmpty {
                    currentPreviewFrame = frames.first?.image
                    startAnimation()
                }
            } else {
                errorMessage = "gif_splitter_load_error".localized
                showErrorAlert = true
            }
            
            isLoading = false
        }
    }
    
    // 选择保存目录
    func selectSaveDirectory() {
        let panel = NSOpenPanel()
        panel.allowsMultipleSelection = false
        panel.canChooseDirectories = true
        panel.canChooseFiles = false
        panel.canCreateDirectories = true
        panel.prompt = "gif_splitter_select_save_location".localized
        
        // 如果有上次保存的目录，设置为初始目录
        if let savedURL = saveDirectoryURL {
            panel.directoryURL = savedURL
        }
        
        panel.begin { [weak self] response in
            guard let self = self else { return }
            
            if response == .OK, let url = panel.url {
                self.saveDirectoryURL = url
            }
        }
    }
    
    // 保存拆分的帧到指定文件夹
    func saveFrames() {
        guard !frames.isEmpty else {
            errorMessage = "gif_splitter_error_no_frames".localized
            showErrorAlert = true
            return
        }
        
        guard !saveFolderName.isEmpty else {
            errorMessage = "gif_splitter_error_no_folder_name".localized
            showErrorAlert = true
            return
        }
        
        // 构建保存路径
        let fileManager = FileManager.default
        let saveDirectory: URL
        
        if let directoryURL = saveDirectoryURL {
            saveDirectory = directoryURL.appendingPathComponent(saveFolderName)
        } else {
            saveDirectory = downloadsDirectory.appendingPathComponent(saveFolderName)
        }

        do {
            // 创建文件夹（如果不存在）
            if !fileManager.fileExists(atPath: saveDirectory.path) {
                try fileManager.createDirectory(at: saveDirectory, withIntermediateDirectories: true, attributes: nil)
            }
            
            // 保存每一帧
            for (index, frame) in frames.enumerated() {
                let fileName = "\(saveFolderName)\(index).png"
                let fileURL = saveDirectory.appendingPathComponent(fileName)
                
                if let imageData = frame.image.pngData() {
                    try imageData.write(to: fileURL)
                }
            }
            
            // 保存成功
            errorMessage = "gif_splitter_save_success".localized(with: saveDirectory.path)
            showErrorAlert = true
            
            // 如果是通过直接保存而没有先选择目录，也记录保存位置
            if saveDirectoryURL == nil {
                saveDirectoryURL = saveDirectory.deletingLastPathComponent()
                UserDefaults.standard.set(saveDirectoryURL?.path, forKey: lastSaveDirectoryKey)
            }
            
        } catch {
            errorMessage = "gif_splitter_save_error".localized(with: error.localizedDescription)
            showErrorAlert = true
        }
    }
    
    // 清空当前数据
    func clean() {
        previewSpeed = 1
        isLoading = false
        gifURL = nil
        frames = []
        saveFolderName = ""
        // 不清除saveDirectoryURL，保留用户上次选择的保存位置
        stopAnimation()
        currentPreviewFrame = nil
    }
    
    // 动画预览相关方法
    private func updatePreviewFrame() {
        guard !frames.isEmpty else { return }
        frameIndex = (frameIndex + 1) % frames.count
        currentPreviewFrame = frames[frameIndex].image
    }
    
    func startAnimation() {
        guard !frames.isEmpty else { return }
        updatePreviewSpeed()
        timerManager.startTimer { [weak self] in
            self?.updatePreviewFrame()
        }
    }
    
    func updatePreviewSpeed() {
        let speed = Int(originSpeed * 1000 / previewSpeed)
        timerManager.updateIntervalMilliseconds(to: speed)
    }
    
    func stopAnimation() {
        timerManager.stopTimer()
    }
}

// NSImage扩展，用于将NSImage转换为PNG数据
extension NSImage {
    func pngData() -> Data? {
        guard let tiffRepresentation = tiffRepresentation,
              let bitmapImage = NSBitmapImageRep(data: tiffRepresentation) else { return nil }
        return bitmapImage.representation(using: .png, properties: [:])
    }
}
