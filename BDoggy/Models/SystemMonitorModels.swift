//
//  SystemMonitorModels.swift
//  BDoggy
//
//  Created by System Monitor on 2024/12/11.
//

import Foundation

// MARK: - 系统指标模型

struct SystemMetrics: Codable {
    let id: String
    let timestamp: Date
    
    // CPU 相关
    let cpuUsagePercent: Double
    let topProcesses: [ProcessInfoModel]
    
    // 内存相关
    let memoryUsagePercent: Double
    let memoryUsedGB: Double
    let memoryAvailableGB: Double
    let swapUsedGB: Double
    let swapTotalGB: Double
    
    // 磁盘相关
    let diskFreeGB: Double
    let diskUsagePercent: Double
    
    // 电池相关
    let batteryLevel: Int?
    let batteryHealth: Double?
    let batteryCycleCount: Int?
    let powerSourceType: String
    
    // 温度和风扇
    let cpuTemperature: Double?
    let gpuTemperature: Double?
    let fanSpeed: Int?
    
    // 活跃应用
    let activeAppBundleID: String?
    let activeAppName: String?
    
    init() {
        self.id = UUID().uuidString
        self.timestamp = Date()
        
        // CPU 指标
        let cpuMetrics = SystemMetricsCollector.getCPUMetrics()
        self.cpuUsagePercent = cpuMetrics.usage
        self.topProcesses = cpuMetrics.topProcesses
        
        // 内存指标
        let memoryMetrics = SystemMetricsCollector.getMemoryMetrics()
        self.memoryUsagePercent = memoryMetrics.usagePercent
        self.memoryUsedGB = memoryMetrics.usedGB
        self.memoryAvailableGB = memoryMetrics.availableGB
        self.swapUsedGB = memoryMetrics.swapUsedGB
        self.swapTotalGB = memoryMetrics.swapTotalGB
        
        // 磁盘指标
        let diskMetrics = SystemMetricsCollector.getDiskMetrics()
        self.diskFreeGB = diskMetrics.freeGB
        self.diskUsagePercent = diskMetrics.usagePercent
        
        // 电池指标
        let batteryMetrics = SystemMetricsCollector.getBatteryMetrics()
        self.batteryLevel = batteryMetrics.level
        self.batteryHealth = batteryMetrics.health
        self.batteryCycleCount = batteryMetrics.cycleCount
        self.powerSourceType = batteryMetrics.powerSourceType
        
        // 温度指标
        let thermalMetrics = SystemMetricsCollector.getThermalMetrics()
        self.cpuTemperature = thermalMetrics.cpuTemp
        self.gpuTemperature = thermalMetrics.gpuTemp
        self.fanSpeed = thermalMetrics.fanSpeed
        
        // 活跃应用
        let activeApp = SystemMetricsCollector.getActiveApplication()
        self.activeAppBundleID = activeApp.bundleID
        self.activeAppName = activeApp.name
    }
}

// MARK: - 辅助结构体

struct CPUMetrics {
    let usage: Double
    let topProcesses: [ProcessInfoModel]
}

struct MemoryMetrics {
    let usagePercent: Double
    let usedGB: Double
    let availableGB: Double
    let swapUsedGB: Double
    let swapTotalGB: Double
}

struct DiskMetrics {
    let freeGB: Double
    let usagePercent: Double
}

struct BatteryMetrics {
    let level: Int?
    let health: Double?
    let cycleCount: Int?
    let powerSourceType: String
}

struct ThermalMetrics {
    let cpuTemp: Double?
    let gpuTemp: Double?
    let fanSpeed: Int?
}

struct ActiveApplication {
    let bundleID: String?
    let name: String?
}

// MARK: - 监控配置模型

struct MonitoringConfig: Codable {
    var isEnabled: Bool = true
    var collectionInterval: CollectionInterval = .fiveMinutes
    var enableCPUMonitoring: Bool = true
    var enableMemoryMonitoring: Bool = true
    var enableDiskMonitoring: Bool = true
    var enableBatteryMonitoring: Bool = true
    var enableThermalMonitoring: Bool = true
    var enableAppTracking: Bool = false // 默认关闭，需要用户授权
    var dataRetentionDays: Int = 30
    
    enum CollectionInterval: String, CaseIterable, Codable {
        case oneMinute = "1min"
        case fiveMinutes = "5min"
        case oneHour = "1hour"
        
        var timeInterval: TimeInterval {
            switch self {
            case .oneMinute: return 60
            case .fiveMinutes: return 300
            case .oneHour: return 3600
            }
        }
        
        var displayName: String {
            switch self {
            case .oneMinute: return "每分钟"
            case .fiveMinutes: return "每5分钟"
            case .oneHour: return "每小时"
            }
        }
    }
}

// MARK: - 数据库错误类型

enum SystemMonitorError: Error, LocalizedError {
    case databaseNotInitialized
    case dataCollectionFailed(String)
    case permissionDenied(String)
    case invalidConfiguration
    
    var errorDescription: String? {
        switch self {
        case .databaseNotInitialized:
            return "数据库未初始化"
        case .dataCollectionFailed(let reason):
            return "数据采集失败: \(reason)"
        case .permissionDenied(let permission):
            return "权限被拒绝: \(permission)"
        case .invalidConfiguration:
            return "配置无效"
        }
    }
}
