//
//  EnhancedSystemMonitorModels.swift
//  BDoggy
//
//  Created by K4 on 2025/1/11.
//

import Foundation

// MARK: - 系统信息模型

struct SystemInfo: Codable {
    let id: String
    let deviceModel: String
    let cpuName: String
    let totalMemoryGB: Double
    let totalDiskGB: Double
    let macOSVersion: String
    let recordedAt: Date
    
    init() {
        self.id = UUID().uuidString
        self.deviceModel = SystemInfoCollector.getDeviceModel()
        self.cpuName = SystemInfoCollector.getCPUName()
        self.totalMemoryGB = SystemInfoCollector.getTotalMemoryGB()
        self.totalDiskGB = SystemInfoCollector.getTotalDiskGB()
        self.macOSVersion = SystemInfoCollector.getMacOSVersion()
        self.recordedAt = Date()
    }

    init(deviceModel: String, cpuName: String, totalMemoryGB: Double, totalDiskGB: Double, macOSVersion: String, recordedAt: Date) {
        self.id = UUID().uuidString
        self.deviceModel = deviceModel
        self.cpuName = cpuName
        self.totalMemoryGB = totalMemoryGB
        self.totalDiskGB = totalDiskGB
        self.macOSVersion = macOSVersion
        self.recordedAt = recordedAt
    }
}

// MARK: - 增强版系统指标模型

/// 增强版系统指标，包含更详细的信息
struct EnhancedSystemMetrics: Codable, Identifiable {
    let id: String
    let timestamp: Date
    
    // CPU相关
    let cpuUsagePercent: Double
    let cpuTemperature: Double?
    let cpuCoreCount: Int
    
    // 内存相关
    let memoryUsagePercent: Double
    let memoryUsedGB: Double
    let memoryAvailableGB: Double
    let memoryTotalGB: Double
    let swapUsedGB: Double
    let swapTotalGB: Double
    
    // 磁盘相关
    let diskFreeGB: Double
    let diskUsagePercent: Double
    let diskTotalGB: Double
    
    // 电池相关（可选，桌面机可能没有）
    let batteryLevel: Int?
    let batteryHealth: Double?
    let batteryCycleCount: Int?
    let powerSourceType: String
    
    // 温度和风扇
    let gpuTemperature: Double?
    let fanSpeed: Int?
    
    // 当前活跃应用
    let activeAppBundleID: String?
    let activeAppName: String?
    
    // 系统负载
    let loadAverage1min: Double
    let loadAverage5min: Double
    let loadAverage15min: Double
    
    // 网络相关
    let networkBytesIn: UInt64
    let networkBytesOut: UInt64
    
    // 进程信息
    let topProcesses: [ProcessInfoModel]
    
    init() {
        self.id = UUID().uuidString
        self.timestamp = Date()
        
        // 采集CPU信息
        self.cpuUsagePercent = EnhancedSystemInfoCollector.getCPUUsage()
        self.cpuTemperature = EnhancedSystemInfoCollector.getCPUTemperature()
        self.cpuCoreCount = EnhancedSystemInfoCollector.getCPUCoreCount()
        
        // 采集内存信息
        let memoryInfo = EnhancedSystemInfoCollector.getMemoryInfo()
        self.memoryUsagePercent = memoryInfo.usagePercent
        self.memoryUsedGB = memoryInfo.usedGB
        self.memoryAvailableGB = memoryInfo.availableGB
        self.memoryTotalGB = memoryInfo.totalGB
        self.swapUsedGB = memoryInfo.swapUsedGB
        self.swapTotalGB = memoryInfo.swapTotalGB
        
        // 采集磁盘信息
        let diskInfo = EnhancedSystemInfoCollector.getDiskInfo()
        self.diskFreeGB = diskInfo.freeGB
        self.diskUsagePercent = diskInfo.usagePercent
        self.diskTotalGB = diskInfo.totalGB
        
        // 采集电池信息
        let batteryInfo = EnhancedSystemInfoCollector.getBatteryInfo()
        self.batteryLevel = batteryInfo.level
        self.batteryHealth = batteryInfo.health
        self.batteryCycleCount = batteryInfo.cycleCount
        self.powerSourceType = batteryInfo.powerSourceType
        
        // 采集温度和风扇信息
        self.gpuTemperature = EnhancedSystemInfoCollector.getGPUTemperature()
        self.fanSpeed = EnhancedSystemInfoCollector.getFanSpeed()
        
        // 采集当前活跃应用
        let activeApp = EnhancedSystemInfoCollector.getActiveApplication()
        self.activeAppBundleID = activeApp.bundleID
        self.activeAppName = activeApp.name
        
        // 采集系统负载
        let loadAvg = EnhancedSystemInfoCollector.getLoadAverage()
        self.loadAverage1min = loadAvg.oneMin
        self.loadAverage5min = loadAvg.fiveMin
        self.loadAverage15min = loadAvg.fifteenMin
        
        // 采集网络信息
        let networkInfo = EnhancedSystemInfoCollector.getNetworkInfo()
        self.networkBytesIn = networkInfo.bytesIn
        self.networkBytesOut = networkInfo.bytesOut
        
        // 采集进程信息
        self.topProcesses = EnhancedSystemInfoCollector.getTopProcesses()
    }
}

// MARK: - 应用使用记录模型

/// 应用使用记录
struct AppUsageRecord: Codable, Identifiable {
    let id: String
    let bundleID: String
    let appName: String
    var startTime: Date
    var endTime: Date?
    var duration: TimeInterval
    var peakCPUUsage: Double
    var CPUUsages: [Double]
    var peakMemoryUsageMB: Double
    var memoryUsageMBs: [Double]
    var avgCPUUsage: Double
    var avgMemoryUsageMB: Double
    
    init(bundleID: String, appName: String) {
        self.id = UUID().uuidString
        self.bundleID = bundleID
        self.appName = appName
        self.startTime = Date()
        self.endTime = nil
        self.duration = 0
        self.peakCPUUsage = 0
        self.CPUUsages = []
        self.peakMemoryUsageMB = 0
        self.memoryUsageMBs = []
        self.avgCPUUsage = 0
        self.avgMemoryUsageMB = 0
    }
    
    /// 结束应用使用记录
    mutating func endSession() {
        self.endTime = Date()
        if let endTime = self.endTime {
            let du = endTime.timeIntervalSince(self.startTime)
            self.duration += du
        }
    }
    
    /// 结束应用使用记录
    func getDurationNoRecord() -> TimeInterval {
        let time = Date()
        return time.timeIntervalSince(self.startTime)
    }
    
    /// 结束应用使用记录
    mutating func resetStartTime() {
        self.startTime = Date()
    }
    
    /// 更新CPU使用率
    mutating func updateCPUUsage(_ usage: Double) {
        self.peakCPUUsage = max(self.peakCPUUsage, usage)
        self.CPUUsages.append(usage)
        // 简单的移动平均
        self.avgCPUUsage = (self.avgCPUUsage + usage) / 2.0
    }
    
    /// 更新内存使用量
    mutating func updateMemoryUsage(_ usageMB: Double) {
        self.peakMemoryUsageMB = max(self.peakMemoryUsageMB, usageMB)
        self.memoryUsageMBs.append(usageMB)
        // 简单的移动平均
        self.avgMemoryUsageMB = (self.avgMemoryUsageMB + usageMB) / 2.0
    }
}

// MARK: - 辅助数据结构

/// 内存信息
struct MemoryInfo {
    let usagePercent: Double
    let usedGB: Double
    let availableGB: Double
    let totalGB: Double
    let swapUsedGB: Double
    let swapTotalGB: Double
}

/// 磁盘信息
struct DiskInfo {
    let freeGB: Double
    let usagePercent: Double
    let totalGB: Double
}

/// 电池信息
struct BatteryInfo {
    let level: Int?
    let health: Double?
    let cycleCount: Int?
    let powerSourceType: String
}

/// 活跃应用信息
struct ActiveAppInfo {
    let bundleID: String?
    let name: String?
}

/// 系统负载信息
struct LoadAverageInfo {
    let oneMin: Double
    let fiveMin: Double
    let fifteenMin: Double
}

/// 网络信息
struct NetworkInfo {
    let bytesIn: UInt64
    let bytesOut: UInt64
}

/// 进程信息模型（增强版）
struct ProcessInfoModel: Codable, Identifiable {
    let id = UUID()
    let pid: Int32
    let name: String
    let cpuUsage: Double
    let memoryUsageMB: Double
    let bundleID: String?
    
    enum CodingKeys: String, CodingKey {
        case pid, name, cpuUsage, memoryUsageMB, bundleID
    }
}

// MARK: - 监控配置

/// 监控配置
struct MonitorConfig: Decodable, Encodable {
    var collectionInterval: TimeInterval = 60 // 默认1分钟
    var enableBatteryMonitoring: Bool = true
    var enableTemperatureMonitoring: Bool = true
    var enableAppUsageTracking: Bool = true
    var maxDataRetentionDays: Int = 30
    var enableDetailedProcessInfo: Bool = true
    
    enum CollectionInterval: TimeInterval, CaseIterable {
        case oneMinute = 60
        case fiveMinutes = 300
        case tenMinutes = 600
        case thirtyMinutes = 1800
        case oneHour = 3600
        
        var displayName: String {
            switch self {
            case .oneMinute: return "1分钟"
            case .fiveMinutes: return "5分钟"
            case .tenMinutes: return "10分钟"
            case .thirtyMinutes: return "30分钟"
            case .oneHour: return "1小时"
            }
        }
    }
}

// MARK: - 数据分析结果

/// 系统健康评估结果
struct SystemHealthAssessment {
    let overallScore: Double // 0-100分
    let cpuHealthScore: Double
    let memoryHealthScore: Double
    let diskHealthScore: Double
    let batteryHealthScore: Double?
    let temperatureHealthScore: Double
    
    let recommendations: [String]
    let warnings: [String]
    let upgradeRecommendations: [String]
    
    let assessmentDate: Date
}

/// AI分析用的数据摘要
struct SystemAnalysisSummary: Codable {
    let deviceModel: String
    let cpuName: String
    let totalMemoryGB: Double
    let totalDiskGB: Double
    let macOSVersion: String
    
    // 最近7天的平均值
    let avgCPUUsage: Double
    let avgMemoryUsage: Double
    let avgDiskUsage: Double
    let avgTemperature: Double?
    
    // 峰值数据
    let peakCPUUsage: Double
    let peakMemoryUsage: Double
    let peakTemperature: Double?
    
    // 电池健康（如果有）
    let batteryHealth: Double?
    let batteryCycleCount: Int?
    
    // 应用使用模式
    let topApps: [String] // 最常用的应用
    let totalUsageHours: Double // 总使用时长
    
    // 性能趋势
    let performanceTrend: String // "improving", "stable", "declining"
    
    let analysisDate: Date
}
