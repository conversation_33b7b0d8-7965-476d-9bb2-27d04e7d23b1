//
//  RunnerMakerViewModel.swift
//  BDoggy
//
//  Created by K4 on 2025/2/18.
//

import Foundation

import SwiftUI

class RunnerMakerViewModel: ObservableObject {
    @Published var runners = [FrameGroup]()
    @Published var runnerName: String = ""
    @Published var frames: [(image: NSImage, name: String, url: URL)] = []
    // 添加错误提示相关的状态
    @Published var showErrorAlert: Bool = false
    @Published var errorMessage: String = ""
    @Published var isEnableCreate: Bool = false
    @Published var previewSpeed: Double = 1
    @Published var currentPreviewFrame = NSImage(systemSymbolName: "photo", accessibilityDescription: "Photo")
    var isLoading: Bool = false
    var error: Error?
    private let purchaseManager = PurchaseManager.shared
    private var frameIndex = 0
    private let timerManager: GCDTimerManager
    private var createPermissionObserver: NSObjectProtocol?
    private static var runnerMakerID = "com.doggy.BDoggy.nonConsumable.RunnerMaker"
    private var usedIcon: String = UserDefaults.standard.string(forKey: Constants.useIconKey) ?? "Doggy"

    // 确保对象销毁
    deinit {
        Logger.debug("maker deinit")
        stopAnimation()
        if let createPermissionObserver = createPermissionObserver {
            NotificationCenter.default.removeObserver(createPermissionObserver)
        }
    }

    init() {
        isEnableCreate = purchaseManager.isProductPurchased(RunnerMakerViewModel.runnerMakerID)
        timerManager = GCDTimerManager(interval: 500)

        // 订阅窗口获取焦点的通知
        if createPermissionObserver == nil {
            createPermissionObserver = NotificationCenter.default.addObserver(forName: .windowDidGetFocus, object: nil, queue: .main) { [weak self] notification in
                if let window = notification.object as? NSWindow, window.identifier!.rawValue == BDoggyApp.runnerMakerWindowID {
                    self?.isEnableCreate = self?.purchaseManager.isProductPurchased(RunnerMakerViewModel.runnerMakerID) ?? false
                }
            }
        }
    }

    //
    @MainActor
    func fetchRunners() {
        Task {
            do {
                let data: [FrameGroup] = try await fetchRunnersFromNetwork()
                self.runners = data
            } catch {
                Logger.error("fail:", error)
            }
        }
    }

    // 删除选中的跑者资源
    @MainActor
    func deleteRunner(item: FrameGroup) async -> Bool {
        guard !item.groupName.isEmpty else {
            errorMessage = "invalid_runner_name".localized
            showErrorAlert = true
            return false
        }

        do {
            // 构建目标文件夹路径
            let dir = FileHelper.shared.getUserFrameDirURL(
                ownerID: UserManager.appAccountToken.uuidString,
                userID: UserManager.appAccountToken.uuidString,
                groupName: item.groupName
            )

            guard usedIcon != dir.lastPathComponent else {
                errorMessage = "cannot_delete_runner_when_used".localized
                showErrorAlert = true
                return false
            }

            // 检查文件夹是否存在
            let fileManager = FileManager.default
            guard fileManager.fileExists(atPath: dir.path) else {
                Logger.error("要删除的文件夹不存在: \(dir.path)")
                errorMessage = "resource_not_found".localized
                showErrorAlert = true
                return false
            }

            // 删除文件夹及其内容
            try fileManager.removeItem(at: dir)
            Logger.info("成功删除跑者资源: \(item.groupName)")

            // 刷新列表
            fetchRunners()

            // 如果当前正在编辑的是被删除的项，则清空编辑区
            if runnerName == item.groupName {
                clean()
            }
            // 通知更新页面
            let usageData: [String: String] = ["groupName": item.groupName]
            NotificationCenter.default.post(name: PurchaseManager.updateNotification, object: nil, userInfo: usageData)

            return true
        } catch {
            Logger.error("删除跑者资源失败: \(error.localizedDescription)")
            errorMessage = "delete_failed".localized + ": \(error.localizedDescription)"
            showErrorAlert = true
            return false
        }
    }

    func cleanupUnusedResources() {
        // 释放预览图像
        currentPreviewFrame = nil
        clean()
        // 如果需要，可以在这里添加其他资源清理逻辑
        // 例如，可以考虑在不需要时释放部分帧图像
    }

    func loadEditRunner(item: FrameGroup) {
        clean()
        runnerName = item.groupName
        for url in item.fileURLs {
            if let image = NSImage(contentsOf: url) {
                frames.append((image: image, name: url.lastPathComponent, url: url))
            }
        }
        startAnimation()
    }

    func clean() {
        runnerName = ""
        frames.removeAll() // 清空 frames 数组
        stopAnimation()
        currentPreviewFrame = NSImage(systemSymbolName: "photo", accessibilityDescription: "Photo")
    }

    private func updateIcon() {
        guard !frames.isEmpty else { return }
        frameIndex = (frameIndex + 1) % frames.count
        currentPreviewFrame = frames[frameIndex].image
    }

    private func startAnimation() {
        guard !frames.isEmpty else { return }
        previewSpeedChange()
        timerManager.startTimer { [weak self] in
            self?.updateIcon()
        }
    }

    func uploadFrames() {
        stopAnimation()
        startAnimation()
    }

    func previewSpeedChange() {
        let speed = max(Int(previewSpeed) * 1000 / frames.count, Constants.frameMinInterval)
        timerManager.updateIntervalMilliseconds(to: speed)
    }

    private func stopAnimation() {
        timerManager.stopTimer()
    }

    @MainActor func saveAndRefresh() {
        Task {
            do {
                let dir = FileHelper.shared.getUserFrameDirURL(ownerID: UserManager.appAccountToken.uuidString, userID: UserManager.appAccountToken.uuidString, groupName: runnerName)
                // 在后台线程执行文件操作
                try await saveFramesToDirectory(at: dir)
                createGIF(at: dir)
                clean()
                fetchRunners()
                // 通知更新页面
                let usageData: [String: String] = ["groupName": runnerName]
                NotificationCenter.default.post(name: PurchaseManager.updateNotification, object: nil, userInfo: usageData)
            } catch RunnerSaveError.emptyFrames {
                errorMessage = "no_frames_to_save".localized
                showErrorAlert = true
            } catch RunnerSaveError.invalidRunnerName {
                errorMessage = "invalid_runner_name".localized
                showErrorAlert = true
            } catch RunnerSaveError.failedToCreateDirectory {
                errorMessage = "failed_create_directory".localized
                showErrorAlert = true
            } catch let RunnerSaveError.failedToCopyFile(error) {
                errorMessage = "failed_copy_file".localized + " - \(error)"
                showErrorAlert = true
            } catch {
                errorMessage = "unknown_error".localized + "：\(error.localizedDescription)"
                showErrorAlert = true
            }
        }
    }

    private func createGIF(at dir: URL) {
        var tempImages = [NSImage]()
        var delays = [Double]()
        for (_, frame) in frames.enumerated() {
            tempImages.append(frame.image)
            delays.append(0.2)
        }

        let newFileName = "\(runnerName).gif"
        let destinationURL = dir.appendingPathComponent(newFileName)
        let isCreated = GIFHelper.createGIF(from: tempImages, delays: delays, url: destinationURL)
        Logger.info("GIF创建状态", isCreated)
    }

    private func saveFramesToDirectory(at destinationPath: URL) async throws {
        // 基础检查
        guard !frames.isEmpty else {
            throw RunnerSaveError.emptyFrames
        }

        guard !runnerName.isEmpty else {
            throw RunnerSaveError.invalidRunnerName
        }

        let fileManager = FileManager.default

        do {
            // 文件操作
            if !fileManager.fileExists(atPath: destinationPath.path) {
                try fileManager.createDirectory(at: destinationPath, withIntermediateDirectories: true, attributes: nil)
            }
            var tempURLs = [URL]()
            for (index, frame) in frames.enumerated() {
                Logger.debug(frame.url)
                // ✅ 开始访问沙盒外资源
                if frame.url.startAccessingSecurityScopedResource() {
                    defer { frame.url.stopAccessingSecurityScopedResource() }
                    let fileExtension = frame.url.pathExtension
                    let tempNewName = "temp_\(frame.url.lastPathComponent)\(index).\(fileExtension)"
                    let tempURL = destinationPath.appendingPathComponent(tempNewName)
                    if fileManager.fileExists(atPath: tempURL.path) {
                        try fileManager.removeItem(at: tempURL)
                    }
                    try fileManager.copyItem(at: frame.url, to: tempURL)
                    tempURLs.append(tempURL)
                } else {
                    let fileExtension = frame.url.pathExtension
                    let tempNewName = "temp_\(frame.url.lastPathComponent)\(index).\(fileExtension)"
                    let tempURL = destinationPath.appendingPathComponent(tempNewName)
                    if fileManager.fileExists(atPath: tempURL.path) {
                        try fileManager.removeItem(at: tempURL)
                    }
                    try fileManager.copyItem(at: frame.url, to: tempURL)
                    tempURLs.append(tempURL)
                }
            }

            guard !tempURLs.isEmpty else {
                throw RunnerSaveError.failedToCopyFile("⚠️ 无法访问安全作用域资源")
            }

            // 先删除目录中所有以 runnerName 为前缀的文件
            do {
                let existingFiles = try fileManager.contentsOfDirectory(at: destinationPath, includingPropertiesForKeys: nil)
                for file in existingFiles {
                    let fileName = file.lastPathComponent
                    if fileName.hasPrefix(runnerName) {
                        try fileManager.removeItem(at: file)
                    }
                }
            } catch {
                Logger.error("delete_old_files_failed", error)
                // 继续执行，不中断流程
            }

            for (index, frame) in tempURLs.enumerated() {
                let fileExtension = frame.pathExtension
                let newFileName = "\(runnerName)\(index).\(fileExtension)"
                let destinationURL = destinationPath.appendingPathComponent(newFileName)
                if fileManager.fileExists(atPath: destinationURL.path) {
                    try fileManager.removeItem(at: destinationURL)
                }
                // 重命名临时文件到最终文件
                try fileManager.moveItem(at: frame, to: destinationURL)
            }
        } catch {
            switch error {
            case CocoaError.fileWriteNoPermission:
                throw RunnerSaveError.failedToCreateDirectory
            default:
                throw RunnerSaveError.failedToCopyFile("Something wrong to copy files")
            }
        }
    }

    // 模拟网络请求
    private func fetchRunnersFromNetwork() async throws -> [FrameGroup] {
        let frameGroups = FileHelper.shared.scanFolders(for: [UserManager.appAccountToken.uuidString])
        return frameGroups
    }

    enum RunnerSaveError: Error {
        case emptyFrames
        case invalidRunnerName
        case failedToCreateDirectory
        case failedToCopyFile(String)
    }
}
