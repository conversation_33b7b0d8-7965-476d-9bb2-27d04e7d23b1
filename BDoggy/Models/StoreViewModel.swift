//
//
//  BDoggy
//
//  Created by K4 on 2024/12/10.
//

import Combine
import SwiftUI

@MainActor
class StoreViewModel: ObservableObject {
    @Published var stores = [StoreItem]()
    @Published var downloadedItems = [StoreItem]()
    @Published var unPurchaseItems = [StoreItem]()
    @Published var purchasedItems = [StoreItem]()
    @Published var isLoading: Bool = false
    @Published var error: Error? = nil
    private var originStoreItem = [StoreItem]()

    init() {
        Logger.debug("Store init")
    }

    //
    @MainActor
    func fetchStores() {
        Task {
            do {
                let data: [StoreItem] = try await NetworkManager.shared.request(url: "\(Constants.apiBaseURL)/api/store/system", method: HTTPMethod.GET)
                self.originStoreItem = data
                self.stores = self.processStoreItems(storeItems: self.originStoreItem)

            } catch {
                Logger.error("failed:", error)
            }
        }
    }

    private func verifyUser(store: StoreItem) -> <PERSON>ol {
        if store.productType == .system {
            return true
        }
        let userID = UserDefaults.standard.string(forKey: Constants.AppleUserIDKey)
        if store.owner == userID {
            return false
        }
        return true
    }

    //
    func download(store: StoreItem) {
        if !self.verifyUser(store: store) {
            // TODO: 弹窗提示
            return
        }
        var dir = FileHelper.shared.getSystemFrameDirURL(groupName: store.title)
        switch store.productType {
        case .custom:
            dir = FileHelper.shared.getUserFrameDirURL(ownerID: store.owner, userID: store.publisher, groupName: store.title)
        default: break
        }
        let downloadUrls = store.downloadUrls.filter {
            guard let url = URL(string: $0) else { return false }
            return !self.isImageDownloaded(url: url, downloadDirURL: dir)
        }

        guard !store.downloadUrls.isEmpty else {
            return
        }
        store.isDownloading = true
        TencentCOSDownloader.shared.fetchTemporaryCredentials(urls: downloadUrls, targetFolder: dir) { (result: Result<[URL], NetworkError>) in
            switch result {
            case .success:
                DispatchQueue.main.async {
                    store.isDownloading = false
                    store.isDownloaded = true
                }
            case .failure:
                DispatchQueue.main.async {
                    store.isDownloading = false
                }
            }
        }
    }

    private func processStoreItems(storeItems: [StoreItem]) -> [StoreItem] {
        var processedItems: [StoreItem] = []

        for store in storeItems {
            let storeCopy = store.copy()

            // 增加对 store.downloadUrls 的空值判断
            guard !storeCopy.downloadUrls.isEmpty else {
                Logger.debug("下载链接为空，跳过此项目: \(storeCopy.title)")
                continue
            }

            var dir = FileHelper.shared.getSystemFrameDirURL(groupName: storeCopy.title)
            switch storeCopy.productType {
            case .custom:
//                let userID = UserDefaults.standard.string(forKey: Constants.AppleUserIDKey)
                dir = FileHelper.shared.getUserFrameDirURL(ownerID: storeCopy.owner, userID: storeCopy.publisher, groupName: storeCopy.title)
            default: break
            }

            if storeCopy.isPurchased {
                let downloaded = self.isAllDownloaded(urls: storeCopy.downloadUrls, downloadDirURL: dir)
                storeCopy.isDownloaded = downloaded
            }

            processedItems.append(storeCopy)
        }
        // 排序：未购买的在前，已购买未下载的在中间，已购买且已下载的在后
        processedItems.sort {
            if !$0.isPurchased && $1.isPurchased {
                return true
            } else if $0.isPurchased && !$1.isPurchased {
                return false
            } else {
                return !$0.isDownloaded && $1.isDownloaded
            }
        }

        // 更新已下载、未购买和已购买的数组
        self.downloadedItems = processedItems.filter { $0.isPurchased && $0.isDownloaded }
        self.unPurchaseItems = processedItems.filter { !$0.isPurchased }
        self.purchasedItems = processedItems.filter { $0.isPurchased && !$0.isDownloaded }
        return processedItems
    }

    // 确保对象销毁
    deinit {}

    func isAllDownloaded(urls: [String], downloadDirURL: URL) -> Bool {
        // 判断是否所有图片已下载
        let allImagesDownloaded = urls.allSatisfy {
            guard let url = URL(string: $0) else { return true }
            return self.isImageDownloaded(url: url, downloadDirURL: downloadDirURL)
        }
        return allImagesDownloaded
    }

    // 判断图片是否已下载
    func isImageDownloaded(url: URL, downloadDirURL: URL) -> Bool {
        let fileName = url.lastPathComponent
        let fileURL = downloadDirURL.appendingPathComponent(fileName)
        return FileManager.default.fileExists(atPath: fileURL.path)
    }
}

class StoreItem: ObservableObject, Identifiable, Decodable {
    var id: String
    let title: String
    let owner: String
    let publisher: String
    let description: String?
    var price: String
    let downloadUrls: [String]
    var isPurchased: Bool
    let productType: ProductType
    @Published var isDownloading: Bool = false // 默认值
    @Published var isPurchasing: Bool = false // 默认值
    @Published var isDownloaded: Bool = false // 默认值

    enum CodingKeys: String, CodingKey {
        case id, title, owner, publisher, description, price, downloadUrls, isPurchased, productType
    }

    init(id: String, title: String, owner: String, publisher: String, description: String?, price: String, downloadUrls: [String], isPurchased: Bool, productType: ProductType) {
        self.id = id
        self.title = title
        self.owner = owner
        self.publisher = publisher
        self.description = description
        self.price = price
        self.downloadUrls = downloadUrls
        self.isPurchased = isPurchased
        self.productType = productType
    }

    // 创建副本的方法
    func copy() -> StoreItem {
        return StoreItem(
            id: self.id,
            title: self.title,
            owner: self.owner,
            publisher: self.publisher,
            description: self.description,
            price: self.price,
            downloadUrls: self.downloadUrls,
            isPurchased: self.isPurchased,
            productType: self.productType
        )
    }
}

enum ProductType: String, Decodable {
    case system
    case custom
    case share
    case other
}
