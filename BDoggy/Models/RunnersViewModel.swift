//
//  的属性变化可以被观察到.swift
//  BDoggy
//
//  Created by K4 on 2024/12/10.
//

import Combine
import SwiftUI

class RunnersViewModel: ObservableObject {
    @Published var runners = [FrameGroup]()
    @Published var isLoading: Bool = false
    @Published var error: Error? = nil
    @Published var usedIcon: String = UserDefaults.standard.string(forKey: Constants.useIconKey) ?? "Doggy"
    @Published var isUpdate: Bool = false

    private var updateObserver: NSObjectProtocol?
    // 确保对象销毁
    deinit {
        if let updateObserver = updateObserver {
            NotificationCenter.default.removeObserver(updateObserver)
        }
    }

    init() {
        Logger.debug("RunnersViewModel init")
        // 订阅窗口获取焦点的通知
        if updateObserver == nil {
            Logger.debug("RunnersView update")
            updateObserver = NotificationCenter.default.addObserver(forName: PurchaseManager.updateNotification, object: nil, queue: .main) { [weak self] _ in
                self?.isUpdate = true
            }
        }
    }

    //
    func fetchRunners() async {
        do {
            await MainActor.run {
                isLoading = true
            }

            let fetchedItems = try await fetchRunnersFromNetwork()

            // 过滤和排序 fetchedItems
            let filteredAndSortedItems = filterAndSortRunners(fetchedItems)

            await MainActor.run {
                self.runners = filteredAndSortedItems
                self.isLoading = false
                self.isUpdate = false
            }
        } catch {
            await MainActor.run {
                self.error = error
                self.isLoading = false
                self.isUpdate = false
            }
        }
    }

    func doChange(from dirName: String) {
        if dirName != usedIcon {
            usedIcon = dirName
            DispatchQueue.main.async {
                NotificationCenter.default.post(name: StatusBarController.notificationChangeIcons, object: nil, userInfo: [Constants.Notification_ChangeFrameKey: dirName])
            }
        }
    }

    // 过滤和排序 runners
    private func filterAndSortRunners(_ items: [FrameGroup]) -> [FrameGroup] {
        // 获取 ProductMapping 中的数据
        let purchasedItems = ProductMapping.shared.purchasedItems
        let systemProducts = ProductMapping.shared.systemProducts

        // 合并 purchasedItems 和 systemProducts
        let allProducts = purchasedItems + systemProducts

        // 创建 groupName 到 order 的映射
        var groupNameToOrderMap: [String: Int] = [:]
        for product in allProducts {
            groupNameToOrderMap[product.groupName] = product.order
        }

        // 获取当前用户ID
        let currentUserID = UserManager.appAccountToken.uuidString
        
        // 将items分为两组：用户自己的items和其他items
        let userOwnedItems = items.filter { $0.owner == currentUserID }
        let otherItems = items.filter { $0.owner != currentUserID }
        
        // 过滤其他items，只保留groupName在allProducts中的元素
        let validGroupNames = Set(allProducts.map { $0.groupName })
        let filteredOtherItems = otherItems.filter { validGroupNames.contains($0.groupName) }
        
        // 按照order排序其他items
        let sortedOtherItems = filteredOtherItems.sorted { item1, item2 in
            let order1 = groupNameToOrderMap[item1.groupName] ?? Int.max
            let order2 = groupNameToOrderMap[item2.groupName] ?? Int.max
            return order1 < order2
        }
        
        // 将用户自己的items排序到最后
        let sortedUserOwnedItems = userOwnedItems.sorted { item1, item2 in
            item1.groupName < item2.groupName // 按字母顺序排序用户自己的items
        }
        
        // 合并两组排序后的items
        return sortedOtherItems + sortedUserOwnedItems
    }

    // 模拟网络请求
    private func fetchRunnersFromNetwork() async throws -> [FrameGroup] {
        let frameGroups = FileHelper.shared.scanFolders(for: ["system", UserManager.appAccountToken.uuidString])
        return frameGroups
    }
}
