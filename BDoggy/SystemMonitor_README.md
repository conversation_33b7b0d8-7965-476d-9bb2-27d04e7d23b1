# BDoggy 增强版系统监控模块

## 概述

BDoggy 的增强版系统监控模块是一个完整的、模块化的 macOS 系统监控解决方案，使用原生 IOKit、sysctl、NSWorkspace 等 API 进行系统信息采集，并通过 SQLite 进行本地数据存储。

## 主要特性

### 🔍 全面的系统监控
- **CPU 监控**: 实时 CPU 使用率、温度、核心数
- **内存监控**: 内存使用率、可用内存、Swap 使用情况
- **磁盘监控**: 磁盘使用率、可用空间
- **电池监控**: 电池电量、健康度、循环次数（支持笔记本）
- **温度监控**: CPU/GPU 温度、风扇转速
- **网络监控**: 网络流量统计
- **进程监控**: Top 进程信息、CPU/内存占用

### 📱 应用使用跟踪
- 自动跟踪当前活跃应用
- 记录应用使用时长
- 统计应用 CPU/内存峰值和平均值
- 生成应用使用报告

### 💾 数据存储与管理
- 使用 SQLite 进行本地数据存储
- 数据存储在 `~/Library/Application Support/BDoggy/`
- 支持数据清理和导出
- 可配置数据保留天数

### 🔐 隐私与权限
- 遵循 macOS 隐私规范
- 支持辅助功能权限管理
- 完全本地存储，不上传任何数据
- 透明的权限请求流程

### ⚙️ 灵活配置
- 可配置采集间隔（1分钟到1小时）
- 可选择启用/禁用特定监控功能
- 支持日志级别配置
- 自动数据清理配置

## 架构设计

### 核心组件

1. **EnhancedSystemInfoCollector**: 系统信息采集器
   - 使用 IOKit 采集硬件信息
   - 使用 sysctl 获取系统参数
   - 使用 NSWorkspace 跟踪应用状态

2. **EnhancedSystemMonitorDatabase**: 数据库管理器
   - SQLite 数据库操作
   - 数据模型定义
   - 查询和统计功能

3. **SystemMonitorScheduler**: 监控调度器
   - 定时数据采集
   - 应用使用跟踪
   - 权限管理集成

4. **PermissionManager**: 权限管理器
   - 检查和请求系统权限
   - 权限状态监控
   - 用户引导

5. **Logger**: 日志管理器
   - 分级日志记录
   - 文件和控制台输出
   - 性能监控日志

### 数据模型

#### 系统信息表 (system_info)
- 设备型号、CPU 信息、内存/磁盘容量
- macOS 版本、记录时间
- 只记录一次的静态信息

#### 指标日志表 (metrics_log)
- 实时系统指标数据
- CPU、内存、磁盘使用率
- 电池、温度、风扇信息
- 当前活跃应用信息

#### 应用使用表 (app_usage)
- 应用使用记录
- 使用时长、CPU/内存峰值
- 按应用和日期统计

## 使用方法

### 初始化

```swift
// 在应用启动时初始化
SystemMonitorInitializer.shared.initialize()

// 启动监控
SystemMonitorInitializer.shared.startMonitoring()
```

### 配置监控

```swift
let scheduler = SystemMonitorScheduler.shared

// 配置采集间隔
scheduler.config.collectionInterval = 300 // 5分钟

// 启用/禁用功能
scheduler.config.enableBatteryMonitoring = true
scheduler.config.enableAppUsageTracking = true
scheduler.config.maxDataRetentionDays = 30
```

### 获取数据

```swift
// 获取最近7天的指标数据
let metrics = scheduler.getRecentMetrics(days: 7)

// 获取应用使用汇总
let appUsage = scheduler.getAppUsageSummary(days: 7)

// 获取监控状态
let status = SystemMonitorInitializer.shared.getMonitoringStatus()
```

### 权限管理

```swift
let permissionManager = PermissionManager.shared

// 检查权限
permissionManager.checkAllPermissions()

// 请求辅助功能权限
permissionManager.requestAccessibilityPermission()

// 打开系统设置
permissionManager.openAccessibilitySettings()
```

## 界面组件

### EnhancedSystemMonitorView
主监控界面，包含：
- 监控状态显示
- 权限状态检查
- 监控控制按钮
- 数据统计卡片
- 快速操作按钮

### MonitoringSettingsView
设置界面，包含：
- 基本监控配置
- 功能开关
- 日志设置
- 数据管理
- 高级选项

### DataManagementView
数据管理界面，包含：
- 数据统计显示
- 最近数据预览
- 数据导出功能
- 数据清理操作

## 文件结构

```
BDoggy/
├── Models/
│   └── EnhancedSystemMonitorModels.swift    # 数据模型
├── Utils/
│   ├── EnhancedSystemInfoCollector.swift    # 系统信息采集
│   ├── EnhancedSystemMonitorDatabase.swift  # 数据库管理
│   ├── SystemMonitorScheduler.swift         # 监控调度
│   ├── PermissionManager.swift              # 权限管理
│   ├── SystemMonitorInitializer.swift      # 初始化器
│   └── Logger.swift                         # 日志管理
└── Views/
    ├── EnhancedSystemMonitorView.swift      # 主监控界面
    ├── MonitoringSettingsView.swift         # 设置界面
    └── DataManagementView.swift             # 数据管理界面
```

## 数据存储位置

- **数据库**: `~/Library/Application Support/BDoggy/system_monitor.db`
- **日志文件**: `~/Library/Application Support/BDoggy/system_monitor.log`

## 权限要求

### 必需权限
- **辅助功能**: 用于跟踪当前活跃应用

### 可选权限
- **完全磁盘访问**: 用于获取更详细的系统信息
- **屏幕录制**: 用于屏幕相关功能（暂未使用）

## 性能考虑

- 使用后台队列进行数据采集，不阻塞主线程
- 可配置的采集间隔，平衡精度和性能
- 自动数据清理，控制数据库大小
- 高效的 SQLite 查询和索引

## 隐私保护

- 所有数据完全存储在本地
- 不收集或上传任何个人信息
- 遵循 macOS 隐私和安全规范
- 透明的权限请求和说明

## 扩展性

模块化设计支持：
- 添加新的监控指标
- 自定义数据分析算法
- 集成第三方监控工具
- 扩展数据导出格式

## 故障排除

### 常见问题

1. **监控无法启动**
   - 检查辅助功能权限是否已授予
   - 查看日志文件获取详细错误信息

2. **数据采集不准确**
   - 确认系统权限设置
   - 检查采集间隔配置

3. **数据库文件过大**
   - 调整数据保留天数
   - 执行数据清理操作

### 日志查看

```swift
// 获取日志文件路径
let logPath = Logger.getLogFilePath()

// 读取最近日志
let recentLogs = Logger.getRecentLogs(lines: 100)
```

## 更新日志

### v1.0.0 (2025-01-11)
- 初始版本发布
- 完整的系统监控功能
- SQLite 数据存储
- 权限管理系统
- 用户界面组件

## 贡献

欢迎提交 Issue 和 Pull Request 来改进系统监控模块。

## 许可证

本模块遵循与 BDoggy 主项目相同的许可证。
