//
//  DatabaseMigrationManager.swift
//  BDoggy
//
//  Created by K4 on 2025/1/11.
//  数据库迁移管理器
//

import Foundation
import SQLite3
import os.log

/// 数据库迁移管理器
/// 负责管理数据库版本升级和数据迁移
class DatabaseMigrationManager {
    
    private let database: OptimizedSystemMonitorDatabase
    private let logger = Logger.self
    
    // 当前数据库版本
    static let currentVersion = 2
    
    init(database: OptimizedSystemMonitorDatabase) {
        self.database = database
    }
    
    // MARK: - 迁移管理
    
    /// 执行数据库迁移
    func performMigration() -> Result<Void, DatabaseMigrationError> {
        let currentVersion = getCurrentDatabaseVersion()
        let targetVersion = Self.currentVersion
        
        guard currentVersion < targetVersion else {
            logger.info("数据库版本已是最新: \(currentVersion)")
            return .success(())
        }
        
        logger.info("开始数据库迁移: \(currentVersion) -> \(targetVersion)")
        
        // 创建备份
        switch createBackup() {
        case .success(let backupPath):
            logger.info("数据库备份创建成功: \(backupPath)")
        case .failure(let error):
            logger.error("创建数据库备份失败: \(error.localizedDescription)")
            return .failure(error)
        }
        
        // 执行迁移步骤
        for version in (currentVersion + 1)...targetVersion {
            switch executeMigrationStep(toVersion: version) {
            case .success():
                logger.info("迁移到版本 \(version) 成功")
            case .failure(let error):
                logger.error("迁移到版本 \(version) 失败: \(error.localizedDescription)")
                // 尝试恢复备份
                _ = restoreFromBackup()
                return .failure(error)
            }
        }
        
        logger.info("数据库迁移完成")
        return .success(())
    }
    
    /// 获取当前数据库版本
    private func getCurrentDatabaseVersion() -> Int {
        switch database.dbQueue.sync(execute: {
            let sql = "SELECT version_number FROM schema_versions ORDER BY version_number DESC LIMIT 1;"
            var statement: OpaquePointer?
            
            guard sqlite3_prepare_v2(database.db, sql, -1, &statement, nil) == SQLITE_OK else {
                return 0
            }
            
            defer { sqlite3_finalize(statement) }
            
            if sqlite3_step(statement) == SQLITE_ROW {
                return Int(sqlite3_column_int(statement, 0))
            }
            
            return 0
        }) {
        case .success(let version):
            return version
        case .failure:
            return 0
        }
    }
    
    /// 执行单个迁移步骤
    private func executeMigrationStep(toVersion version: Int) -> Result<Void, DatabaseMigrationError> {
        switch version {
        case 1:
            return migrateToVersion1()
        case 2:
            return migrateToVersion2()
        default:
            return .failure(.unsupportedVersion(version))
        }
    }
    
    // MARK: - 具体迁移步骤
    
    /// 迁移到版本1（初始版本）
    private func migrateToVersion1() -> Result<Void, DatabaseMigrationError> {
        return database.executeInTransaction {
            // 版本1是初始版本，只需要记录版本号
            let sql = "INSERT INTO schema_versions (version_number, description) VALUES (1, '初始数据库版本');"
            
            if !database.executeSQL(sql, description: "记录版本1") {
                throw DatabaseMigrationError.migrationFailed("记录版本1失败")
            }
        }.mapError { error in
            if let migrationError = error as? DatabaseMigrationError {
                return migrationError
            }
            return .migrationFailed(error.localizedDescription)
        }
    }
    
    /// 迁移到版本2（添加新字段和索引）
    private func migrateToVersion2() -> Result<Void, DatabaseMigrationError> {
        return database.executeInTransaction {
            // 添加新字段到现有表
            let alterStatements = [
                "ALTER TABLE app_info ADD COLUMN vendor_name TEXT;",
                "ALTER TABLE app_info ADD COLUMN install_date INTEGER;",
                "ALTER TABLE app_info ADD COLUMN last_updated INTEGER;",
                "ALTER TABLE system_info ADD COLUMN cpu_architecture TEXT;",
                "ALTER TABLE system_info ADD COLUMN kernel_version TEXT;",
                "ALTER TABLE system_info ADD COLUMN system_serial TEXT;"
            ]
            
            for statement in alterStatements {
                if !database.executeSQL(statement, description: "添加新字段") {
                    // 某些字段可能已存在，继续执行
                    logger.warning("执行语句可能失败（字段可能已存在）: \(statement)")
                }
            }
            
            // 添加新索引
            let indexStatements = [
                "CREATE INDEX IF NOT EXISTS idx_app_info_vendor ON app_info(vendor_name);",
                "CREATE INDEX IF NOT EXISTS idx_app_info_install_date ON app_info(install_date);",
                "CREATE INDEX IF NOT EXISTS idx_system_info_architecture ON system_info(cpu_architecture);"
            ]
            
            for statement in indexStatements {
                if !database.executeSQL(statement, description: "添加新索引") {
                    throw DatabaseMigrationError.migrationFailed("添加索引失败: \(statement)")
                }
            }
            
            // 迁移现有数据（如果需要）
            try migrateExistingDataToVersion2()
            
            // 记录版本
            let versionSQL = "INSERT INTO schema_versions (version_number, description) VALUES (2, '添加新字段和索引，优化数据结构');"
            if !database.executeSQL(versionSQL, description: "记录版本2") {
                throw DatabaseMigrationError.migrationFailed("记录版本2失败")
            }
        }.mapError { error in
            if let migrationError = error as? DatabaseMigrationError {
                return migrationError
            }
            return .migrationFailed(error.localizedDescription)
        }
    }
    
    /// 迁移现有数据到版本2
    private func migrateExistingDataToVersion2() throws {
        // 更新系统信息表的新字段
        let updateSystemInfoSQL = """
            UPDATE system_info 
            SET cpu_architecture = ?, kernel_version = ?, system_serial = ?
            WHERE cpu_architecture IS NULL;
        """
        
        var statement: OpaquePointer?
        guard sqlite3_prepare_v2(database.db, updateSystemInfoSQL, -1, &statement, nil) == SQLITE_OK else {
            throw DatabaseMigrationError.migrationFailed("准备系统信息更新语句失败")
        }
        
        defer { sqlite3_finalize(statement) }
        
        sqlite3_bind_text(statement, 1, SystemInfoCollector.getCPUArchitecture(), -1, nil)
        sqlite3_bind_text(statement, 2, SystemInfoCollector.getKernelVersion(), -1, nil)
        sqlite3_bind_text(statement, 3, SystemInfoCollector.getSystemSerial(), -1, nil)
        
        if sqlite3_step(statement) != SQLITE_DONE {
            throw DatabaseMigrationError.migrationFailed("更新系统信息失败")
        }
        
        logger.info("现有数据迁移到版本2完成")
    }
    
    // MARK: - 备份和恢复
    
    /// 创建数据库备份
    private func createBackup() -> Result<String, DatabaseMigrationError> {
        let databasePath = database.getDatabasePath()
        let backupPath = databasePath + ".backup.\(Int(Date().timeIntervalSince1970))"
        
        do {
            try FileManager.default.copyItem(atPath: databasePath, toPath: backupPath)
            return .success(backupPath)
        } catch {
            return .failure(.backupFailed(error.localizedDescription))
        }
    }
    
    /// 从备份恢复数据库
    private func restoreFromBackup() -> Result<Void, DatabaseMigrationError> {
        let databasePath = database.getDatabasePath()
        let backupDirectory = URL(fileURLWithPath: databasePath).deletingLastPathComponent()
        
        do {
            let files = try FileManager.default.contentsOfDirectory(at: backupDirectory, 
                                                                   includingPropertiesForKeys: nil)
            
            // 找到最新的备份文件
            let backupFiles = files.filter { $0.lastPathComponent.contains(".backup.") }
                                  .sorted { $0.lastPathComponent > $1.lastPathComponent }
            
            guard let latestBackup = backupFiles.first else {
                return .failure(.noBackupFound)
            }
            
            // 删除当前数据库文件
            try FileManager.default.removeItem(atPath: databasePath)
            
            // 恢复备份
            try FileManager.default.copyItem(at: latestBackup, 
                                           to: URL(fileURLWithPath: databasePath))
            
            logger.info("数据库已从备份恢复: \(latestBackup.lastPathComponent)")
            return .success(())
            
        } catch {
            return .failure(.restoreFailed(error.localizedDescription))
        }
    }
    
    /// 清理旧备份文件
    func cleanupOldBackups(keepCount: Int = 5) -> Result<Int, DatabaseMigrationError> {
        let databasePath = database.getDatabasePath()
        let backupDirectory = URL(fileURLWithPath: databasePath).deletingLastPathComponent()
        
        do {
            let files = try FileManager.default.contentsOfDirectory(at: backupDirectory,
                                                                   includingPropertiesForKeys: [.creationDateKey])
            
            let backupFiles = files.filter { $0.lastPathComponent.contains(".backup.") }
                                  .sorted { 
                                      let date1 = try? $0.resourceValues(forKeys: [.creationDateKey]).creationDate ?? Date.distantPast
                                      let date2 = try? $1.resourceValues(forKeys: [.creationDateKey]).creationDate ?? Date.distantPast
                                      return date1! > date2!
                                  }
            
            let filesToDelete = Array(backupFiles.dropFirst(keepCount))
            
            for file in filesToDelete {
                try FileManager.default.removeItem(at: file)
            }
            
            logger.info("清理了\(filesToDelete.count)个旧备份文件")
            return .success(filesToDelete.count)
            
        } catch {
            return .failure(.cleanupFailed(error.localizedDescription))
        }
    }
}

