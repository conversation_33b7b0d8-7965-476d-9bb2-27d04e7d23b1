-- =====================================================
-- BDoggy 优化数据库架构设计
-- 基于SQLite最佳实践重新设计的数据库结构
-- =====================================================

-- 启用外键约束
PRAGMA foreign_keys = ON;

-- =====================================================
-- 1. 系统信息表 (优化版)
-- =====================================================
CREATE TABLE IF NOT EXISTS system_info (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    device_model TEXT NOT NULL,
    cpu_name TEXT NOT NULL,
    cpu_core_count INTEGER NOT NULL,
    cpu_architecture TEXT NOT NULL,        -- 新增：CPU架构
    total_memory_mb INTEGER NOT NULL,      -- 改为MB单位，使用INTEGER
    total_disk_gb INTEGER NOT NULL,       -- 改为INTEGER
    macos_version TEXT NOT NULL,
    kernel_version TEXT,                   -- 新增：内核版本
    system_serial TEXT,                    -- 新增：系统序列号
    recorded_at INTEGER NOT NULL,         -- 统一使用INTEGER存储时间戳
    created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
    updated_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
);

-- =====================================================
-- 2. 应用信息表 (新增，规范化设计)
-- =====================================================
CREATE TABLE IF NOT EXISTS app_info (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    bundle_id TEXT NOT NULL UNIQUE,
    app_name TEXT NOT NULL,
    app_version TEXT,
    app_category TEXT,                     -- 应用分类
    vendor_name TEXT,                      -- 开发商
    install_date INTEGER,                 -- 安装日期
    last_updated INTEGER,                 -- 最后更新时间
    is_system_app BOOLEAN DEFAULT 0,      -- 是否系统应用
    created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
    updated_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
);

-- =====================================================
-- 3. 指标类型定义表 (新增)
-- =====================================================
CREATE TABLE IF NOT EXISTS metric_types (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    type_name TEXT NOT NULL UNIQUE,       -- cpu, memory, disk, battery, thermal, network
    description TEXT,
    unit TEXT,                            -- 单位
    data_type TEXT NOT NULL,              -- INTEGER, REAL, TEXT
    is_active BOOLEAN DEFAULT 1,
    created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
);

-- =====================================================
-- 4. 系统指标数据表 (重新设计，按类型分离)
-- =====================================================

-- CPU指标表
CREATE TABLE IF NOT EXISTS cpu_metrics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp INTEGER NOT NULL,
    usage_percent INTEGER NOT NULL,       -- 存储为0-10000，表示0.00%-100.00%
    user_percent INTEGER NOT NULL,
    system_percent INTEGER NOT NULL,
    idle_percent INTEGER NOT NULL,
    load_avg_1min REAL,
    load_avg_5min REAL,
    load_avg_15min REAL,
    core_count INTEGER NOT NULL,
    frequency_mhz INTEGER,
    created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
);

-- 内存指标表
CREATE TABLE IF NOT EXISTS memory_metrics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp INTEGER NOT NULL,
    usage_percent INTEGER NOT NULL,       -- 0-10000
    used_mb INTEGER NOT NULL,
    available_mb INTEGER NOT NULL,
    total_mb INTEGER NOT NULL,
    active_mb INTEGER NOT NULL,
    inactive_mb INTEGER NOT NULL,
    wired_mb INTEGER NOT NULL,
    compressed_mb INTEGER NOT NULL,
    swap_used_mb INTEGER NOT NULL,
    swap_total_mb INTEGER NOT NULL,
    memory_pressure INTEGER NOT NULL,     -- 0-100
    created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
);

-- 磁盘指标表
CREATE TABLE IF NOT EXISTS disk_metrics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp INTEGER NOT NULL,
    usage_percent INTEGER NOT NULL,       -- 0-10000
    used_gb INTEGER NOT NULL,
    free_gb INTEGER NOT NULL,
    total_gb INTEGER NOT NULL,
    read_ops_per_sec INTEGER,
    write_ops_per_sec INTEGER,
    read_bytes_per_sec INTEGER,
    write_bytes_per_sec INTEGER,
    created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
);

-- 电池指标表
CREATE TABLE IF NOT EXISTS battery_metrics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp INTEGER NOT NULL,
    level_percent INTEGER,                -- 0-100
    health_percent INTEGER,               -- 0-100
    cycle_count INTEGER,
    power_source_type TEXT NOT NULL,      -- Battery, AC Power, UPS
    is_charging BOOLEAN DEFAULT 0,
    time_remaining_minutes INTEGER,       -- 剩余时间（分钟）
    temperature_celsius REAL,
    voltage_mv INTEGER,                   -- 电压（毫伏）
    created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
);

-- 温度指标表
CREATE TABLE IF NOT EXISTS thermal_metrics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp INTEGER NOT NULL,
    cpu_temperature_celsius REAL,
    gpu_temperature_celsius REAL,
    fan_speed_rpm INTEGER,
    thermal_state TEXT,                   -- Normal, Fair, Serious, Critical
    created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
);

-- 网络指标表
CREATE TABLE IF NOT EXISTS network_metrics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp INTEGER NOT NULL,
    bytes_in INTEGER NOT NULL,
    bytes_out INTEGER NOT NULL,
    packets_in INTEGER NOT NULL,
    packets_out INTEGER NOT NULL,
    errors_in INTEGER DEFAULT 0,
    errors_out INTEGER DEFAULT 0,
    interface_name TEXT,
    created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
);

-- =====================================================
-- 5. 进程信息表 (新增，替代JSON存储)
-- =====================================================
CREATE TABLE IF NOT EXISTS process_snapshots (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp INTEGER NOT NULL,
    pid INTEGER NOT NULL,
    process_name TEXT NOT NULL,
    cpu_usage_percent INTEGER NOT NULL,   -- 0-10000
    memory_usage_mb INTEGER NOT NULL,
    app_id INTEGER,                       -- 关联app_info表
    parent_pid INTEGER,
    created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
    FOREIGN KEY (app_id) REFERENCES app_info(id) ON DELETE SET NULL
);

-- =====================================================
-- 6. 应用使用记录表 (优化版)
-- =====================================================
CREATE TABLE IF NOT EXISTS app_usage_sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    app_id INTEGER NOT NULL,
    session_start INTEGER NOT NULL,
    session_end INTEGER,
    duration_seconds INTEGER NOT NULL DEFAULT 0,
    peak_cpu_usage INTEGER NOT NULL DEFAULT 0,    -- 0-10000
    avg_cpu_usage INTEGER NOT NULL DEFAULT 0,     -- 0-10000
    peak_memory_mb INTEGER NOT NULL DEFAULT 0,
    avg_memory_mb INTEGER NOT NULL DEFAULT 0,
    switch_count INTEGER DEFAULT 1,               -- 切换次数
    is_active BOOLEAN DEFAULT 1,
    created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
    updated_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
    FOREIGN KEY (app_id) REFERENCES app_info(id) ON DELETE CASCADE
);

-- =====================================================
-- 7. 应用使用详细数据表 (存储时间序列数据)
-- =====================================================
CREATE TABLE IF NOT EXISTS app_usage_details (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id INTEGER NOT NULL,
    timestamp INTEGER NOT NULL,
    cpu_usage INTEGER NOT NULL,           -- 0-10000
    memory_usage_mb INTEGER NOT NULL,
    created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
    FOREIGN KEY (session_id) REFERENCES app_usage_sessions(id) ON DELETE CASCADE
);

-- =====================================================
-- 8. 系统事件表 (新增)
-- =====================================================
CREATE TABLE IF NOT EXISTS system_events (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    event_type TEXT NOT NULL,             -- startup, shutdown, sleep, wake, app_launch, app_quit
    event_timestamp INTEGER NOT NULL,
    app_id INTEGER,                       -- 如果是应用相关事件
    additional_data TEXT,                 -- JSON格式的额外数据
    created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
    FOREIGN KEY (app_id) REFERENCES app_info(id) ON DELETE SET NULL
);

-- =====================================================
-- 9. 配置表 (优化版)
-- =====================================================
CREATE TABLE IF NOT EXISTS monitor_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    config_category TEXT NOT NULL,       -- monitoring, ui, alerts, data_retention
    config_key TEXT NOT NULL,
    config_value TEXT NOT NULL,
    value_type TEXT NOT NULL,            -- string, integer, boolean, json
    description TEXT,
    is_user_configurable BOOLEAN DEFAULT 1,
    created_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
    updated_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now')),
    UNIQUE(config_category, config_key)
);

-- =====================================================
-- 10. 数据库版本表 (新增，支持迁移)
-- =====================================================
CREATE TABLE IF NOT EXISTS schema_versions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    version_number INTEGER NOT NULL UNIQUE,
    description TEXT,
    migration_script TEXT,
    applied_at INTEGER NOT NULL DEFAULT (strftime('%s', 'now'))
);

-- =====================================================
-- 索引定义 (优化查询性能)
-- =====================================================

-- 系统信息表索引
CREATE INDEX IF NOT EXISTS idx_system_info_recorded_at ON system_info(recorded_at);

-- 应用信息表索引
CREATE INDEX IF NOT EXISTS idx_app_info_bundle_id ON app_info(bundle_id);
CREATE INDEX IF NOT EXISTS idx_app_info_category ON app_info(app_category);
CREATE INDEX IF NOT EXISTS idx_app_info_system_app ON app_info(is_system_app);

-- CPU指标表索引
CREATE INDEX IF NOT EXISTS idx_cpu_metrics_timestamp ON cpu_metrics(timestamp);
CREATE INDEX IF NOT EXISTS idx_cpu_metrics_usage ON cpu_metrics(usage_percent);
CREATE INDEX IF NOT EXISTS idx_cpu_metrics_time_usage ON cpu_metrics(timestamp, usage_percent);

-- 内存指标表索引
CREATE INDEX IF NOT EXISTS idx_memory_metrics_timestamp ON memory_metrics(timestamp);
CREATE INDEX IF NOT EXISTS idx_memory_metrics_usage ON memory_metrics(usage_percent);
CREATE INDEX IF NOT EXISTS idx_memory_metrics_time_usage ON memory_metrics(timestamp, usage_percent);

-- 磁盘指标表索引
CREATE INDEX IF NOT EXISTS idx_disk_metrics_timestamp ON disk_metrics(timestamp);
CREATE INDEX IF NOT EXISTS idx_disk_metrics_usage ON disk_metrics(usage_percent);

-- 电池指标表索引
CREATE INDEX IF NOT EXISTS idx_battery_metrics_timestamp ON battery_metrics(timestamp);
CREATE INDEX IF NOT EXISTS idx_battery_metrics_level ON battery_metrics(level_percent);

-- 温度指标表索引
CREATE INDEX IF NOT EXISTS idx_thermal_metrics_timestamp ON thermal_metrics(timestamp);
CREATE INDEX IF NOT EXISTS idx_thermal_metrics_cpu_temp ON thermal_metrics(cpu_temperature_celsius);

-- 网络指标表索引
CREATE INDEX IF NOT EXISTS idx_network_metrics_timestamp ON network_metrics(timestamp);

-- 进程快照表索引
CREATE INDEX IF NOT EXISTS idx_process_snapshots_timestamp ON process_snapshots(timestamp);
CREATE INDEX IF NOT EXISTS idx_process_snapshots_pid ON process_snapshots(pid);
CREATE INDEX IF NOT EXISTS idx_process_snapshots_app_id ON process_snapshots(app_id);
CREATE INDEX IF NOT EXISTS idx_process_snapshots_cpu_usage ON process_snapshots(cpu_usage_percent);

-- 应用使用会话表索引
CREATE INDEX IF NOT EXISTS idx_app_usage_sessions_app_id ON app_usage_sessions(app_id);
CREATE INDEX IF NOT EXISTS idx_app_usage_sessions_start ON app_usage_sessions(session_start);
CREATE INDEX IF NOT EXISTS idx_app_usage_sessions_duration ON app_usage_sessions(duration_seconds);
CREATE INDEX IF NOT EXISTS idx_app_usage_sessions_app_start ON app_usage_sessions(app_id, session_start);

-- 应用使用详细数据表索引
CREATE INDEX IF NOT EXISTS idx_app_usage_details_session_id ON app_usage_details(session_id);
CREATE INDEX IF NOT EXISTS idx_app_usage_details_timestamp ON app_usage_details(timestamp);

-- 系统事件表索引
CREATE INDEX IF NOT EXISTS idx_system_events_type ON system_events(event_type);
CREATE INDEX IF NOT EXISTS idx_system_events_timestamp ON system_events(event_timestamp);
CREATE INDEX IF NOT EXISTS idx_system_events_app_id ON system_events(app_id);

-- 配置表索引
CREATE INDEX IF NOT EXISTS idx_monitor_config_category ON monitor_config(config_category);
CREATE INDEX IF NOT EXISTS idx_monitor_config_key ON monitor_config(config_key);

-- =====================================================
-- 触发器定义 (自动维护数据完整性)
-- =====================================================

-- 自动更新app_info表的updated_at字段
CREATE TRIGGER IF NOT EXISTS tr_app_info_updated_at
    AFTER UPDATE ON app_info
    FOR EACH ROW
BEGIN
    UPDATE app_info SET updated_at = strftime('%s', 'now') WHERE id = NEW.id;
END;

-- 自动更新app_usage_sessions表的updated_at字段
CREATE TRIGGER IF NOT EXISTS tr_app_usage_sessions_updated_at
    AFTER UPDATE ON app_usage_sessions
    FOR EACH ROW
BEGIN
    UPDATE app_usage_sessions SET updated_at = strftime('%s', 'now') WHERE id = NEW.id;
END;

-- 自动更新monitor_config表的updated_at字段
CREATE TRIGGER IF NOT EXISTS tr_monitor_config_updated_at
    AFTER UPDATE ON monitor_config
    FOR EACH ROW
BEGIN
    UPDATE monitor_config SET updated_at = strftime('%s', 'now') WHERE id = NEW.id;
END;

-- 自动计算应用使用会话的持续时间
CREATE TRIGGER IF NOT EXISTS tr_app_usage_sessions_duration
    AFTER UPDATE OF session_end ON app_usage_sessions
    FOR EACH ROW
    WHEN NEW.session_end IS NOT NULL
BEGIN
    UPDATE app_usage_sessions
    SET duration_seconds = NEW.session_end - NEW.session_start
    WHERE id = NEW.id;
END;

-- =====================================================
-- 视图定义 (简化常用查询)
-- =====================================================

-- 应用使用统计视图
CREATE VIEW IF NOT EXISTS v_app_usage_stats AS
SELECT
    ai.bundle_id,
    ai.app_name,
    ai.app_category,
    COUNT(aus.id) as session_count,
    SUM(aus.duration_seconds) as total_duration_seconds,
    AVG(aus.duration_seconds) as avg_duration_seconds,
    MAX(aus.peak_cpu_usage) as max_cpu_usage,
    AVG(aus.avg_cpu_usage) as avg_cpu_usage,
    MAX(aus.peak_memory_mb) as max_memory_mb,
    AVG(aus.avg_memory_mb) as avg_memory_mb,
    MIN(aus.session_start) as first_used,
    MAX(aus.session_start) as last_used
FROM app_info ai
LEFT JOIN app_usage_sessions aus ON ai.id = aus.app_id
GROUP BY ai.id, ai.bundle_id, ai.app_name, ai.app_category;

-- 系统性能概览视图
CREATE VIEW IF NOT EXISTS v_system_performance_overview AS
SELECT
    cm.timestamp,
    cm.usage_percent as cpu_usage,
    mm.usage_percent as memory_usage,
    dm.usage_percent as disk_usage,
    bm.level_percent as battery_level,
    tm.cpu_temperature_celsius,
    tm.thermal_state
FROM cpu_metrics cm
LEFT JOIN memory_metrics mm ON cm.timestamp = mm.timestamp
LEFT JOIN disk_metrics dm ON cm.timestamp = dm.timestamp
LEFT JOIN battery_metrics bm ON cm.timestamp = bm.timestamp
LEFT JOIN thermal_metrics tm ON cm.timestamp = tm.timestamp
ORDER BY cm.timestamp DESC;

-- 最近活跃应用视图
CREATE VIEW IF NOT EXISTS v_recent_active_apps AS
SELECT
    ai.bundle_id,
    ai.app_name,
    ai.app_category,
    aus.session_start,
    aus.session_end,
    aus.duration_seconds,
    aus.avg_cpu_usage,
    aus.avg_memory_mb
FROM app_usage_sessions aus
JOIN app_info ai ON aus.app_id = ai.id
WHERE aus.session_start >= strftime('%s', 'now', '-7 days')
ORDER BY aus.session_start DESC;

-- =====================================================
-- 初始数据插入
-- =====================================================

-- 插入指标类型定义
INSERT OR IGNORE INTO metric_types (type_name, description, unit, data_type) VALUES
('cpu', 'CPU使用率和负载指标', 'percent', 'INTEGER'),
('memory', '内存使用情况指标', 'MB', 'INTEGER'),
('disk', '磁盘使用情况指标', 'GB', 'INTEGER'),
('battery', '电池状态指标', 'percent', 'INTEGER'),
('thermal', '温度和散热指标', 'celsius', 'REAL'),
('network', '网络流量指标', 'bytes', 'INTEGER');

-- 插入默认配置
INSERT OR IGNORE INTO monitor_config (config_category, config_key, config_value, value_type, description, is_user_configurable) VALUES
-- 监控配置
('monitoring', 'enabled', 'true', 'boolean', '是否启用系统监控', 1),
('monitoring', 'collection_interval_seconds', '300', 'integer', '数据采集间隔（秒）', 1),
('monitoring', 'enable_cpu_monitoring', 'true', 'boolean', '启用CPU监控', 1),
('monitoring', 'enable_memory_monitoring', 'true', 'boolean', '启用内存监控', 1),
('monitoring', 'enable_disk_monitoring', 'true', 'boolean', '启用磁盘监控', 1),
('monitoring', 'enable_battery_monitoring', 'true', 'boolean', '启用电池监控', 1),
('monitoring', 'enable_thermal_monitoring', 'true', 'boolean', '启用温度监控', 1),
('monitoring', 'enable_network_monitoring', 'true', 'boolean', '启用网络监控', 1),
('monitoring', 'enable_app_tracking', 'false', 'boolean', '启用应用使用跟踪', 1),

-- 数据保留配置
('data_retention', 'max_retention_days', '30', 'integer', '数据保留天数', 1),
('data_retention', 'auto_cleanup_enabled', 'true', 'boolean', '自动清理过期数据', 1),
('data_retention', 'cleanup_interval_hours', '24', 'integer', '清理检查间隔（小时）', 1),
('data_retention', 'max_database_size_mb', '500', 'integer', '数据库最大大小（MB）', 1),

-- 性能配置
('performance', 'batch_insert_size', '100', 'integer', '批量插入大小', 0),
('performance', 'query_timeout_seconds', '30', 'integer', '查询超时时间（秒）', 0),
('performance', 'connection_pool_size', '5', 'integer', '连接池大小', 0),

-- UI配置
('ui', 'default_chart_days', '7', 'integer', '默认图表显示天数', 1),
('ui', 'refresh_interval_seconds', '60', 'integer', 'UI刷新间隔（秒）', 1),
('ui', 'show_system_apps', 'false', 'boolean', '显示系统应用', 1),

-- 警报配置
('alerts', 'cpu_threshold_percent', '80', 'integer', 'CPU使用率警报阈值', 1),
('alerts', 'memory_threshold_percent', '85', 'integer', '内存使用率警报阈值', 1),
('alerts', 'disk_threshold_percent', '90', 'integer', '磁盘使用率警报阈值', 1),
('alerts', 'temperature_threshold_celsius', '80', 'integer', '温度警报阈值', 1),
('alerts', 'battery_low_threshold_percent', '20', 'integer', '电池低电量警报阈值', 1);

-- 插入当前数据库版本
INSERT OR IGNORE INTO schema_versions (version_number, description) VALUES
(1, '初始优化数据库架构版本');

-- =====================================================
-- 数据库优化设置
-- =====================================================

-- 启用WAL模式以提高并发性能
PRAGMA journal_mode = WAL;

-- 设置同步模式为NORMAL以平衡性能和安全性
PRAGMA synchronous = NORMAL;

-- 设置缓存大小（页数）
PRAGMA cache_size = 10000;

-- 启用内存映射I/O
PRAGMA mmap_size = 268435456; -- 256MB

-- 设置临时存储为内存
PRAGMA temp_store = MEMORY;

-- 优化查询计划器
PRAGMA optimize;
