# BDoggy 优化数据库架构设计文档

## 概述

本文档描述了BDoggy项目数据库架构的重新设计，基于对现有架构的深入分析，采用SQLite最佳实践，优化了性能、可维护性和扩展性。

## 设计原则

### 1. 数据规范化
- 消除数据冗余
- 建立适当的表关系
- 使用外键约束保证数据完整性

### 2. 性能优化
- 按数据类型分表存储
- 优化数据类型选择
- 建立高效索引策略

### 3. 可扩展性
- 模块化表结构设计
- 支持数据库版本管理
- 预留扩展字段

### 4. 数据完整性
- 外键约束
- 触发器自动维护
- 数据验证机制

## 核心架构变更

### 原架构问题
1. **单表存储多类型指标** - metrics_log表包含20个字段，查询效率低
2. **数据类型不一致** - 时间戳存储方式混乱
3. **缺少索引优化** - 关键查询字段缺少索引
4. **JSON数据存储** - top_processes存储为TEXT，查询困难
5. **数据冗余** - app_name等字段重复存储

### 新架构优势
1. **按类型分表** - CPU、内存、磁盘等指标分别存储
2. **统一数据类型** - 时间戳统一使用INTEGER
3. **完善索引策略** - 覆盖所有常用查询模式
4. **规范化存储** - 进程信息独立表存储
5. **消除冗余** - 应用信息规范化管理

## 表结构设计

### 核心表

#### 1. system_info - 系统信息表
```sql
- id: INTEGER PRIMARY KEY (自增主键)
- device_model: TEXT (设备型号)
- cpu_name: TEXT (CPU名称)
- cpu_architecture: TEXT (CPU架构)
- total_memory_mb: INTEGER (总内存MB)
- macos_version: TEXT (系统版本)
```

#### 2. app_info - 应用信息表 (新增)
```sql
- id: INTEGER PRIMARY KEY
- bundle_id: TEXT UNIQUE (应用包ID)
- app_name: TEXT (应用名称)
- app_category: TEXT (应用分类)
- is_system_app: BOOLEAN (是否系统应用)
```

### 指标数据表 (分类存储)

#### 3. cpu_metrics - CPU指标表
```sql
- timestamp: INTEGER (时间戳)
- usage_percent: INTEGER (使用率，0-10000表示0.00%-100.00%)
- load_avg_1min: REAL (1分钟负载)
- core_count: INTEGER (核心数)
```

#### 4. memory_metrics - 内存指标表
```sql
- timestamp: INTEGER
- usage_percent: INTEGER (0-10000)
- used_mb: INTEGER (已使用MB)
- available_mb: INTEGER (可用MB)
- swap_used_mb: INTEGER (交换区使用MB)
```

#### 5. disk_metrics - 磁盘指标表
```sql
- timestamp: INTEGER
- usage_percent: INTEGER (0-10000)
- used_gb: INTEGER (已使用GB)
- free_gb: INTEGER (可用GB)
```

#### 6. battery_metrics - 电池指标表
```sql
- timestamp: INTEGER
- level_percent: INTEGER (电量百分比)
- health_percent: INTEGER (健康度)
- cycle_count: INTEGER (循环次数)
- is_charging: BOOLEAN (是否充电)
```

#### 7. thermal_metrics - 温度指标表
```sql
- timestamp: INTEGER
- cpu_temperature_celsius: REAL (CPU温度)
- gpu_temperature_celsius: REAL (GPU温度)
- fan_speed_rpm: INTEGER (风扇转速)
```

#### 8. network_metrics - 网络指标表
```sql
- timestamp: INTEGER
- bytes_in: INTEGER (入站字节)
- bytes_out: INTEGER (出站字节)
- packets_in: INTEGER (入站包数)
- packets_out: INTEGER (出站包数)
```

### 应用使用跟踪表

#### 9. app_usage_sessions - 应用使用会话表
```sql
- id: INTEGER PRIMARY KEY
- app_id: INTEGER (关联app_info.id)
- session_start: INTEGER (会话开始时间)
- session_end: INTEGER (会话结束时间)
- duration_seconds: INTEGER (持续时间)
- peak_cpu_usage: INTEGER (峰值CPU使用率)
- avg_memory_mb: INTEGER (平均内存使用)
```

#### 10. app_usage_details - 应用使用详细数据表
```sql
- session_id: INTEGER (关联app_usage_sessions.id)
- timestamp: INTEGER (时间戳)
- cpu_usage: INTEGER (CPU使用率)
- memory_usage_mb: INTEGER (内存使用MB)
```

#### 11. process_snapshots - 进程快照表
```sql
- timestamp: INTEGER
- pid: INTEGER (进程ID)
- process_name: TEXT (进程名)
- cpu_usage_percent: INTEGER (CPU使用率)
- memory_usage_mb: INTEGER (内存使用MB)
- app_id: INTEGER (关联应用ID)
```

### 辅助表

#### 12. system_events - 系统事件表
```sql
- event_type: TEXT (事件类型)
- event_timestamp: INTEGER (事件时间)
- app_id: INTEGER (相关应用ID)
- additional_data: TEXT (额外数据JSON)
```

#### 13. monitor_config - 配置表
```sql
- config_category: TEXT (配置分类)
- config_key: TEXT (配置键)
- config_value: TEXT (配置值)
- value_type: TEXT (值类型)
```

#### 14. schema_versions - 数据库版本表
```sql
- version_number: INTEGER (版本号)
- description: TEXT (版本描述)
- applied_at: INTEGER (应用时间)
```

## 索引策略

### 时间序列查询优化
```sql
-- 时间戳索引（所有指标表）
CREATE INDEX idx_cpu_metrics_timestamp ON cpu_metrics(timestamp);
CREATE INDEX idx_memory_metrics_timestamp ON memory_metrics(timestamp);

-- 复合索引（时间+使用率）
CREATE INDEX idx_cpu_metrics_time_usage ON cpu_metrics(timestamp, usage_percent);
```

### 应用查询优化
```sql
-- 应用相关索引
CREATE INDEX idx_app_info_bundle_id ON app_info(bundle_id);
CREATE INDEX idx_app_usage_sessions_app_start ON app_usage_sessions(app_id, session_start);
```

### 性能监控索引
```sql
-- 使用率查询索引
CREATE INDEX idx_cpu_metrics_usage ON cpu_metrics(usage_percent);
CREATE INDEX idx_memory_metrics_usage ON memory_metrics(usage_percent);
```

## 视图设计

### 1. v_app_usage_stats - 应用使用统计视图
提供应用使用的汇总统计信息，包括会话次数、总时长、平均CPU/内存使用等。

### 2. v_system_performance_overview - 系统性能概览视图
整合各类指标数据，提供系统整体性能视图。

### 3. v_recent_active_apps - 最近活跃应用视图
显示最近7天内的应用使用情况。

## 触发器机制

### 自动时间戳更新
```sql
-- 自动更新updated_at字段
CREATE TRIGGER tr_app_info_updated_at
    AFTER UPDATE ON app_info
    FOR EACH ROW
BEGIN
    UPDATE app_info SET updated_at = strftime('%s', 'now') WHERE id = NEW.id;
END;
```

### 自动计算持续时间
```sql
-- 自动计算会话持续时间
CREATE TRIGGER tr_app_usage_sessions_duration
    AFTER UPDATE OF session_end ON app_usage_sessions
    FOR EACH ROW
BEGIN
    UPDATE app_usage_sessions 
    SET duration_seconds = NEW.session_end - NEW.session_start 
    WHERE id = NEW.id;
END;
```

## 性能优化配置

### SQLite优化设置
```sql
PRAGMA journal_mode = WAL;        -- 启用WAL模式提高并发
PRAGMA synchronous = NORMAL;      -- 平衡性能和安全性
PRAGMA cache_size = 10000;        -- 设置缓存大小
PRAGMA mmap_size = 268435456;     -- 启用内存映射I/O
PRAGMA temp_store = MEMORY;       -- 临时存储使用内存
```

### 数据类型优化
- **时间戳**: 统一使用INTEGER存储Unix时间戳
- **百分比**: 使用INTEGER存储（0-10000表示0.00%-100.00%）
- **内存大小**: 使用INTEGER存储MB单位
- **磁盘大小**: 使用INTEGER存储GB单位

## 数据迁移策略

### 版本管理
- 使用schema_versions表跟踪数据库版本
- 每次架构变更记录迁移脚本
- 支持向前和向后兼容

### 迁移步骤
1. 备份现有数据
2. 创建新表结构
3. 数据转换和迁移
4. 验证数据完整性
5. 更新版本记录

## 监控和维护

### 自动清理机制
- 基于配置的数据保留策略
- 定期清理过期数据
- 数据库大小监控

### 性能监控
- 查询执行时间监控
- 索引使用情况分析
- 存储空间使用跟踪

这个优化的数据库架构将显著提升BDoggy的数据处理性能、查询效率和系统可维护性。
