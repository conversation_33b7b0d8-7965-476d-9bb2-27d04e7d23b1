//
//  DatabaseMigrationError.swift
//  BDoggy
//
//  Created by K4 on 2025/8/14.
//


import Foundation
import SQLite3
import os.log

// MARK: - 迁移错误类型
enum DatabaseMigrationError: Error, LocalizedError {
    case unsupportedVersion(Int)
    case migrationFailed(String)
    case backupFailed(String)
    case restoreFailed(String)
    case noBackupFound
    case cleanupFailed(String)
    
    var errorDescription: String? {
        switch self {
        case .unsupportedVersion(let version):
            return "不支持的数据库版本: \(version)"
        case .migrationFailed(let message):
            return "数据库迁移失败: \(message)"
        case .backupFailed(let message):
            return "创建备份失败: \(message)"
        case .restoreFailed(let message):
            return "恢复备份失败: \(message)"
        case .noBackupFound:
            return "未找到备份文件"
        case .cleanupFailed(let message):
            return "清理备份失败: \(message)"
        }
    }
}
