//
//  Constants.swift
//  BDoggy
//
//  Created by K4 on 2024/12/23.
//

import Foundation

enum Constants {
    static let cacheIconFolder = "icons"
    static let useIconKey = "iconName"
    static let allIcons = "allIcons"
    static let apiBaseURL = "http://localhost:3000"
    static let APP_NAME = "BDoggy"
    static let AppleUserIDKey = "AppleUserID"
    static let AppleUserFullNameKey = "AppleUserFullNameKey"
    static let AppleUserEmailKey = "AppleUserEmailKey"
    static let Notification_ChangeFrameKey = "dirName"
    static let ImageExtensions = ["png", "jpg", "jpeg"]
    static let JWT_SECRET = "90289687c489fe5b4b4570dcb4ab0adb48771c185e418b3be6b5ba86e6c17de65e036b0c1bf6eccb2850661a7909ab97a371b210a5e843adceb330bc4a41a9e8"

    // UserDefaults Keys
    static let LastDeviceIDKey = "LastDeviceID"
    static let HasLaunchedBeforeKey = "HasLaunchedBefore"
    static let totalMaxInterval: Int = 3300
    static let frameMinInterval: Int = 20
}

enum IconType: Codable {
    case system
    case web
    case custom
}
