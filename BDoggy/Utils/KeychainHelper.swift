//
//  KeychainHelper.swift
//  BDoggy
//
//  Created by K4 on 2025/2/24.
//

import Foundation
import Security

enum KeychainHelper {
    private static let service = "com.doggy.BDoggy"
    private static let account = "userAccountToken"

    // 保存 UUID 到 Keychain
    static func saveAppAccountToken(_ token: UUID) {
        let tokenString = token.uuidString
        let data = tokenString.data(using: .utf8)!

        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: account,
            kSecValueData as String: data
        ]

        SecItemDelete(query as CFDictionary) // 先删除旧的，防止重复存储
        SecItemAdd(query as CFDictionary, nil)
    }

    // 从 Keychain 获取 UUID
    static func getAppAccountToken() -> UUID? {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: account,
            kSecReturnData as String: true,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]

        var dataTypeRef: AnyObject?
        if SecItemCopyMatching(query as CFDictionary, &dataTypeRef) == errSecSuccess {
            if let data = dataTypeRef as? Data,
               let tokenString = String(data: data, encoding: .utf8),
               let token = UUID(uuidString: tokenString)
            {
                return token
            }
        }
        return nil
    }
}
