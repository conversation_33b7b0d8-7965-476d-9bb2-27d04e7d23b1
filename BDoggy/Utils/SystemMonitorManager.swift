////
////  SystemMonitorManager.swift
////  BDoggy
////
////  Created by System Monitor on 2024/12/11.
////
//
//import AppKit
//import Foundation
//
///// 系统监控管理器 - 负责协调数据采集、存储和定时任务
//
//class SystemMonitorManager: ObservableObject {
//    static let shared = SystemMonitorManager()
//    
//    @Published var isMonitoring = false
//    @Published var config = MonitoringConfig()
//    @Published var lastCollectionTime: Date?
//    @Published var collectionCount: Int = 0
//    @Published var errorMessage: String?
//    
//    private var collectionTimer: Timer?
//    private let database = SystemMonitorDatabase.shared
//    private var currentActiveApp: (bundleID: String?, name: String?, startTime: Date)?
//    
//    private init() {
//        loadConfiguration()
//        setupAppTrackingIfNeeded()
//    }
//    
//    deinit {
//        stopMonitoring()
//    }
//    
//    // MARK: - 监控控制
//    
//    /// 开始监控
//    func startMonitoring() {
//        guard !isMonitoring else { return }
//        
//        Logger.info("开始系统监控，采集间隔: \(config.collectionInterval.displayName)")
//        
//        // 首次采集系统信息（静态信息）
//        collectSystemInfoIfNeeded()
//        
//        // 立即采集一次动态数据
//        collectSystemMetrics()
//        
//        // 启动定时器
//        startCollectionTimer()
//        
//        isMonitoring = true
//        saveConfiguration()
//    }
//    
//    /// 停止监控
//    func stopMonitoring() {
//        guard isMonitoring else { return }
//        
//        Logger.info("停止系统监控")
//        
//        collectionTimer?.invalidate()
//        collectionTimer = nil
//        
//        isMonitoring = false
//        saveConfiguration()
//    }
//    
//    /// 手动采集一次数据
//    func collectOnce() {
//        collectSystemMetrics()
//    }
//    
//    // MARK: - 定时任务管理
//    
//    private func startCollectionTimer() {
//        collectionTimer?.invalidate()
//        
//        collectionTimer = Timer.scheduledTimer(withTimeInterval: config.collectionInterval.timeInterval, repeats: true) { [weak self] _ in
//            Task { @MainActor in
//                self?.collectSystemMetrics()
//            }
//        }
//    }
//    
//    private func restartTimerIfNeeded() {
//        if isMonitoring {
//            startCollectionTimer()
//        }
//    }
//    
//    // MARK: - 数据采集
//    
//    private func collectSystemInfoIfNeeded() {
//        // 检查是否已有系统信息记录
//        if database.getSystemInfo() == nil {
//            let systemInfo = SystemInfo()
//            if database.insertSystemInfo(systemInfo) {
//                Logger.info("系统信息已记录")
//            } else {
//                Logger.error("系统信息记录失败")
//            }
//        }
//    }
//    
//    private func collectSystemMetrics() {
//        do {
//            let metrics = SystemMetrics()
//            
//            if database.insertSystemMetrics(metrics) {
//                lastCollectionTime = Date()
//                collectionCount += 1
//                errorMessage = nil
//                Logger.debug("系统指标采集成功 #\(collectionCount)")
//            } else {
//                throw SystemMonitorError.dataCollectionFailed("数据库写入失败")
//            }
//            
//        } catch {
//            errorMessage = error.localizedDescription
//            Logger.error("系统指标采集失败: \(error)")
//        }
//    }
//    
//    // MARK: - 应用跟踪
//    
//    private func setupAppTrackingIfNeeded() {
//        guard config.enableAppTracking else { return }
//        
//        // 监听应用切换事件
//        NSWorkspace.shared.notificationCenter.addObserver(
//            self,
//            selector: #selector(activeApplicationChanged),
//            name: NSWorkspace.didActivateApplicationNotification,
//            object: nil
//        )
//        
//        // 记录当前活跃应用
//        if let frontmostApp = NSWorkspace.shared.frontmostApplication {
//            currentActiveApp = (
//                bundleID: frontmostApp.bundleIdentifier,
//                name: frontmostApp.localizedName,
//                startTime: Date()
//            )
//        }
//    }
//    
//    @objc private func activeApplicationChanged(_ notification: Notification) {
//        guard config.enableAppTracking else { return }
//        
//        // 结束上一个应用的使用记录
//        if let currentApp = currentActiveApp {
//            let endTime = Date()
//            let record = AppUsageRecord(
//                bundleID: currentApp.bundleID ?? "unknown",
//                appName: currentApp.name ?? "Unknown App",
//                startTime: currentApp.startTime
//            ).withEndTime(endTime)
//            
//            // 这里需要实现 AppUsageRecord 的数据库插入方法
//            Logger.debug("应用使用记录: \(record.appName) - \(record.duration)秒")
//        }
//        
//        // 开始新应用的使用记录
//        if let userInfo = notification.userInfo,
//           let app = userInfo[NSWorkspace.applicationUserInfoKey] as? NSRunningApplication
//        {
//            currentActiveApp = (
//                bundleID: app.bundleIdentifier,
//                name: app.localizedName,
//                startTime: Date()
//            )
//        }
//    }
//    
//    // MARK: - 配置管理
//    
//    /// 更新监控配置
//    func updateConfig(_ newConfig: MonitoringConfig) {
//        let oldInterval = config.collectionInterval
//        config = newConfig
//        
//        // 如果采集间隔改变，重启定时器
//        if oldInterval != newConfig.collectionInterval {
//            restartTimerIfNeeded()
//        }
//        
//        // 如果应用跟踪设置改变
//        if config.enableAppTracking {
//            setupAppTrackingIfNeeded()
//        } else {
//            NSWorkspace.shared.notificationCenter.removeObserver(self)
//            currentActiveApp = nil
//        }
//        
//        saveConfiguration()
//    }
//    
//    private func loadConfiguration() {
//        // 从 UserDefaults 加载配置
//        if let data = UserDefaults.standard.data(forKey: "SystemMonitorConfig"),
//           let savedConfig = try? JSONDecoder().decode(MonitoringConfig.self, from: data)
//        {
//            config = savedConfig
//        }
//        
//        // 加载监控状态
//        isMonitoring = UserDefaults.standard.bool(forKey: "SystemMonitorEnabled")
//        
//        if isMonitoring {
//            startMonitoring()
//        }
//    }
//    
//    private func saveConfiguration() {
//        // 保存配置到 UserDefaults
//        if let data = try? JSONEncoder().encode(config) {
//            UserDefaults.standard.set(data, forKey: "SystemMonitorConfig")
//        }
//        
//        UserDefaults.standard.set(isMonitoring, forKey: "SystemMonitorEnabled")
//    }
//    
//    // MARK: - 数据管理
//    
//    /// 清理过期数据
//    func cleanupOldData() {
//        let success = database.cleanupOldData(olderThanDays: config.dataRetentionDays)
//        if success {
//            Logger.info("过期数据清理完成")
//        } else {
//            Logger.error("过期数据清理失败")
//        }
//    }
//    
//    /// 清除所有数据
//    func clearAllData() -> Bool {
//        let success = database.clearAllData()
//        if success {
//            collectionCount = 0
//            lastCollectionTime = nil
//            Logger.info("所有监控数据已清除")
//        }
//        return success
//    }
//    
//    /// 获取数据库大小
//    func getDatabaseSize() -> String {
//        let sizeBytes = database.getDatabaseSize()
//        let formatter = ByteCountFormatter()
//        formatter.allowedUnits = [.useMB, .useKB]
//        formatter.countStyle = .file
//        return formatter.string(fromByteCount: sizeBytes)
//    }
//    
//    // MARK: - 数据查询接口
//    
//    /// 获取CPU使用率趋势（用于图表显示）
//    func getCPUUsageTrend(hours: Int = 24) -> [(Date, Double)] {
//        return database.getCPUUsageTrend(hours: hours)
//    }
//    
//    /// 获取内存使用率趋势
//    func getMemoryUsageTrend(hours: Int = 24) -> [(Date, Double)] {
//        return database.getMemoryUsageTrend(hours: hours)
//    }
//    
//    /// 获取当前系统状态摘要
//    func getCurrentSystemSummary() -> [String: Any] {
//        let systemInfo = SystemInfoCollector.getCompleteSystemInfo()
//        let cpuMetrics = SystemMetricsCollector.getCPUMetrics()
//        let memoryMetrics = SystemMetricsCollector.getMemoryMetrics()
//        let diskMetrics = SystemMetricsCollector.getDiskMetrics()
//        let batteryMetrics = SystemMetricsCollector.getBatteryMetrics()
//        
//        return [
//            "systemInfo": systemInfo,
//            "cpuUsage": cpuMetrics.usage,
//            "memoryUsage": memoryMetrics.usagePercent,
//            "diskUsage": diskMetrics.usagePercent,
//            "batteryLevel": batteryMetrics.level ?? -1,
//            "powerSource": batteryMetrics.powerSourceType,
//            "isMonitoring": isMonitoring,
//            "collectionCount": collectionCount,
//            "lastCollection": lastCollectionTime?.timeIntervalSince1970 ?? 0
//        ]
//    }
//}
//
//// MARK: - 权限检查扩展
//
//extension SystemMonitorManager {
//    /// 检查是否有访问活跃应用的权限
//    func checkActiveAppPermission() -> Bool {
//        // 尝试获取当前活跃应用
//        let activeApp = NSWorkspace.shared.frontmostApplication
//        return activeApp != nil
//    }
//    
//    /// 引导用户开启辅助功能权限
//    func requestAccessibilityPermission() {
//        let alert = NSAlert()
//        alert.messageText = "需要辅助功能权限"
//        alert.informativeText = "为了监控活跃应用，需要在系统偏好设置中开启辅助功能权限。"
//        alert.addButton(withTitle: "打开系统偏好设置")
//        alert.addButton(withTitle: "取消")
//        
//        if alert.runModal() == .alertFirstButtonReturn {
//            // 打开系统偏好设置的辅助功能页面
//            let url = URL(string: "x-apple.systempreferences:com.apple.preference.security?Privacy_Accessibility")!
//            NSWorkspace.shared.open(url)
//        }
//    }
//}
