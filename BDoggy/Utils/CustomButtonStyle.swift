//
//  CustomButtonStyle.swift
//  BDoggy
//
//  Created by K4 on 2024/12/20.
//
import SwiftUI
struct CustomButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .background(configuration.isPressed ? Color("theme") : Color("theme")) // 根据按钮状态切换背景颜色
            .foregroundColor(.white)
            .cornerRadius(8)
            .shadow(color: configuration.isPressed ? .clear : Color(.sRGBLinear, white: 0, opacity: 0.33), radius: 5, x: 0, y: 3) // 添加阴影
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0) // 按下时缩小按钮
            .animation(.easeInOut, value: configuration.isPressed)
    }
}

struct NoBorderButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .padding(0) // 移除内边距
            .background(Color.clear) // 设置背景为透明，避免默认背景色
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(Color.clear, lineWidth: 0) // 使用透明边框或设置线宽为0
            )
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0) // 可选：添加按下效果
    }
}
