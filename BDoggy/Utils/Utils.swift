//
//  Utils.swift
//  BDoggy
//
//  Created by K4 on 2025/2/14.
//

import Foundation

class Utils {
    /// 对字符串进行 URL 编码
    /// - Parameter input: 需要编码的字符串
    /// - Returns: URL 编码后的字符串，如果编码失败则返回 ""
    static func urlEncode(_ input: String) -> String {
        var allowedCharacterSet = CharacterSet.urlQueryAllowed
        // 根据 RFC 3986，`+` 和 `=` 应该被编码
        allowedCharacterSet.remove(charactersIn: ";!<\"=#>$?%@&['\\(])^*`+{,|/}:")
        return input.addingPercentEncoding(withAllowedCharacters: allowedCharacterSet) ?? ""
    }

    /// 对字符串进行 URL 解码
    /// - Parameter string: 需要解码的字符串
    /// - Returns: URL 解码后的字符串，如果解码失败则返回 ""
    static func urlDecode(_ input: String) -> String {
        return input.removingPercentEncoding ?? ""
    }

    // TODO: -
    static func isRemote(urlString: String) -> Bool {
        guard let url = URL(string: urlString) else { return false }
        return url.scheme == "http" || url.scheme == "https"
    }

    // TODO: -
    static func urlExtension(urlString: String) -> String? {
        if isRemote(urlString: urlString) {
            return URL(string: urlString)?.pathExtension.lowercased()
        } else {
            return (urlString as NSString).pathExtension.lowercased()
        }
    }

    // TODO: -
    static func urlPathWithoutExtension(urlString: String) -> String? {
        guard let url = URL(string: urlString) else { return nil }

        if isRemote(urlString: urlString) {
            // 对于远程URL，删除最后一个组件的扩展名
            let pathWithoutExtension = url.deletingPathExtension().path
            return pathWithoutExtension
        } else {
            // 对于本地路径，使用 NSString 方法
            return (urlString as NSString).deletingPathExtension
        }
    }
    
    
     static func formatFileSize(_ bytes: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.countStyle = .file
        return formatter.string(fromByteCount: bytes)
    }
    
     static func formatInterval(_ seconds: TimeInterval) -> String {
        if seconds < 60 {
            return "\(Int(seconds))秒"
        } else if seconds < 3600 {
            return "\(Int(seconds / 60))分钟"
        } else {
            return "\(Int(seconds / 3600))小时"
        }
    }
    
     static func formatTimeAgo(_ seconds: TimeInterval) -> String {
        if seconds < 60 {
            return "\(Int(seconds))秒"
        } else if seconds < 3600 {
            return "\(Int(seconds / 60))分钟"
        } else if seconds < 86400 {
            return "\(Int(seconds / 3600))小时"
        } else {
            return "\(Int(seconds / 86400))天"
        }
    }
}
