//
//  SystemUsageMonitor.swift
//  BDoggy
//
//  Created by K4 on 2024/12/9.
//

import Darwin
import Foundation
import IOKit
import IOKit.ps
import MachO
import SystemConfiguration // 添加 SystemConfiguration 框架

class SystemUsageMonitor {
    static let shared = SystemUsageMonitor()
    static let notificationName = Notification.Name("SystemUsageMonitor")

    private var isMonitoring = false
    private var previousUserTicks: Int = 0
    private var previousSystemTicks: Int = 0
    private var previousIdleTicks: Int = 0
    private var previousTotalTicks: Int = 0
    private var batteryInfoCount: Int = 13
    private var previousBatteryInfo: [String: Any] = [:]
    
    // 添加网络监控相关变量
    private var previousBytesIn: UInt64 = 0
    private var previousBytesOut: UInt64 = 0
    private var previousNetworkTime: Date = .init()
    private var networkInfo: [String: Any] = [:]

    private let queue = DispatchQueue(label: "com.bdoggy.systemUsageMonitor", qos: .background)

    private init() {}

    /// 启动 CPU 监控
    func startMonitoring(interval: TimeInterval = 5.0) {
        guard !isMonitoring else { return }
        isMonitoring = true

        queue.async {
            while self.isMonitoring {
                self.fetchCPUUsage()
                Thread.sleep(forTimeInterval: interval)
            }
        }
    }

    /// 停止 CPU 监控
    func stopMonitoring() {
        isMonitoring = false
    }

    /// 停止 CPU 监控
    func restartMonitoring() {
        isMonitoring = true
    }
    
    /// 获取所有应用的内存使用情况
    private func getAllAppsMemoryUsage() -> [String: Double] {
        var memoryInfo = [String: Double]()
        let bytesToGB = { (bytes: Double) -> Double in
            return bytes / (1024 * 1024 * 1024)
        }
        
        // 获取所有进程 ID
        let bufferSize = MemoryLayout<pid_t>.stride * 1024
        let buffer = UnsafeMutablePointer<pid_t>.allocate(capacity: bufferSize)
        defer { buffer.deallocate() }
        
        let count = proc_listallpids(buffer, Int32(bufferSize))
        guard count > 0 else { return [:] }
        
        // 遍历所有进程
        for i in 0 ..< count {
            let pid = buffer[Int(i)]
            var taskInfo = task_vm_info_data_t()
            var count = mach_msg_type_number_t(MemoryLayout<task_vm_info>.size) / 4
            var name = [CChar](repeating: 0, count: 1024)
            
            // 获取进程名称
            proc_name(pid, &name, UInt32(name.count))
            let processName = String(cString: name)
            guard !processName.isEmpty else { continue }
            
            // 获取进程的内存使用情况
            var task: task_t = mach_task_self_
            let kr = task_for_pid(mach_task_self_, pid, &task)
            guard kr == KERN_SUCCESS else { continue }
            
            let result = withUnsafeMutablePointer(to: &taskInfo) {
                $0.withMemoryRebound(to: integer_t.self, capacity: Int(count)) {
                    task_info(task, task_flavor_t(TASK_VM_INFO), $0, &count)
                }
            }
            
            if result == KERN_SUCCESS {
                let memoryUsage = bytesToGB(Double(taskInfo.phys_footprint))
                memoryInfo[processName] = memoryUsage
            }
        }
        
        return memoryInfo
    }

    /// 获取当前内存使用情况（同步方法）
    /// 返回的数据单位均为 GB
    /// - totalMemory: 系统总物理内存
    /// - usedMemory: 系统已使用内存（活跃 + 有线内存）
    /// - availableMemory: 系统可用内存（空闲 + 不活跃内存）
    /// - activeMemory: 活跃内存（正在使用的内存）
    /// - inactiveMemory: 不活跃内存（最近未使用的内存）
    /// - wiredMemory: 有线内存（系统核心占用，不可释放）
    /// - freeMemory: 空闲内存
    /// - compressedMemory: 压缩内存
    /// - appMemory: 当前应用占用内存
    func getCurrentMemoryUsage() -> [String: Double] {
        var pageSize: vm_size_t = 0
        var hostInfo = vm_statistics64_data_t()
        var count = mach_msg_type_number_t(MemoryLayout<vm_statistics64_data_t>.size / MemoryLayout<integer_t>.size)
        
        let hostPort = mach_host_self()
        let kerr = host_page_size(hostPort, &pageSize)
        
        if kerr != KERN_SUCCESS {
            print("获取页面大小失败: \(kerr)")
            return [:]
        }
        
        let result = withUnsafeMutablePointer(to: &hostInfo) {
            $0.withMemoryRebound(to: integer_t.self, capacity: Int(count)) {
                host_statistics64(hostPort, HOST_VM_INFO64, $0, &count)
            }
        }
        
        if result != KERN_SUCCESS {
            print("获取内存统计信息失败: \(result)")
            return [:]
        }
        
        // 计算内存使用情况（转换为 GB）
        let bytesToGB = { (bytes: Double) -> Double in
            return bytes / (1024 * 1024 * 1024)
        }
        
        let freeMemory = bytesToGB(Double(hostInfo.free_count) * Double(pageSize))
        let activeMemory = bytesToGB(Double(hostInfo.active_count) * Double(pageSize))
        let inactiveMemory = bytesToGB(Double(hostInfo.inactive_count) * Double(pageSize))
        let wiredMemory = bytesToGB(Double(hostInfo.wire_count) * Double(pageSize))
        let compressedMemory = bytesToGB(Double(hostInfo.compressor_page_count) * Double(pageSize))
        
        // 计算总物理内存
        let totalMemory = bytesToGB(Double(ProcessInfo.processInfo.physicalMemory))
        
        // 计算已使用内存
        let usedMemory = activeMemory + wiredMemory
        
        // 计算可用内存
        let availableMemory = freeMemory + inactiveMemory
        
        // 获取当前应用内存使用情况
        var taskInfo = task_vm_info_data_t()
        var taskInfoCount = mach_msg_type_number_t(MemoryLayout<task_vm_info>.size) / 4
        let taskResult = withUnsafeMutablePointer(to: &taskInfo) {
            $0.withMemoryRebound(to: integer_t.self, capacity: Int(taskInfoCount)) {
                task_info(mach_task_self_, task_flavor_t(TASK_VM_INFO), $0, &taskInfoCount)
            }
        }
        
        let appMemory = taskResult == KERN_SUCCESS ? bytesToGB(Double(taskInfo.phys_footprint)) : 0.0
        
        // 计算使用百分比
        let usedPercentage = (usedMemory / totalMemory) * 100.0
        
        // 计算内存压力
        let memoryPressure = calculateMemoryPressure(
            totalMemory: totalMemory,
            compressedMemory: compressedMemory,
            freeMemory: freeMemory,
            inactiveMemory: inactiveMemory
        )
        
        return [
            "totalMemory": totalMemory,
            "usedMemory": usedMemory,
            "availableMemory": availableMemory,
            "activeMemory": activeMemory,
            "inactiveMemory": inactiveMemory,
            "wiredMemory": wiredMemory,
            "freeMemory": freeMemory,
            "compressedMemory": compressedMemory,
            "appMemory": appMemory,
            "usedMemoryPercentage": usedPercentage,
            "memoryPressure": memoryPressure // 添加内存压力指标
        ]
    }
    
    /// 计算内存压力百分比
    /// - Parameters:
    ///   - totalMemory: 总物理内存（GB）
    ///   - compressedMemory: 压缩内存（GB）
    ///   - freeMemory: 空闲内存（GB）
    ///   - inactiveMemory: 不活跃内存（GB）
    /// - Returns: 内存压力百分比 (0-100)
    private func calculateMemoryPressure(
        totalMemory: Double,
        compressedMemory: Double,
        freeMemory: Double,
        inactiveMemory: Double
    ) -> Double {
        // 计算可用内存比例
        let availableRatio = (freeMemory + inactiveMemory) / totalMemory
        
        // 计算压缩内存比例
        let compressionRatio = compressedMemory / totalMemory
        
        // 内存压力计算公式：
        // 1. 当可用内存比例低于 20% 时，压力开始增加
        // 2. 压缩内存比例越高，表示压力越大
        // 3. 结果范围：0-100，数值越大表示压力越大
        
        let pressureFromAvailable = max(0, (0.2 - availableRatio) * 500) // 可用内存少于20%时开始产生压力
        let pressureFromCompression = compressionRatio * 100 // 压缩内存带来的压力
        
        // 综合压力计算，取两种压力的加权平均
        let pressure = (pressureFromAvailable * 0.5 + pressureFromCompression * 0.5)
        
        // 确保结果在 0-100 范围内
        return min(100, max(0, pressure))
    }
    
    /// 获取 CPU 使用率
    private func fetchCPUUsage() {
        let HOST_CPU_LOAD_INFO_COUNT = MemoryLayout<host_cpu_load_info_data_t>.stride / MemoryLayout<integer_t>.stride
        var count = mach_msg_type_number_t(HOST_CPU_LOAD_INFO_COUNT)
        var loadInfo = host_cpu_load_info()

        let result = withUnsafeMutablePointer(to: &loadInfo) {
            $0.withMemoryRebound(to: integer_t.self, capacity: Int(count)) {
                host_statistics(mach_host_self(), HOST_CPU_LOAD_INFO, $0, &count)
            }
        }

        guard result == KERN_SUCCESS else {
            print("Failed to fetch CPU load info")
            return
        }

        let userTicks = Int(loadInfo.cpu_ticks.0)
        let systemTicks = Int(loadInfo.cpu_ticks.1)
        let idleTicks = Int(loadInfo.cpu_ticks.2)
        let niceTicks = Int(loadInfo.cpu_ticks.3)

        let totalTicks = userTicks + systemTicks + idleTicks + niceTicks
        let userDelta = userTicks - previousUserTicks
        let systemDelta = systemTicks - previousSystemTicks
        let idleDelta = idleTicks - previousIdleTicks
        let totalDelta = totalTicks - previousTotalTicks

        previousUserTicks = userTicks
        previousSystemTicks = systemTicks
        previousIdleTicks = idleTicks
        previousTotalTicks = totalTicks

        let userUsage = totalDelta > 0 ? Double(userDelta) / Double(totalDelta) * 100.0 : 0.0
        let systemUsage = totalDelta > 0 ? Double(systemDelta) / Double(totalDelta) * 100.0 : 0.0
        let totalUsage = totalDelta > 0 ? (1.0 - Double(idleDelta) / Double(totalDelta)) * 100.0 : 0.0
        let idleUsage = totalDelta > 0 ? Double(idleDelta) / Double(totalDelta) * 100.0 : 0.0

        // 通过通知发送 CPU 使用率
        let usageData: [String: Any] = ["user": userUsage, "system": systemUsage, "total": totalUsage, "idle": idleUsage]
        
        // 获取内存数据并合并到一个通知中
        let memoryData = getCurrentMemoryUsage()
        var combinedData = usageData
        for (key, value) in memoryData {
            combinedData[key] = value
        }
         
        batteryInfoCount += 1
        if batteryInfoCount > 12 {
            batteryInfoCount = 0
            // 添加电池信息
            previousBatteryInfo = getBatteryInfo()
            for (key, value) in previousBatteryInfo {
                combinedData[key] = value
            }
        } else {
            for (key, value) in previousBatteryInfo {
                combinedData[key] = value
            }
        }
        
        // 添加网络信息
        let networkData = getNetworkInfo()
        for (key, value) in networkData {
            combinedData[key] = value
        }
        
        DispatchQueue.main.async {
            NotificationCenter.default.post(name: SystemUsageMonitor.notificationName, object: nil, userInfo: combinedData)
        }
    }

    /// 获取电池详细信息
    /// - Returns: 包含电池信息的字典
    ///   - isCharging: 是否正在充电
    ///   - percentage: 电池电量百分比
    ///   - timeRemaining: 剩余使用时间（分钟）
    ///   - timeToCharge: 充满需要的时间（分钟）
    ///   - isPluggedIn: 是否连接电源
    ///   - cycleCount: 电池循环次数
    ///   - maxCapacity: 电池最大容量百分比
    ///   - temperature: 电池温度（摄氏度）
    private func getBatteryInfo() -> [String: Any] {
        var batteryInfo: [String: Any] = [:]
        
        let smartBattery = IOServiceGetMatchingService(kIOMainPortDefault,
                                                       IOServiceMatching("AppleSmartBattery"))
        defer { IOServiceClose(smartBattery) }
//        defer { IOObjectRelease(smartBattery) }
        
        guard smartBattery > 0 else { return [:] }
        var properties: Unmanaged<CFMutableDictionary>?
           
        guard IORegistryEntryCreateCFProperties(smartBattery, &properties, kCFAllocatorDefault, 0) == KERN_SUCCESS,
              let props = properties?.takeRetainedValue() as? [String: Any]
        else {
            return [:]
        }
        
        // 获取电源信息
        var powerSourceState = ""
        if let powerSourcesInfo = IOPSCopyPowerSourcesInfo()?.takeRetainedValue(),
           let sourcesList = IOPSCopyPowerSourcesList(powerSourcesInfo)?.takeRetainedValue() as? [CFDictionary]
        {
            for source in sourcesList {
                if let sourceInfo = source as? [String: Any] {
                    // 获取电源来源（Battery / AC Power）
                    if let state = sourceInfo[kIOPSPowerSourceStateKey as String] as? String {
                        powerSourceState = state
//                        Logger.debug("电源:\(state)")
                    }
                }
            }
        }
        // 基本信息
        let isCharging = (props["IsCharging"] as? Bool) ?? false
        let percentage = (props["CurrentCapacity"] as? Int) ?? 0
        let isPluggedIn = (props["ExternalConnected"] as? Bool) ?? false
        let cycleCount = (props["CycleCount"] as? Int) ?? 0
        let maxCapacity = (props["MaxCapacity"] as? Int) ?? 0
        let designCapacity = (props["DesignCapacity"] as? Int) ?? 1
        let temperature = (props["Temperature"] as? Double) ?? 0
        
        // 额外的电池信息
        let designCycleCount = (props["DesignCycleCount"] as? Int) ?? 1000 // 设计循环次数
        let timeRemaining = (props["TimeRemaining"] as? Int) ?? 0 // 剩余时间（分钟）
        
        // 计算电池健康度
        let maxCapacityPercentage = (Double(maxCapacity) / Double(designCapacity)) * 100.0
        
        // 计算循环寿命百分比
        let cycleLifePercentage = min(100.0, (Double(cycleCount) / Double(designCycleCount)) * 100.0)
        
        batteryInfo = [
            "isCharging": isCharging,
            "percentage": Double(percentage),
            "isPluggedIn": isPluggedIn,
            "cycleCount": Double(cycleCount),
            "maxCapacityPercentage": maxCapacityPercentage,
            "temperature": temperature / 100.0, // 转换为摄氏度
            "timeRemaining": Double(timeRemaining),
            "cycleLifePercentage": cycleLifePercentage,
            "designCycleCount": Double(designCycleCount),
            "designCapacity": Double(designCapacity),
            "maxCapacity": Double(maxCapacity),
            "powerSourceState": powerSourceState
        ]
        
        return batteryInfo
    }
    
    /// 获取网络信息
    /// - Returns: 包含网络信息的字典
    ///   - ipAddress: 设备 IP 地址
    ///   - uploadSpeed: 上传速率 (KB/s)
    ///   - downloadSpeed: 下载速率 (KB/s)
    ///   - networkType: 网络连接类型 ("WiFi", "Ethernet" 或 "Unknown")
    ///   - wifiIP: WiFi 连接的 IP 地址 (如果有)
    ///   - ethernetIP: 有线连接的 IP 地址 (如果有)
    func getNetworkInfo() -> [String: Any] {
        // 获取当前网络流量
        let (bytesIn, bytesOut) = getCurrentNetworkBytes()
        
        // 计算时间差
        let currentTime = Date()
        let timeInterval = currentTime.timeIntervalSince(previousNetworkTime)
        
        // 计算网络速率 (KB/s)
        var uploadSpeed: Double = 0
        var downloadSpeed: Double = 0
        
        if timeInterval > 0, previousBytesIn > 0, previousBytesOut > 0 {
            if bytesIn >= previousBytesIn {
                downloadSpeed = Double(bytesIn - previousBytesIn) / timeInterval / 1024.0
            }
            
            if bytesOut >= previousBytesOut {
                uploadSpeed = Double(bytesOut - previousBytesOut) / timeInterval / 1024.0
            }
        }
        
        // 更新上一次的值
        previousBytesIn = bytesIn
        previousBytesOut = bytesOut
        previousNetworkTime = currentTime
        
        // 获取网络连接类型和对应的 IP 地址
        let (networkType, wifiIP, ethernetIP) = getNetworkTypeAndIPs()
        
        // 确定主要 IP 地址（优先使用当前活跃连接的 IP）
        var ipAddress = "unknown"
        if networkType == "WiFi", wifiIP != nil {
            ipAddress = wifiIP!
        } else if networkType == "Ethernet", ethernetIP != nil {
            ipAddress = ethernetIP!
        } else if wifiIP != nil {
            ipAddress = wifiIP!
        } else if ethernetIP != nil {
            ipAddress = ethernetIP!
        }
        
        let info: [String: Any] = [
            "ipAddress": ipAddress,
            "uploadSpeed": uploadSpeed,
            "downloadSpeed": downloadSpeed,
            "networkType": networkType,
            "wifiIP": wifiIP ?? "无连接",
            "ethernetIP": ethernetIP ?? "无连接"
        ]
        
        // 保存网络信息
        networkInfo = info
        
        return info
    }
    
    /// 获取网络连接类型和对应的 IP 地址
    /// - Returns: 元组 (网络类型, WiFi IP, 有线 IP)
    private func getNetworkTypeAndIPs() -> (String, String?, String?) {
        var wifiIP: String?
        var ethernetIP: String?
        var activeNetworkType = "Unknown"
        
        // 获取所有网络接口
        var ifaddr: UnsafeMutablePointer<ifaddrs>?
        guard getifaddrs(&ifaddr) == 0 else { return (activeNetworkType, nil, nil) }
        defer { freeifaddrs(ifaddr) }
        
        // 检查网络接口活跃状态
        var wifiActive = false
        var ethernetActive = false
        
        // 遍历网络接口
        var ptr = ifaddr
        while ptr != nil {
            defer { ptr = ptr?.pointee.ifa_next }
            
            guard let interface = ptr?.pointee else { continue }
            guard let addr = interface.ifa_addr else { continue }
            
            let name = String(cString: interface.ifa_name)
            let flags = Int32(interface.ifa_flags)
            let isUp = (flags & IFF_UP) != 0
            let isRunning = (flags & IFF_RUNNING) != 0
            
            // 检查是否为 IPv4 地址
            if addr.pointee.sa_family == UInt8(AF_INET) {
                var hostname = [CChar](repeating: 0, count: Int(NI_MAXHOST))
                getnameinfo(addr, socklen_t(addr.pointee.sa_len),
                            &hostname, socklen_t(hostname.count),
                            nil, socklen_t(0), NI_NUMERICHOST)
                let ip = String(cString: hostname)
                
                // 检查是否为 WiFi 接口 (en0 通常是 WiFi)
                if name == "en0" {
                    wifiIP = ip
                    if isUp && isRunning {
                        wifiActive = true
                    }
                }
                
                // 检查是否为以太网接口 (en1, en2, en3 等可能是有线连接)
                if name == "en1" || name == "en2" || name == "en3" || name == "en4" {
                    ethernetIP = ip
                    if isUp && isRunning {
                        ethernetActive = true
                    }
                }
            }
        }
        
        // 确定当前活跃的网络类型
        if wifiActive {
            activeNetworkType = "WiFi"
        } else if ethernetActive {
            activeNetworkType = "Ethernet"
        }
        
        return (activeNetworkType, wifiIP, ethernetIP)
    }
    
    /// 获取当前设备的 IP 地址
    /// - Returns: IP 地址字符串，如果获取失败则返回 nil
    private func getIPAddress() -> String? {
        var address: String?
        
        // 获取所有网络接口
        var ifaddr: UnsafeMutablePointer<ifaddrs>?
        guard getifaddrs(&ifaddr) == 0 else { return nil }
        defer { freeifaddrs(ifaddr) }
        
        // 遍历网络接口
        var ptr = ifaddr
        while ptr != nil {
            defer { ptr = ptr?.pointee.ifa_next }
            
            let interface = ptr?.pointee
            let addrFamily = interface?.ifa_addr.pointee.sa_family
            
            // 检查是否为 IPv4 或 IPv6 地址
            if addrFamily == UInt8(AF_INET) || addrFamily == UInt8(AF_INET6) {
                // 获取接口名称
                let name = String(cString: (interface?.ifa_name)!)
                
                // 排除本地回环接口
                if name == "en0" || name == "en1" {
                    var hostname = [CChar](repeating: 0, count: Int(NI_MAXHOST))
                    getnameinfo(interface?.ifa_addr, socklen_t((interface?.ifa_addr.pointee.sa_len)!),
                                &hostname, socklen_t(hostname.count),
                                nil, socklen_t(0), NI_NUMERICHOST)
                    address = String(cString: hostname)
                    
                    // 优先使用 IPv4 地址
                    if addrFamily == UInt8(AF_INET) {
                        break
                    }
                }
            }
        }
        
        return address
    }
    
    /// 获取当前网络流量（字节数）
    /// - Returns: 元组 (接收字节数, 发送字节数)
    private func getCurrentNetworkBytes() -> (UInt64, UInt64) {
        var ifaddr: UnsafeMutablePointer<ifaddrs>?
        var bytesIn: UInt64 = 0
        var bytesOut: UInt64 = 0
        
        guard getifaddrs(&ifaddr) == 0 else { return (0, 0) }
        defer { freeifaddrs(ifaddr) }
        
        var ptr = ifaddr
        while ptr != nil {
            defer { ptr = ptr?.pointee.ifa_next }
            
            let interface = ptr?.pointee
            let name = String(cString: (interface?.ifa_name)!)
            
            // 只统计主要网络接口的流量
            if name == "en0" || name == "en1" {
                if let addr = interface?.ifa_addr, addr.pointee.sa_family == AF_LINK {
                    if let data = interface?.ifa_data {
                        let networkData = data.assumingMemoryBound(to: if_data.self)
                        bytesIn += UInt64(networkData.pointee.ifi_ibytes)
                        bytesOut += UInt64(networkData.pointee.ifi_obytes)
                    }
                }
            }
        }
        
        return (bytesIn, bytesOut)
    }
    
    // 确保在对象销毁时移除观察者
    deinit {
        stopMonitoring()
    }
}
