//
//  GCDTimerManager.swift
//  BDoggy
//
//  Created by K4 on 2024/12/10.
//

import Foundation

enum ExecutionQueue {
    case main
    case background
    case userInteractive // 添加高优先级队列选项
}

class GCDTimerManager {
    private var isRunning = false
    private var isInitTask = false
    private var interval: Int
    private var task: (() -> Void)?
    private var executionQueue: ExecutionQueue
    private var timerToken = UUID() // 添加一个令牌来标识当前计时器周期
    private var lastExecutionTime: DispatchTime? // 记录上次执行时间用于校准
    private var queue: DispatchQueue // 自定义队列
    
    init(interval: Int = 1000, executionQueue: ExecutionQueue = .main) {
        self.interval = interval
        self.executionQueue = executionQueue
        
        // 创建高优先级队列以提高定时精度
        self.queue = DispatchQueue(label: "com.doggy.BDoggy.timerQueue", qos: .userInteractive)
    }
    
    func startTimer(task: @escaping () -> Void) {
        stopTimer()
        self.task = task
        isRunning = true
        lastExecutionTime = nil // 重置执行时间
        scheduleNextTask()
    }
    
    func stopTimer() {
        isRunning = false
        task = nil
        timerToken = UUID() // 生成新令牌，使旧任务失效
        lastExecutionTime = nil
    }
    
    // 添加毫秒级别的更新方法
    func updateIntervalMilliseconds(to milliseconds: Int) {
        interval = milliseconds
    }
    
    private func scheduleNextTask() {
        guard isRunning else { return }
        isInitTask = true
        
        let currentToken = timerToken // 捕获当前令牌
        
        // 使用毫秒级精度
        let milliseconds = interval
        
        // 计算下一次执行的时间，考虑校准
        var nextExecutionTime: DispatchTime
        if let lastTime = lastExecutionTime {
            // 基于上次执行时间计算，减少累积误差
            nextExecutionTime = lastTime + .milliseconds(milliseconds)
            
            // 如果已经过了计划时间，立即执行
            if nextExecutionTime < .now() {
                nextExecutionTime = .now()
            }
        } else {
            nextExecutionTime = .now() + .milliseconds(milliseconds)
        }
        
        // 使用自定义高优先级队列
        queue.asyncAfter(deadline: nextExecutionTime) { [weak self] in
            guard let self = self,
                  self.isRunning,
                  currentToken == self.timerToken else { return } // 检查令牌是否仍然有效
            
            // 记录本次执行时间
            self.lastExecutionTime = .now()
            
            switch self.executionQueue {
            case .main:
                DispatchQueue.main.async {
                    self.task?()
                }
            case .background:
                self.task?()
            case .userInteractive:
                // 直接在高优先级队列上执行
                self.task?()
            }
            
            self.scheduleNextTask()
        }
    }
}
