//
//  ProductConfigGenerator.swift
//  BDoggy
//
//  Created by K4 on 2024/12/9.
//

import Foundation

class ProductConfigGenerator {
    static func generateSystemConfig() {
        let mainPath = Bundle.main.resourcePath!
        let mainURL = URL(fileURLWithPath: mainPath)
        let bdoggyURL = mainURL.appendingPathComponent("BDoggy")
        let resourcesURL = bdoggyURL.appendingPathComponent("Resources")
        do {
            // 获取Resources目录下的所有子文件夹
            let fileManager = FileManager.default
            let contents = try fileManager.contentsOfDirectory(at: resourcesURL, includingPropertiesForKeys: [.isDirectoryKey], options: [.skipsHiddenFiles])
            
            // 过滤出子文件夹，排除国际化文件夹（以.lproj结尾）
            let subfolders = contents.filter { url in
                let isDirectory = (try? url.resourceValues(forKeys: [.isDirectoryKey]).isDirectory) ?? false
                let isLocalizationFolder = url.lastPathComponent.hasSuffix(".lproj")
                return isDirectory && !isLocalizationFolder
            }
            
            var systemItems: [[String: Any]] = []
            var missingGifFiles: [String] = []
            
            // 处理每个子文件夹
            for folder in subfolders {
                let folderName = folder.lastPathComponent
                
                // 获取文件夹中的所有图片文件
                let folderContents = try fileManager.contentsOfDirectory(at: folder, includingPropertiesForKeys: nil, options: [.skipsHiddenFiles])
                let imageFiles = folderContents.filter { url in
                    let fileExtension = url.pathExtension.lowercased()
                    return ["png", "jpg", "jpeg", "gif"].contains(fileExtension)
                }.map { $0.lastPathComponent }.sorted()
                
                // 检查是否存在以文件夹名称命名的gif文件
                let gifFileName = "\(folderName).gif"
                let hasGifFile = imageFiles.contains(gifFileName)
                
                if !hasGifFile {
                    missingGifFiles.append(folderName)
                }
                
                // 创建system数组项
                let systemItem: [String: Any] = [
                    "downloadUrls": imageFiles,
                    "displayIcon": gifFileName,
                    "publisher": "system",
                    "groupName": folderName,
                    "productType": "runner",
                    "order": 0
                ]
                
                systemItems.append(systemItem)
            }
            
            // 读取现有的product_config.json文件
            let configPath = resourcesURL.appendingPathComponent("product_config.json")
            let configData = try Data(contentsOf: configPath)
            var configDict = try JSONSerialization.jsonObject(with: configData, options: []) as! [String: Any]
            
            // 更新system数组
            configDict["system"] = systemItems
            
            // 将更新后的配置写回文件
            let updatedData = try JSONSerialization.data(withJSONObject: configDict, options: [.prettyPrinted])
            try updatedData.write(to: configPath)
            
            print("配置文件已更新成功！")
            
            // 打印缺少gif文件的文件夹
            if !missingGifFiles.isEmpty {
                print("\n以下文件夹缺少对应的gif文件：")
                for folderName in missingGifFiles {
                    print("- \(folderName)")
                }
            }
            
        } catch {
            print("处理过程中出错：\(error.localizedDescription)")
        }
    }
    
    static func generateProductsConfig(for folderNames: [String]) {
        let mainPath = Bundle.main.resourcePath!
        let mainURL = URL(fileURLWithPath: mainPath)
        let bdoggyURL = mainURL.appendingPathComponent("BDoggy")
        let resourcesURL = bdoggyURL.appendingPathComponent("Resources")
        print(resourcesURL.absoluteString)
        
        do {
            let fileManager = FileManager.default
            
            // 获取Resources目录下的所有子文件夹
            let contents = try fileManager.contentsOfDirectory(at: resourcesURL, includingPropertiesForKeys: [.isDirectoryKey], options: [.skipsHiddenFiles])
            
            // 过滤出子文件夹，排除国际化文件夹（以.lproj结尾）
            let subfolders = contents.filter { url in
                let isDirectory = (try? url.resourceValues(forKeys: [.isDirectoryKey]).isDirectory) ?? false
                let isLocalizationFolder = url.lastPathComponent.hasSuffix(".lproj")
                return isDirectory && !isLocalizationFolder
            }
            
            // 读取现有的product_config.json文件
            let configPath = resourcesURL.appendingPathComponent("product_config.json")
            var configDict: [String: Any] = [:]
            
            if fileManager.fileExists(atPath: configPath.path) {
                do {
                    let configData = try Data(contentsOf: configPath)
                    configDict = try JSONSerialization.jsonObject(with: configData, options: []) as? [String: Any] ?? [:]
                } catch {
                    print("读取配置文件失败，将创建新的配置文件: \(error.localizedDescription)")
                    configDict = ["products": [:], "system": []]
                }
            } else {
                print("配置文件不存在，将创建新的配置文件")
                configDict = ["products": [:], "system": []]
            }
            
            // 确保products字典和system数组存在
            var products = configDict["products"] as? [String: Any] ?? [:]
            var systemItems: [[String: Any]] = []
            
            var processedProductFolders: [String] = []
            var processedSystemFolders: [String] = []
            var missingGifFiles: [String] = []
            
            // 将指定的文件夹名称转换为Set，方便查找
            let productFolderSet = Set(folderNames)
            
            // 处理所有子文件夹
            for folder in subfolders {
                let folderName = folder.lastPathComponent
                
                // 获取文件夹中的所有图片文件
                let folderContents = try fileManager.contentsOfDirectory(at: folder, includingPropertiesForKeys: nil, options: [.skipsHiddenFiles])
                let imageFiles = folderContents.filter { url in
                    let fileExtension = url.pathExtension.lowercased()
                    return ["png", "jpg", "jpeg", "gif"].contains(fileExtension)
                }.map { $0.lastPathComponent }.sorted()
                
                if imageFiles.isEmpty {
                    continue // 跳过没有图片的文件夹
                }
                
                // 检查是否存在以文件夹名称命名的gif文件
                let gifFileName = "\(folderName).gif"
                let hasGifFile = imageFiles.contains(gifFileName)
                
                if !hasGifFile {
                    missingGifFiles.append(folderName)
                }
                
                // 判断是添加到products还是system
                if productFolderSet.contains(folderName) {
                    // 添加到products
                    let productKey = "com.doggy.BDoggy.nonConsumable.\(folderName)"
                    let productItem: [String: Any] = [
                        "downloadUrls": imageFiles,
                        "displayIcon": gifFileName,
                        "publisher": "system",
                        "groupName": folderName,
                        "productType": "runner",
                        "order": 1
                    ]
                    
                    products[productKey] = productItem
                    processedProductFolders.append(folderName)
                } else {
                    // 添加到system
                    let systemItem: [String: Any] = [
                        "downloadUrls": imageFiles,
                        "displayIcon": gifFileName,
                        "publisher": "system",
                        "groupName": folderName,
                        "productType": "runner",
                        "order": 0
                    ]
                    
                    systemItems.append(systemItem)
                    processedSystemFolders.append(folderName)
                }
            }
            
            let runnerMakerItem: [String: Any] = [
                "downloadUrls": [],
                "displayIcon": "copywriting.gif",
                "publisher": "system",
                "groupName": "RunnerMaker",
                "productType": "service",
                "order": 0
            ]
            products["com.doggy.BDoggy.nonConsumable.RunnerMaker"] = runnerMakerItem
            processedProductFolders.append("RunnerMaker")
            // 更新products字典和system数组
            configDict["products"] = products
            configDict["system"] = systemItems
            
            // 将更新后的配置写回文件
            let updatedData = try JSONSerialization.data(withJSONObject: configDict, options: [.prettyPrinted])
            try updatedData.write(to: configPath)
            
            print("配置文件已更新成功！")
            
            if !processedProductFolders.isEmpty {
                print("\n已处理以下文件夹并添加到products中：")
                for folderName in processedProductFolders {
                    print("- \(folderName)")
                }
            }
            
            if !processedSystemFolders.isEmpty {
                print("\n已处理以下文件夹并添加到system中：")
                for folderName in processedSystemFolders {
                    print("- \(folderName)")
                }
            }
            
            // 打印缺少gif文件的文件夹
            if !missingGifFiles.isEmpty {
                print("\n以下文件夹缺少对应的gif文件：")
                for folderName in missingGifFiles {
                    print("- \(folderName)")
                }
            }
            
            // 检查是否有指定但不存在的文件夹
            let existingFolders = subfolders.map { $0.lastPathComponent }
            let nonExistentFolders = productFolderSet.filter { !existingFolders.contains($0) }
            
            if !nonExistentFolders.isEmpty {
                print("\n以下指定的文件夹不存在：")
                for folderName in nonExistentFolders {
                    print("- \(folderName)")
                }
            }
            
        } catch {
            print("处理过程中出错：\(error.localizedDescription)")
        }
    }
}
