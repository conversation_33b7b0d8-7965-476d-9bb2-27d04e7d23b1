import Foundation

enum PurchaseError: LocalizedError {
    case productNotFound
    case paymentDisabled
    case userNotFound
    case verificationFailed
    case userCancelled
    case pending
    case unknown
    case restoreFailed

    var errorDescription: String {
        switch self {
        case .productNotFound:
            return "未找到产品"
        case .paymentDisabled:
            return "支付功能已被禁用"
        case .userNotFound:
            return "未找到用户信息"
        case .verificationFailed:
            return "交易验证失败"
        case .userCancelled:
            return "用户取消购买"
        case .pending:
            return "购买待处理"
        case .unknown:
            return "未知错误"
        case .restoreFailed:
            return "恢复购买失败"
        @unknown default:
            return "未知错误"
        }
    }
}
