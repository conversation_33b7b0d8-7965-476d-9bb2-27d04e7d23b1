//
//  BlackManGIFGeneratorTool.swift
//  BDoggy
//
//  Created by K4 on 2024/12/9.
//

import Foundation

// @main
struct BlackManGIFGeneratorTool {
    static func main() {
        let mainPath = Bundle.main.resourcePath!
        let mainURL = URL(fileURLWithPath: mainPath)
        let bdoggyURL = mainURL.appendingPathComponent("BDoggy")
        let resourcesURL = bdoggyURL.appendingPathComponent("Resources")
        
        do {
            let fileManager = FileManager.default
            let contents = try fileManager.contentsOfDirectory(at: resourcesURL, includingPropertiesForKeys: [.isDirectoryKey])
            
            // 过滤出非国际化的子文件夹
            let folderNames = contents.filter { url in
                let isDirectory = (try? url.resourceValues(forKeys: [.isDirectoryKey]).isDirectory) ?? false
                let isLocalizationFolder = url.lastPathComponent.hasSuffix(".lproj")
                return isDirectory && !isLocalizationFolder
            }.map { $0.lastPathComponent }
            
            print("找到以下文件夹：\(folderNames.joined(separator: ", "))")
            BlackManGIFGenerator.generateGIFs(for: folderNames)
        } catch {
            print("获取文件夹列表时出错：\(error.localizedDescription)")
        }
    }
}
