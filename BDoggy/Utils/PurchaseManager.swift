//
//  PurchaseManager.swift
//  BDoggy
//
//  Created by K4 on 2025/2/11.
//

import StoreKit

@Observable
class PurchaseManager {
    static let shared = PurchaseManager()

    var purchasedProductIDs: Set<String> = []
    var availableProducts: [ProductData] = []
    var unPurchaseItems = [ProductData]()
    var purchasedItems = [ProductData]()
    // 添加错误提示相关的状态
    var showErrorAlert: Bool = false
    var test: Bool = false
    var errorMessage: String = ""
    static let updateNotification = Notification.Name("UpdatePurchasedStatus")
    private var updateObserver: NSObjectProtocol?

    // 产品标识符
    private let productIdentifiers = ProductMapping.shared.productIdentifiers

    // 确保对象销毁
    deinit {
        if let observer = updateObserver {
            NotificationCenter.default.removeObserver(observer)
        }
    }

    private init() {
        // 订阅窗口获取焦点的通知
        if updateObserver == nil {
            updateObserver = NotificationCenter.default.addObserver(forName: .windowDidGetFocus, object: nil, queue: .main) { [weak self] notification in
                if let window = notification.object as? NSWindow, window.identifier!.rawValue == BDoggyApp.runnerStoreWindowID {
                    DispatchQueue.main.async {
                        self?.updateProductDatas()
                    }
                }
            }
        }
        // 启动交易监听
        listenForPurchases()

        // 初始化数据
        Task {
            await fetchPurchasedProducts()
            await fetchAvailableProducts()
            availableProducts = processStoreItems(storeItems: availableProducts)
            // 通知更新页面
            let usageData = ["groupName": "update"]
            NotificationCenter.default.post(name: PurchaseManager.updateNotification, object: nil, userInfo: usageData)
        }
    }

    // 获取可用产品列表
    @MainActor
    func fetchAvailableProducts() async {
        do {
            Logger.debug("🏝️开始获取", productIdentifiers)
            let products = try await Product.products(for: productIdentifiers)
            availableProducts = products.map { product in
                // 封装为 Product
                ProductData(
                    product: product,
                    isPurchased: purchasedProductIDs.contains(product.id)
                )
            }
            #if DEBUG
            let productIDs = products.map { product in
                product.id
            }
            Logger.debug("🏝️获取结束", productIDs)
            #endif
        } catch {
            Logger.error("获取产品列表失败: \(error.localizedDescription)")
        }
    }

    private func processStoreItems(storeItems: [ProductData]) -> [ProductData] {
        var processedItems: [ProductData] = []

        for store in storeItems {
            let storeCopy = store
            if storeCopy.productType == "runner" {
                // 增加对 store.downloadUrls 的空值判断
                guard !storeCopy.downloadUrlString.isEmpty else {
                    Logger.warning("下载链接为空，跳过此项目: \(storeCopy.id)")
                    continue
                }

                let dir = FileHelper.shared.getUserFrameDirURL(ownerID: storeCopy.publisher, userID: storeCopy.publisher, groupName: storeCopy.groupName)

                if storeCopy.isPurchased {
                    let downloaded = isAllDownloaded(urls: storeCopy.downloadUrlString, downloadDirURL: dir)
                    storeCopy.isDownloaded = downloaded
                }
                processedItems.append(storeCopy)
            } else if storeCopy.productType == "service" {
                processedItems.append(storeCopy)
            }
        }
        // 排序：1. 未购买的在前，已购买未下载的在中间，已购买且已下载的在后
        // 2. 同一类别内按 order 升序排序
        processedItems.sort {
            if !$0.isPurchased, $1.isPurchased {
                return true
            } else if $0.isPurchased, !$1.isPurchased {
                return false
            } else if $0.isPurchased == $1.isPurchased {
                if $0.isDownloaded == $1.isDownloaded {
                    // 当购买状态和下载状态都相同时，按 order 升序排序
                    return $0.order < $1.order
                }
                // 未下载的在前
                return !$0.isDownloaded && $1.isDownloaded
            }
            return false
        }

        // 更新已下载、未购买和已购买的数组
        unPurchaseItems = processedItems.filter { !$0.isPurchased }
        purchasedItems = processedItems.filter { $0.isPurchased }

        // 将 purchasedItems 转换并保存到 ProductMapping.shared.purchasedItems
        ProductMapping.shared.purchasedItems = purchasedItems.map { productData in
            ProductInfo(
                downloadUrls: productData.downloadUrlString,
                displayIcon: productData.displayIcon,
                publisher: productData.publisher,
                groupName: productData.groupName,
                productType: productData.productType,
                order: productData.order,
                update: nil
            )
        }

        return processedItems
    }

    //
    func download(product: ProductData) {
        let dir = FileHelper.shared.getUserFrameDirURL(ownerID: product.publisher, userID: product.publisher, groupName: product.groupName)

        let downloadUrls = product.downloadUrlString.filter {
            guard let url = URL(string: $0) else { return false }
            return !self.isImageDownloaded(url: url, downloadDirURL: dir)
        }
        guard !product.downloadUrlString.isEmpty else {
            return
        }
        product.isDownloading = true
        if Utils.isRemote(urlString: product.downloadUrlString[0]) {
            TencentCOSDownloader.shared.fetchTemporaryCredentials(urls: downloadUrls, targetFolder: dir) { (result: Result<[URL], NetworkError>) in
                switch result {
                case .success:
                    DispatchQueue.main.async {
                        product.isDownloading = false
                        product.isDownloaded = true
                    }
                case .failure:
                    DispatchQueue.main.async {
                        product.isDownloading = false
                    }
                }
            }
        } else {
            FileHelper.shared.copyResourcesToFolder(from: downloadUrls, targetFolder: dir) { (result: Result<[URL], FileError>) in
                switch result {
                case .success:
                    product.isDownloading = false
                    product.isDownloaded = true
                case .failure:
                    product.isDownloading = false
                }
            }
        }
        // 通知更新页面
        let usageData: [String: String] = ["groupName": product.groupName]
        NotificationCenter.default.post(name: PurchaseManager.updateNotification, object: nil, userInfo: usageData)
    }

    private func isAllDownloaded(urls: [String], downloadDirURL: URL) -> Bool {
        // 判断是否所有图片已下载
        let allImagesDownloaded = urls.allSatisfy {
            guard let url = URL(string: $0) else { return true }
            return self.isImageDownloaded(url: url, downloadDirURL: downloadDirURL)
        }
        return allImagesDownloaded
    }

    // 判断图片是否已下载
    private func isImageDownloaded(url: URL, downloadDirURL: URL) -> Bool {
        let fileName = url.lastPathComponent
        let fileURL = downloadDirURL.appendingPathComponent(fileName)
        return FileManager.default.fileExists(atPath: fileURL.path)
    }

    // 刷新产品列表
    func refreshProducts() async {
        await fetchAvailableProducts()
    }

    // MARK: - 购买商品

    @MainActor
    func purchaseProduct(productID: String) async throws {
        do {
            // 获取产品信息
            guard let product = try await Product.products(for: [productID]).first else {
                throw PurchaseError.productNotFound
            }

            Logger.info("🛍️ 准备购买商品：", product.displayName, product.displayPrice)

            // 检查是否可以进行购买
            guard AppStore.canMakePayments else {
                throw PurchaseError.paymentDisabled
            }

            // 执行购买
            let result = try await product.purchase(options: [.appAccountToken(UserManager.appAccountToken), .custom(key: "UserID", value: UserManager.appAccountToken.uuidString)])

            switch result {
            case .success(let verification):
                if let transaction = try? verification.payloadValue {
                    await handleTransaction(transaction)
                    updateProductDatas()
                } else {
                    throw PurchaseError.verificationFailed
                }
            case .userCancelled:
                Logger.info("用户取消购买")
                throw PurchaseError.userCancelled
            case .pending:
                Logger.info("⌛ 购买待处理")
                throw PurchaseError.pending
            @unknown default:
                throw PurchaseError.unknown
            }
        } catch let error as PurchaseError {
            Logger.error("购买失败：", error.localizedDescription)
            throw error
        } catch {
            Logger.error("购买失败：", error.localizedDescription)
            throw PurchaseError.unknown
        }
    }

    // MARK: - 处理交易

    private func handleTransaction(_ transaction: Transaction) async {
        guard transaction.revocationDate == nil else {
            Logger.warning("交易已被撤销")
            return
        }
//        let originalTransactionID = transaction.originalID
//        let appAccountToken = transaction.appAccountToken?.uuidString ?? ""

        // 发送购买信息到服务器
//        let purchaseData = PurchaseData(userID: userID, originalTransactionID: originalTransactionID, appAccountToken: appAccountToken, productID: transaction.productID)

        purchasedProductIDs.insert(transaction.productID)
        // 完成交易
        await transaction.finish()
        Logger.info("交易完成")
        // 通知更新页面
        let usageData: [String: String] = ["productID": transaction.productID]
        NotificationCenter.default.post(name: PurchaseManager.updateNotification, object: nil, userInfo: usageData)
    }

    // MARK: - 恢复购买

    @MainActor
    func restorePurchases() async {
        do {
            // TODO: -
            Logger.info("开始恢复购买...")
            // 确保用户已登录 App Store
            try await AppStore.sync()

            var restoredCount = 0
            for await verification in Transaction.currentEntitlements {
                if case .verified(let transaction) = verification {
                    await handleTransaction(transaction)
                    // 可以在此重新存储 appAccountToken
                    if let token = transaction.appAccountToken {
                        KeychainHelper.saveAppAccountToken(token)
                    }
                    restoredCount += 1
                }
            }

            if restoredCount > 0 {
                Logger.success("成功恢复 \(restoredCount) 个购买项目")
                // 刷新产品列表状态
                updateProductDatas()
            } else {
                Logger.info("没有找到可恢复的购买项目")
            }
        } catch {
            Logger.error("恢复购买失败：", error.localizedDescription)
        }
    }

    // MARK: - 查询已购买商品

    func fetchPurchasedProducts() async {
        for await verification in Transaction.currentEntitlements {
            if let transaction = try? verification.payloadValue,
               transaction.revocationDate == nil
            {
                purchasedProductIDs.insert(transaction.productID)
            }
        }
        Logger.info("purchasedProductIDs:", purchasedProductIDs)
    }

    // MARK: -  更新 `productData` 购买状态

    @MainActor
    private func updateProductDatas() {
        let products = availableProducts.map { productViewModel in
            let updatedViewModel = productViewModel
            updatedViewModel.isPurchased = purchasedProductIDs.contains(productViewModel.id)
            return updatedViewModel
        }
        availableProducts = processStoreItems(storeItems: products)
        Logger.info("更新购买状态", availableProducts)
    }

    // MARK: - 查询购买状态

    func isProductPurchased(_ productID: String) -> Bool {
        return purchasedProductIDs.contains(productID)
    }

    // 异步查询（确保数据最新）
    func checkPurchaseStatus(_ productID: String) async -> Bool {
        // 先查询本地缓存
        if purchasedProductIDs.contains(productID) {
            return true
        }

        // 从 App Store 查询最新状态
        for await verification in Transaction.currentEntitlements {
            if case .verified(let transaction) = verification,
               transaction.productID == productID,
               transaction.revocationDate == nil
            {
                return true
            }
        }
        return false
    }

    // MARK: -  监听购买状态变化

    private func listenForPurchases() {
        // 使用 Task 创建持续运行的监听任务
        Task.detached { [weak self] in
            // 持续监听交易更新
            for await result in Transaction.updates {
                // 确保在主线程更新 UI
                await MainActor.run { [weak self] in
                    guard let self = self else { return }

                    switch result {
                    case .verified(let transaction):
                        // 处理有效交易
                        self.purchasedProductIDs.insert(transaction.productID)
                        Task {
                            await transaction.finish()
                            self.updateProductDatas()
                            Logger.success("新交易已处理：\(transaction.productID)")
                        }

                    case .unverified(_, let error):
                        // 处理无效交易
                        Logger.error("交易验证失败：", error.localizedDescription)
                    }
                }
            }
            Logger.debug("打开购买状态执行完毕")
        }
    }
}

struct PurchaseData: Codable {
    let userID: String
    let originalTransactionID: UInt64
    let appAccountToken: String
    let productID: String
}

@Observable
class ProductData: Identifiable {
    let id: String
    let displayName: String
    let downloadUrlString: [String]
    let description: String
    let price: String
    let publisher: String
    let displayIcon: String
    let groupName: String
    let productType: String
    let order: Int
    var isPurchased: Bool
    var isDownloaded: Bool = false
    var isDownloading: Bool = false

    // 添加计算属性
    var isDisplayIconRemote: Bool {
        guard let url = URL(string: displayIcon) else { return false }
        return url.scheme == "http" || url.scheme == "https"
    }

    var displayIconExtension: String? {
        if isDisplayIconRemote {
            return URL(string: displayIcon)?.pathExtension.lowercased()
        } else {
            return (displayIcon as NSString).pathExtension.lowercased()
        }
    }

    // 添加新的计算属性
    var displayIconPathWithoutExtension: String? {
        guard let url = URL(string: displayIcon) else { return nil }

        if isDisplayIconRemote {
            // 对于远程URL，删除最后一个组件的扩展名
            let pathWithoutExtension = url.deletingPathExtension().path
            return pathWithoutExtension
        } else {
            // 对于本地路径，使用 NSString 方法
            return (displayIcon as NSString).deletingPathExtension
        }
    }

    init(product: Product, isPurchased: Bool = false) {
        id = product.id
        displayName = product.displayName
        description = product.description
        price = product.displayPrice
        self.isPurchased = isPurchased

        // 从映射文件获取额外信息
        if let productInfo = ProductMapping.shared.getProductInfo(product.id) {
            downloadUrlString = productInfo.downloadUrls
            publisher = productInfo.publisher
            displayIcon = productInfo.displayIcon
            groupName = productInfo.groupName
            productType = productInfo.productType
            order = productInfo.order
        } else {
            order = 999
            downloadUrlString = []
            publisher = "unknown"
            displayIcon = "unknown"
            productType = "unknown"
            groupName = "unknown"
        }
    }
}
