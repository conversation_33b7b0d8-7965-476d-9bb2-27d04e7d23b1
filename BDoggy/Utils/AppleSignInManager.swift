import AuthenticationServices
import Foundation

@MainActor
class AppleSignInManager: NSObject, ASAuthorizationControllerDelegate {
    static let shared = AppleSignInManager()

    override private init() {}

    var completionHandler: ((Result<String, Error>) -> Void)?

    /// 发起 Apple 登录
    @MainActor
    func signInWithApple(completion: @escaping (Result<String, Error>) -> Void) {
        let request = ASAuthorizationAppleIDProvider().createRequest()
        request.requestedScopes = [.fullName, .email]

        let controller = ASAuthorizationController(authorizationRequests: [request])
        controller.delegate = self
        // 设置合适的 QoS 级别
        controller.performRequests()

        completionHandler = completion
    }

    /// Apple 登录成功
    func authorizationController(controller: ASAuthorizationController, didCompleteWithAuthorization authorization: ASAuthorization) {
        if let credential = authorization.credential as? ASAuthorizationAppleIDCredential {
            let userID = credential.user // Apple ID 唯一标识
            UserDefaults.standard.set(userID, forKey: Constants.AppleUserIDKey)

            var parameters: [String: Any] = [
                "userIdentifier": userID
            ]

            // 获取身份令牌和授权代码
            if let identityToken = credential.identityToken,
               let authorizationCode = credential.authorizationCode
            {
                // 添加到参数中
                parameters["identityToken"] = identityToken.base64EncodedString()
                parameters["authorizationCode"] = authorizationCode.base64EncodedString()
            }

            // 将参数编码为 JSON 数据
            guard let jsonData = try? JSONSerialization.data(withJSONObject: parameters, options: []) else {
                print("Failed to encode parameters to JSON")
                return
            }
            // 先返回登录成功
            completionHandler?(.success(userID))

            // 后台执行网络请求
            Task.detached {
                do {
                    let _: String = try await NetworkManager.shared.request(
                        url: "http://localhost:3000/api/auth/apple",
                        method: HTTPMethod.POST,
                        body: jsonData
                    )
                } catch {
                    print("请求失败:", error.localizedDescription)
                }
            }
        }
    }

    /// Apple 登录失败
    func authorizationController(controller: ASAuthorizationController, didCompleteWithError error: Error) {
        completionHandler?(.failure(error))
    }

    /// 获取存储的 Apple ID 用户标识
    func getStoredAppleUserID() -> String? {
        return UserDefaults.standard.string(forKey: Constants.AppleUserIDKey)
    }
}

struct JWTDto: Decodable {
    let token: String
    let user: User
    let apple_tokens: AppleToken
}

struct User: Decodable {
    let id: String
    let userIdentifier: String
    let email: String
}

struct AppleToken: Decodable {
    let access_token: String
    let refresh_token: String
    let expires_in: Int
}
