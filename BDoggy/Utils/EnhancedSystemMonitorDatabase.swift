//
//  EnhancedSystemMonitorDatabase.swift
//  BDoggy
//
//  Created by K4 on 2025/1/11.
//

import Foundation
import SQLite3

/// 增强版系统监控数据库管理器
/// 支持完整的系统监控数据存储，包括系统信息、指标日志和应用使用记录
class EnhancedSystemMonitorDatabase {
    static let shared = EnhancedSystemMonitorDatabase()

    private var db: OpaquePointer?
    private let databasePath: String
    private let fileManager = FileManager.default

    // MARK: - 初始化

    private init() {
        // 创建应用数据目录
        let appSupportURL = fileManager.urls(for: .applicationSupportDirectory, in: .userDomainMask).first!
        let appDataURL = appSupportURL.appendingPathComponent("BDoggy")

        // 确保目录存在
        try? fileManager.createDirectory(at: appDataURL, withIntermediateDirectories: true)

        // 数据库文件路径
        databasePath = appDataURL.appendingPathComponent("system_monitor.db").path

        // 初始化数据库
        initializeDatabase()
    }

    deinit {
        closeDatabase()
    }

    // MARK: - 数据库连接管理

    private func initializeDatabase() {
        if sqlite3_open(databasePath, &db) == SQLITE_OK {
            Logger.info("数据库连接成功: \(databasePath)")
            createTables()
        } else {
            Logger.error("数据库连接失败: \(String(cString: sqlite3_errmsg(db)))")
        }
    }

    private func closeDatabase() {
        if sqlite3_close(db) == SQLITE_OK {
            Logger.info("数据库连接已关闭")
        }
    }

    // MARK: - 创建数据表

    private func createTables() {
        createSystemInfoTable()
        createMetricsLogTable()
        createAppUsageTable()
        createConfigTable()
    }

    /// 创建系统信息表（静态信息，只记录一次）
    private func createSystemInfoTable() {
        let sql = """
            CREATE TABLE IF NOT EXISTS system_info (
                id TEXT PRIMARY KEY,
                device_model TEXT NOT NULL,
                cpu_name TEXT NOT NULL,
                cpu_core_count INTEGER NOT NULL,
                total_memory_gb REAL NOT NULL,
                total_disk_gb REAL NOT NULL,
                macos_version TEXT NOT NULL,
                recorded_at DATETIME NOT NULL
            );
        """
        executeSQL(sql, description: "创建系统信息表")
    }

    /// 创建指标日志表（动态数据，定时记录）
    private func createMetricsLogTable() {
        let sql = """
            CREATE TABLE IF NOT EXISTS metrics_log (
                id TEXT PRIMARY KEY,
                timestamp DATETIME NOT NULL,
                cpu_usage_percent REAL NOT NULL,
                memory_usage_percent REAL NOT NULL,
                memory_used_gb REAL NOT NULL,
                memory_available_gb REAL NOT NULL,
                swap_used_gb REAL NOT NULL,
                swap_total_gb REAL NOT NULL,
                disk_free_gb REAL NOT NULL,
                disk_usage_percent REAL NOT NULL,
                battery_level INTEGER,
                battery_health REAL,
                battery_cycle_count INTEGER,
                power_source_type TEXT NOT NULL,
                cpu_temperature REAL,
                gpu_temperature REAL,
                fan_speed INTEGER,
                active_app_bundle_id TEXT,
                active_app_name TEXT,
                top_processes TEXT
            );
        """
        executeSQL(sql, description: "创建指标日志表")

        // 创建索引以提高查询性能
        let indexSQL = "CREATE INDEX IF NOT EXISTS idx_metrics_timestamp ON metrics_log(timestamp);"
        executeSQL(indexSQL, description: "创建时间戳索引")
    }

    /// 创建应用使用记录表
    private func createAppUsageTable() {
        let sql = """
            CREATE TABLE IF NOT EXISTS app_usage (
                id TEXT PRIMARY KEY,
                bundle_id TEXT NOT NULL,
                app_name TEXT NOT NULL,
                start_time DATETIME NOT NULL,
                end_time DATETIME,
                duration_seconds REAL NOT NULL,
                peak_cpu_usage REAL NOT NULL,
                peak_memory_usage_mb REAL NOT NULL,
                avg_cpu_usage REAL NOT NULL,
                avg_memory_usage_mb REAL NOT NULL,
                recorded_date DATE NOT NULL
            );
        """
        executeSQL(sql, description: "创建应用使用记录表")

        // 创建索引
        let indexSQL1 = "CREATE INDEX IF NOT EXISTS idx_app_usage_bundle_id ON app_usage(bundle_id);"
        let indexSQL2 = "CREATE INDEX IF NOT EXISTS idx_app_usage_date ON app_usage(recorded_date);"
        executeSQL(indexSQL1, description: "创建Bundle ID索引")
        executeSQL(indexSQL2, description: "创建日期索引")
    }

    /// 创建配置表
    private func createConfigTable() {
        let sql = """
            CREATE TABLE IF NOT EXISTS monitor_config (
                key TEXT PRIMARY KEY,
                value TEXT NOT NULL,
                updated_at DATETIME NOT NULL
            );
        """
        executeSQL(sql, description: "创建配置表")
    }

    // MARK: - SQL执行辅助方法

    private func executeSQL(_ sql: String, description: String) {
        if sqlite3_exec(db, sql, nil, nil, nil) == SQLITE_OK {
            Logger.debug("\(description)成功")
        } else {
            Logger.error("\(description)失败: \(String(cString: sqlite3_errmsg(db)))")
        }
    }

    // MARK: - 系统信息操作

    /// 插入系统信息（只插入一次）
    func insertSystemInfo(_ systemInfo: SystemInfo) -> Bool {
        let sql = """
            INSERT OR REPLACE INTO system_info 
            (id, device_model, cpu_name, cpu_core_count, total_memory_gb, total_disk_gb, macos_version, recorded_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?);
        """

        var statement: OpaquePointer?
        guard sqlite3_prepare_v2(db, sql, -1, &statement, nil) == SQLITE_OK else {
            Logger.error("准备系统信息插入语句失败")
            return false
        }

        defer { sqlite3_finalize(statement) }

        sqlite3_bind_text(statement, 1, systemInfo.id, -1, nil)
        sqlite3_bind_text(statement, 2, systemInfo.deviceModel, -1, nil)
        sqlite3_bind_text(statement, 3, systemInfo.cpuName, -1, nil)
        sqlite3_bind_int(statement, 4, Int32(SystemInfoCollector.getCPUCoreCount()))
        sqlite3_bind_double(statement, 5, systemInfo.totalMemoryGB)
        sqlite3_bind_double(statement, 6, systemInfo.totalDiskGB)
        sqlite3_bind_text(statement, 7, systemInfo.macOSVersion, -1, nil)
        sqlite3_bind_double(statement, 8, systemInfo.recordedAt.timeIntervalSince1970)

        return sqlite3_step(statement) == SQLITE_DONE
    }

    /// 获取系统信息
    func getSystemInfo() -> SystemInfo? {
        let sql = "SELECT * FROM system_info LIMIT 1;"
        var statement: OpaquePointer?

        guard sqlite3_prepare_v2(db, sql, -1, &statement, nil) == SQLITE_OK else {
            Logger.error("准备系统信息查询语句失败")
            return nil
        }

        defer { sqlite3_finalize(statement) }

        if sqlite3_step(statement) == SQLITE_ROW {
            let id = String(cString: sqlite3_column_text(statement, 0))
            let deviceModel = String(cString: sqlite3_column_text(statement, 1))
            let cpuName = String(cString: sqlite3_column_text(statement, 2))
            let totalMemoryGB = sqlite3_column_double(statement, 4)
            let totalDiskGB = sqlite3_column_double(statement, 5)
            let macOSVersion = String(cString: sqlite3_column_text(statement, 6))
            let recordedAt = Date(timeIntervalSince1970: sqlite3_column_double(statement, 7))

            return SystemInfo(
                id: id,
                deviceModel: deviceModel,
                cpuName: cpuName,
                totalMemoryGB: totalMemoryGB,
                totalDiskGB: totalDiskGB,
                macOSVersion: macOSVersion,
                recordedAt: recordedAt
            )
        }

        return nil
    }

    // MARK: - 数据清理

    /// 一键清除所有数据
    func clearAllData() -> Bool {
        let tables = ["metrics_log", "app_usage"]
        var success = true

        for table in tables {
            let sql = "DELETE FROM \(table);"
            if sqlite3_exec(db, sql, nil, nil, nil) != SQLITE_OK {
                Logger.error("清除表 \(table) 失败")
                success = false
            }
        }

        if success {
            Logger.info("所有监控数据已清除")
        }

        return success
    }

    /// 清除指定天数之前的数据
    func cleanupOldData(olderThanDays days: Int) -> Bool {
        let cutoffDate = Date().addingTimeInterval(-TimeInterval(days * 24 * 3600))
        let cutoffTimestamp = cutoffDate.timeIntervalSince1970

        let sql1 = "DELETE FROM metrics_log WHERE timestamp < ?;"
        let sql2 = "DELETE FROM app_usage WHERE start_time < ?;"

        var success = true

        for sql in [sql1, sql2] {
            var statement: OpaquePointer?
            if sqlite3_prepare_v2(db, sql, -1, &statement, nil) == SQLITE_OK {
                sqlite3_bind_double(statement, 1, cutoffTimestamp)
                if sqlite3_step(statement) != SQLITE_DONE {
                    success = false
                }
                sqlite3_finalize(statement)
            } else {
                success = false
            }
        }

        if success {
            Logger.info("已清除 \(days) 天前的旧数据")
        }

        return success
    }

    /// 获取数据库文件大小
    func getDatabaseSize() -> Int64 {
        do {
            let attributes = try fileManager.attributesOfItem(atPath: databasePath)
            return attributes[.size] as? Int64 ?? 0
        } catch {
            Logger.error("获取数据库大小失败: \(error)")
            return 0
        }
    }

    /// 获取数据库路径
    func getDatabasePath() -> String {
        return databasePath
    }

    // MARK: - 指标日志操作

    /// 插入系统指标记录
    func insertMetricsLog(_ metrics: EnhancedSystemMetrics) -> Bool {
        let sql = """
            INSERT INTO metrics_log
            (id, timestamp, cpu_usage_percent, memory_usage_percent, memory_used_gb, memory_available_gb,
             swap_used_gb, swap_total_gb, disk_free_gb, disk_usage_percent, battery_level, battery_health,
             battery_cycle_count, power_source_type, cpu_temperature, gpu_temperature, fan_speed,
             active_app_bundle_id, active_app_name, top_processes)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);
        """

        var statement: OpaquePointer?
        guard sqlite3_prepare_v2(db, sql, -1, &statement, nil) == SQLITE_OK else {
            Logger.error("准备指标插入语句失败")
            return false
        }

        defer { sqlite3_finalize(statement) }

        // 将top processes转换为JSON字符串
        let topProcessesJSON = encodeTopProcessesToJSON(metrics.topProcesses)

        sqlite3_bind_text(statement, 1, metrics.id, -1, nil)
        sqlite3_bind_double(statement, 2, metrics.timestamp.timeIntervalSince1970)
        sqlite3_bind_double(statement, 3, metrics.cpuUsagePercent)
        sqlite3_bind_double(statement, 4, metrics.memoryUsagePercent)
        sqlite3_bind_double(statement, 5, metrics.memoryUsedGB)
        sqlite3_bind_double(statement, 6, metrics.memoryAvailableGB)
        sqlite3_bind_double(statement, 7, metrics.swapUsedGB)
        sqlite3_bind_double(statement, 8, metrics.swapTotalGB)
        sqlite3_bind_double(statement, 9, metrics.diskFreeGB)
        sqlite3_bind_double(statement, 10, metrics.diskUsagePercent)

        if let batteryLevel = metrics.batteryLevel {
            sqlite3_bind_int(statement, 11, Int32(batteryLevel))
        } else {
            sqlite3_bind_null(statement, 11)
        }

        if let batteryHealth = metrics.batteryHealth {
            sqlite3_bind_double(statement, 12, batteryHealth)
        } else {
            sqlite3_bind_null(statement, 12)
        }

        if let batteryCycleCount = metrics.batteryCycleCount {
            sqlite3_bind_int(statement, 13, Int32(batteryCycleCount))
        } else {
            sqlite3_bind_null(statement, 13)
        }

        sqlite3_bind_text(statement, 14, metrics.powerSourceType, -1, nil)

        if let cpuTemp = metrics.cpuTemperature {
            sqlite3_bind_double(statement, 15, cpuTemp)
        } else {
            sqlite3_bind_null(statement, 15)
        }

        if let gpuTemp = metrics.gpuTemperature {
            sqlite3_bind_double(statement, 16, gpuTemp)
        } else {
            sqlite3_bind_null(statement, 16)
        }

        if let fanSpeed = metrics.fanSpeed {
            sqlite3_bind_int(statement, 17, Int32(fanSpeed))
        } else {
            sqlite3_bind_null(statement, 17)
        }

        if let bundleID = metrics.activeAppBundleID {
            sqlite3_bind_text(statement, 18, bundleID, -1, nil)
        } else {
            sqlite3_bind_null(statement, 18)
        }

        if let appName = metrics.activeAppName {
            sqlite3_bind_text(statement, 19, appName, -1, nil)
        } else {
            sqlite3_bind_null(statement, 19)
        }

        sqlite3_bind_text(statement, 20, topProcessesJSON, -1, nil)

        return sqlite3_step(statement) == SQLITE_DONE
    }

    /// 获取最近N天的指标数据
    func getMetricsLog(days: Int = 7) -> [EnhancedSystemMetrics] {
        let cutoffDate = Date().addingTimeInterval(-TimeInterval(days * 24 * 3600))
        let sql = """
            SELECT * FROM metrics_log
            WHERE timestamp >= ?
            ORDER BY timestamp DESC;
        """

        var statement: OpaquePointer?
        guard sqlite3_prepare_v2(db, sql, -1, &statement, nil) == SQLITE_OK else {
            Logger.error("准备指标查询语句失败")
            return []
        }

        defer { sqlite3_finalize(statement) }

        sqlite3_bind_double(statement, 1, cutoffDate.timeIntervalSince1970)

        var metrics: [EnhancedSystemMetrics] = []

        while sqlite3_step(statement) == SQLITE_ROW {
            if let metric = parseMetricsFromRow(statement) {
                metrics.append(metric)
            }
        }

        return metrics
    }

    /// 获取指定时间范围的指标数据（用于图表显示）
    func getMetricsForChart(from startDate: Date, to endDate: Date) -> [[String: Any]] {
        let sql = """
            SELECT timestamp, cpu_usage_percent, memory_usage_percent, disk_usage_percent,
                   battery_level, cpu_temperature, gpu_temperature
            FROM metrics_log
            WHERE timestamp BETWEEN ? AND ?
            ORDER BY timestamp ASC;
        """

        var statement: OpaquePointer?
        guard sqlite3_prepare_v2(db, sql, -1, &statement, nil) == SQLITE_OK else {
            Logger.error("准备图表数据查询语句失败")
            return []
        }

        defer { sqlite3_finalize(statement) }

        sqlite3_bind_double(statement, 1, startDate.timeIntervalSince1970)
        sqlite3_bind_double(statement, 2, endDate.timeIntervalSince1970)

        var chartData: [[String: Any]] = []

        while sqlite3_step(statement) == SQLITE_ROW {
            var dataPoint: [String: Any] = [:]

            dataPoint["timestamp"] = Date(timeIntervalSince1970: sqlite3_column_double(statement, 0))
            dataPoint["cpu_usage"] = sqlite3_column_double(statement, 1)
            dataPoint["memory_usage"] = sqlite3_column_double(statement, 2)
            dataPoint["disk_usage"] = sqlite3_column_double(statement, 3)

            if sqlite3_column_type(statement, 4) != SQLITE_NULL {
                dataPoint["battery_level"] = sqlite3_column_int(statement, 4)
            }

            if sqlite3_column_type(statement, 5) != SQLITE_NULL {
                dataPoint["cpu_temperature"] = sqlite3_column_double(statement, 5)
            }

            if sqlite3_column_type(statement, 6) != SQLITE_NULL {
                dataPoint["gpu_temperature"] = sqlite3_column_double(statement, 6)
            }

            chartData.append(dataPoint)
        }

        return chartData
    }

    // MARK: - 应用使用记录操作

    /// 插入应用使用记录 
     func insertOrUpdateAppUsage(_ appUsages: [AppUsageRecord]) -> Bool {
        // TODO: 事物操作更新或插入应用使用记录，bundl_id唯一，其中duration_seconds字段需要计算，计算方式为新老数据相加再存入。memory_usage_mbs和cpu_usages字段需要计算，计算方式：新老数据合并后存入。
         return false
     }

    // MARK: - 应用使用记录操作

    /// 插入应用使用记录
    func insertAppUsage(_ appUsage: AppUsageRecord) -> Bool {
        let sql = """
            INSERT INTO app_usage
            (id, bundle_id, app_name, start_time, end_time, duration_seconds,
             peak_cpu_usage, peak_memory_usage_mb, avg_cpu_usage, avg_memory_usage_mb, recorded_date)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);
        """

        var statement: OpaquePointer?
        guard sqlite3_prepare_v2(db, sql, -1, &statement, nil) == SQLITE_OK else {
            Logger.error("准备应用使用记录插入语句失败")
            return false
        }

        defer { sqlite3_finalize(statement) }

        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let recordedDate = dateFormatter.string(from: appUsage.startTime)

        sqlite3_bind_text(statement, 1, appUsage.id, -1, nil)
        sqlite3_bind_text(statement, 2, appUsage.bundleID, -1, nil)
        sqlite3_bind_text(statement, 3, appUsage.appName, -1, nil)
        sqlite3_bind_double(statement, 4, appUsage.startTime.timeIntervalSince1970)

        if let endTime = appUsage.endTime {
            sqlite3_bind_double(statement, 5, endTime.timeIntervalSince1970)
        } else {
            sqlite3_bind_null(statement, 5)
        }

        sqlite3_bind_double(statement, 6, appUsage.duration)
        sqlite3_bind_double(statement, 7, appUsage.peakCPUUsage)
        sqlite3_bind_double(statement, 8, appUsage.peakMemoryUsageMB)
        sqlite3_bind_double(statement, 9, appUsage.avgCPUUsage)
        sqlite3_bind_double(statement, 10, appUsage.avgMemoryUsageMB)
        sqlite3_bind_text(statement, 11, recordedDate, -1, nil)

        return sqlite3_step(statement) == SQLITE_DONE
    }

    /// 获取应用使用统计（按天）
    func getAppUsageStats(days: Int = 7) -> [AppUsageRecord] {
        let cutoffDate = Date().addingTimeInterval(-TimeInterval(days * 24 * 3600))
        let sql = """
            SELECT * FROM app_usage
            WHERE start_time >= ?
            ORDER BY start_time DESC;
        """

        var statement: OpaquePointer?
        guard sqlite3_prepare_v2(db, sql, -1, &statement, nil) == SQLITE_OK else {
            Logger.error("准备应用使用统计查询语句失败")
            return []
        }

        defer { sqlite3_finalize(statement) }

        sqlite3_bind_double(statement, 1, cutoffDate.timeIntervalSince1970)

        var records: [AppUsageRecord] = []

        while sqlite3_step(statement) == SQLITE_ROW {
            if let record = parseAppUsageFromRow(statement!) {
                records.append(record)
            }
        }

        return records
    }

    /// 获取应用使用汇总（按应用分组）
    func getAppUsageSummary(days: Int = 7) -> [[String: Any]] {
        let cutoffDate = Date().addingTimeInterval(-TimeInterval(days * 24 * 3600))
        let sql = """
            SELECT bundle_id, app_name,
                   COUNT(*) as session_count,
                   SUM(duration_seconds) as total_duration,
                   AVG(duration_seconds) as avg_duration,
                   MAX(peak_cpu_usage) as max_cpu,
                   AVG(avg_cpu_usage) as avg_cpu,
                   MAX(peak_memory_usage_mb) as max_memory,
                   AVG(avg_memory_usage_mb) as avg_memory
            FROM app_usage
            WHERE start_time >= ?
            GROUP BY bundle_id, app_name
            ORDER BY total_duration DESC;
        """

        var statement: OpaquePointer?
        guard sqlite3_prepare_v2(db, sql, -1, &statement, nil) == SQLITE_OK else {
            Logger.error("准备应用使用汇总查询语句失败")
            return []
        }

        defer { sqlite3_finalize(statement) }

        sqlite3_bind_double(statement, 1, cutoffDate.timeIntervalSince1970)

        var summaries: [[String: Any]] = []

        while sqlite3_step(statement) == SQLITE_ROW {
            var summary: [String: Any] = [:]

            summary["bundle_id"] = String(cString: sqlite3_column_text(statement, 0))
            summary["app_name"] = String(cString: sqlite3_column_text(statement, 1))
            summary["session_count"] = sqlite3_column_int(statement, 2)
            summary["total_duration"] = sqlite3_column_double(statement, 3)
            summary["avg_duration"] = sqlite3_column_double(statement, 4)
            summary["max_cpu"] = sqlite3_column_double(statement, 5)
            summary["avg_cpu"] = sqlite3_column_double(statement, 6)
            summary["max_memory"] = sqlite3_column_double(statement, 7)
            summary["avg_memory"] = sqlite3_column_double(statement, 8)

            summaries.append(summary)
        }

        return summaries
    }

    // MARK: - 辅助方法

    /// 将top processes编码为JSON字符串
    private func encodeTopProcessesToJSON(_ processes: [ProcessInfoModel]) -> String {
        do {
            let data = try JSONEncoder().encode(processes)
            return String(data: data, encoding: .utf8) ?? "[]"
        } catch {
            Logger.error("编码top processes失败: \(error)")
            return "[]"
        }
    }

    /// 从数据库行解析SystemMetrics
    private func parseMetricsFromRow(_ statement: OpaquePointer?) -> EnhancedSystemMetrics? {
        // 由于EnhancedSystemMetrics的init()方法会自动采集数据，
        // 这里我们需要创建一个简化版本用于数据库读取
        // 暂时返回nil，实际使用中可以创建一个专门的数据库读取初始化方法
        return nil
    }

    /// 从数据库行解析AppUsageRecord
    private func parseAppUsageFromRow(_ statement: OpaquePointer) -> AppUsageRecord? {
        let id = String(cString: sqlite3_column_text(statement, 0))
        let bundleID = String(cString: sqlite3_column_text(statement, 1))
        let appName = String(cString: sqlite3_column_text(statement, 2))
        let startTime = Date(timeIntervalSince1970: sqlite3_column_double(statement, 3))

        let endTime: Date?
        if sqlite3_column_type(statement, 4) != SQLITE_NULL {
            endTime = Date(timeIntervalSince1970: sqlite3_column_double(statement, 4))
        } else {
            endTime = nil
        }

        let duration = sqlite3_column_double(statement, 5)
        let peakCPUUsage = sqlite3_column_double(statement, 6)
        let peakMemoryUsageMB = sqlite3_column_double(statement, 7)
        let avgCPUUsage = sqlite3_column_double(statement, 8)
        let avgMemoryUsageMB = sqlite3_column_double(statement, 9)

        return AppUsageRecord(
            id: id,
            bundleID: bundleID,
            appName: appName,
            startTime: startTime,
            endTime: endTime,
            duration: duration,
            peakCPUUsage: peakCPUUsage,
            peakMemoryUsageMB: peakMemoryUsageMB,
            avgCPUUsage: avgCPUUsage,
            avgMemoryUsageMB: avgMemoryUsageMB,
            CPUUsages: [],
            memoryUsageMBs: []
        )
    }
}

// MARK: - SystemInfo扩展，支持自定义初始化

extension SystemInfo {
    init(id: String, deviceModel: String, cpuName: String, totalMemoryGB: Double, totalDiskGB: Double, macOSVersion: String, recordedAt: Date) {
        self.id = id
        self.deviceModel = deviceModel
        self.cpuName = cpuName
        self.totalMemoryGB = totalMemoryGB
        self.totalDiskGB = totalDiskGB
        self.macOSVersion = macOSVersion
        self.recordedAt = recordedAt
    }
}

// MARK: - AppUsageRecord扩展，支持数据库初始化

extension AppUsageRecord {
    init(id: String, bundleID: String, appName: String, startTime: Date, endTime: Date?, duration: TimeInterval, peakCPUUsage: Double, peakMemoryUsageMB: Double, avgCPUUsage: Double, avgMemoryUsageMB: Double, CPUUsages: [Double], memoryUsageMBs: [Double]) {
        self.id = id
        self.bundleID = bundleID
        self.appName = appName
        self.startTime = startTime
        self.endTime = endTime
        self.duration = duration
        self.peakCPUUsage = peakCPUUsage
        self.peakMemoryUsageMB = peakMemoryUsageMB
        self.avgCPUUsage = avgCPUUsage
        self.avgMemoryUsageMB = avgMemoryUsageMB
        self.CPUUsages = CPUUsages
        self.memoryUsageMBs = memoryUsageMBs
    }
}
