//
//  PrivacyPermissionManager.swift
//  BDoggy
//
//  Created by System Monitor on 2024/12/11.
//

import Foundation
import AppKit
import ApplicationServices

/// 隐私权限管理器 - 处理权限检查、用户引导和隐私合规
class PrivacyPermissionManager: ObservableObject {
    static let shared = PrivacyPermissionManager()
    
    @Published var hasAccessibilityPermission = false
    @Published var hasFullDiskAccess = false
    
    private init() {
        checkAllPermissions()
    }
    
    // MARK: - 权限检查
    
    /// 检查所有权限状态
    func checkAllPermissions() {
        checkAccessibilityPermission()
        checkFullDiskAccess()
    }
    
    /// 检查辅助功能权限（用于获取活跃应用信息）
    private func checkAccessibilityPermission() {
        // 尝试获取当前活跃应用来测试权限
        let frontmostApp = NSWorkspace.shared.frontmostApplication
        hasAccessibilityPermission = frontmostApp != nil
        
        // 更准确的权限检查方法
        let options = [kAXTrustedCheckOptionPrompt.takeUnretainedValue() as String: false]
        hasAccessibilityPermission = AXIsProcessTrustedWithOptions(options as CFDictionary)
    }
    
    /// 检查完全磁盘访问权限（用于读取某些系统信息）
    private func checkFullDiskAccess() {
        // 尝试访问需要完全磁盘访问权限的文件
        let testPath = "/Library/Application Support"
        hasFullDiskAccess = FileManager.default.isReadableFile(atPath: testPath)
    }
    
    // MARK: - 权限请求
    
    /// 请求辅助功能权限
    func requestAccessibilityPermission() {
        let alert = NSAlert()
        alert.messageText = "需要辅助功能权限"
        alert.informativeText = """
        BDoggy需要辅助功能权限来监控当前活跃的应用程序。
        
        这个权限用于：
        • 记录应用使用时间
        • 提供更准确的系统监控数据
        
        您的隐私数据将仅存储在本地，不会上传到任何服务器。
        """
        alert.addButton(withTitle: "打开系统偏好设置")
        alert.addButton(withTitle: "稍后设置")
        alert.addButton(withTitle: "了解更多")
        
        let response = alert.runModal()
        
        switch response {
        case .alertFirstButtonReturn:
            openAccessibilitySettings()
        case .alertThirdButtonReturn:
            showPrivacyPolicy()
        default:
            break
        }
    }
    
    /// 请求完全磁盘访问权限
    func requestFullDiskAccess() {
        let alert = NSAlert()
        alert.messageText = "需要完全磁盘访问权限"
        alert.informativeText = """
        为了获取更详细的系统信息（如温度、风扇速度等），BDoggy需要完全磁盘访问权限。
        
        这是可选权限，不授予也不影响基本功能。
        
        您的数据安全：
        • 所有数据仅存储在本地
        • 不会访问您的个人文件
        • 仅读取系统监控相关信息
        """
        alert.addButton(withTitle: "打开系统偏好设置")
        alert.addButton(withTitle: "跳过")
        
        if alert.runModal() == .alertFirstButtonReturn {
            openFullDiskAccessSettings()
        }
    }
    
    // MARK: - 系统设置导航
    
    /// 打开辅助功能设置页面
    private func openAccessibilitySettings() {
        let url = URL(string: "x-apple.systempreferences:com.apple.preference.security?Privacy_Accessibility")!
        NSWorkspace.shared.open(url)
        
        // 显示操作指导
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.showAccessibilityInstructions()
        }
    }
    
    /// 打开完全磁盘访问设置页面
    private func openFullDiskAccessSettings() {
        let url = URL(string: "x-apple.systempreferences:com.apple.preference.security?Privacy_AllFiles")!
        NSWorkspace.shared.open(url)
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.showFullDiskAccessInstructions()
        }
    }
    
    // MARK: - 用户指导
    
    /// 显示辅助功能权限设置指导
    private func showAccessibilityInstructions() {
        let alert = NSAlert()
        alert.messageText = "设置辅助功能权限"
        alert.informativeText = """
        请按照以下步骤操作：
        
        1. 在左侧列表中找到并选择"辅助功能"
        2. 点击左下角的锁图标并输入密码
        3. 在右侧列表中找到"BDoggy"
        4. 勾选"BDoggy"旁边的复选框
        5. 重启BDoggy应用
        
        如果列表中没有BDoggy，请点击"+"按钮手动添加。
        """
        alert.addButton(withTitle: "我已完成设置")
        alert.addButton(withTitle: "需要帮助")
        
        if alert.runModal() == .alertSecondButtonReturn {
            showHelpInformation()
        } else {
            // 重新检查权限
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                self.checkAllPermissions()
            }
        }
    }
    
    /// 显示完全磁盘访问权限设置指导
    private func showFullDiskAccessInstructions() {
        let alert = NSAlert()
        alert.messageText = "设置完全磁盘访问权限"
        alert.informativeText = """
        请按照以下步骤操作：
        
        1. 在左侧列表中找到并选择"完全磁盘访问权限"
        2. 点击左下角的锁图标并输入密码
        3. 点击"+"按钮
        4. 在应用程序文件夹中找到并选择"BDoggy"
        5. 确保"BDoggy"旁边的复选框已勾选
        
        设置完成后，重启BDoggy以使权限生效。
        """
        alert.addButton(withTitle: "我已完成设置")
        alert.runModal()
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.checkAllPermissions()
        }
    }
    
    /// 显示帮助信息
    private func showHelpInformation() {
        let alert = NSAlert()
        alert.messageText = "需要帮助？"
        alert.informativeText = """
        如果您在设置权限时遇到问题：
        
        1. 确保您有管理员权限
        2. 尝试重启系统偏好设置
        3. 检查macOS版本是否支持
        4. 联系技术支持获取帮助
        
        您也可以选择不授予这些权限，BDoggy的基本功能仍然可用。
        """
        alert.addButton(withTitle: "确定")
        alert.runModal()
    }
    
    // MARK: - 隐私政策
    
    /// 显示隐私政策
    private func showPrivacyPolicy() {
        let alert = NSAlert()
        alert.messageText = "隐私政策"
        alert.informativeText = """
        BDoggy系统监控功能的隐私承诺：
        
        数据收集：
        • 仅收集系统性能指标（CPU、内存、磁盘使用率等）
        • 可选收集应用使用时间（需要您的明确授权）
        • 不收集任何个人文件或敏感信息
        
        数据存储：
        • 所有数据仅存储在您的设备本地
        • 存储位置：~/Library/Application Support/BDoggy/
        • 您可以随时删除所有收集的数据
        
        数据使用：
        • 数据仅用于向您显示系统性能趋势
        • 不会与任何第三方共享
        • 不会上传到任何服务器
        
        您的控制权：
        • 可以随时开启或关闭监控功能
        • 可以选择监控的数据类型
        • 可以设置数据保留时间
        • 可以一键清除所有数据
        """
        alert.addButton(withTitle: "我已了解")
        alert.runModal()
    }
    
    // MARK: - 数据管理
    
    /// 显示数据清除确认对话框
    func showDataClearConfirmation(completion: @escaping (Bool) -> Void) {
        let alert = NSAlert()
        alert.messageText = "清除所有监控数据"
        alert.informativeText = """
        此操作将永久删除所有系统监控数据，包括：
        
        • 历史性能数据
        • 应用使用记录
        • 系统信息记录
        
        此操作无法撤销。您确定要继续吗？
        """
        alert.addButton(withTitle: "清除数据")
        alert.addButton(withTitle: "取消")
        alert.alertStyle = .warning
        
        let response = alert.runModal()
        completion(response == .alertFirstButtonReturn)
    }
    
    /// 获取数据存储位置
    func getDataStoragePath() -> String {
        let appSupportURL = FileManager.default.urls(for: .applicationSupportDirectory, 
                                                    in: .userDomainMask).first!
        let bdoggyURL = appSupportURL.appendingPathComponent("BDoggy")
        return bdoggyURL.path
    }
    
    /// 在Finder中显示数据文件夹
    func showDataFolderInFinder() {
        let path = getDataStoragePath()
        NSWorkspace.shared.selectFile(nil, inFileViewerRootedAtPath: path)
    }
}

// MARK: - 权限状态枚举
enum PermissionStatus {
    case granted
    case denied
    case notDetermined
    case restricted
    
    var description: String {
        switch self {
        case .granted: return "已授权"
        case .denied: return "已拒绝"
        case .notDetermined: return "未确定"
        case .restricted: return "受限制"
        }
    }
}
