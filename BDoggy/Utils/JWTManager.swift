//
//  JWTManager.swift
//  BDoggy
//
//  Created by K4 on 2025/2/17.
//

import Foundation

class JWTManager {
    static let shared = JWTManager()
    private var jwtToken: String?
    static let JWTToken = "JWTToken"
    static let JWTData = "JWTData"

    // 设置 JWT Token
    func setToken(token: String) {
        jwtToken = token
    }

    // 检查 JWT 是否过期
    func isTokenExpired() -> Bool {
        guard let token = jwtToken else { return true }
        let segments = token.split(separator: ".")
        guard segments.count == 3 else { return true }

        // 解码 Payload
        let payloadSegment = segments[1]
        guard let payloadData = base64UrlDecode(payloadSegment) else { return true }

        // 将 JSON 数据转换为字典
        if let payloadDict = try? JSONSerialization.jsonObject(with: payloadData, options: []) as? [String: Any],
           let exp = payloadDict["exp"] as? TimeInterval
        {
            let expirationDate = Date(timeIntervalSince1970: exp)
            return Date() >= expirationDate
        }

        return true
    }

    // 解码 JWT Token 的方法
    func decodeToken() -> [String: Any]? {
        guard let token = jwtToken else { return nil }
        let segments = token.split(separator: ".")
        guard segments.count == 3 else { return nil }

        // 解码 Payload
        let payloadSegment = segments[1]
        guard let payloadData = base64UrlDecode(payloadSegment) else { return nil }

        // 将 JSON 数据转换为字典
        if let payloadDict = try? JSONSerialization.jsonObject(with: payloadData, options: []) as? [String: Any] {
            return payloadDict
        }

        return nil
    }

    // 自动刷新 JWT Token
    func refreshTokenIfNeeded(completion: @escaping (String?) -> Void) {
        if isTokenExpired() {
            // 模拟调用网络请求，刷新 token
            refreshJWT { newToken in
                if let newToken = newToken {
                    self.setToken(token: newToken)
                    completion(newToken)
                } else {
                    completion(nil)
                }
            }
        } else {
            completion(jwtToken)
        }
    }

    // 模拟刷新 Token 的网络请求
    private func refreshJWT(completion: @escaping (String?) -> Void) {
        // 你的网络请求逻辑，调用服务器接口刷新 token
        print("Fetching new JWT token...")
        DispatchQueue.global().asyncAfter(deadline: .now() + 2) { // 模拟网络请求延迟
            let newToken = "newJWTToken123" // 模拟新的 JWT Token
            completion(newToken)
        }
    }

    // Base64 URL 解码
    private func base64UrlDecode(_ value: Substring) -> Data? {
        var base64 = value
            .replacingOccurrences(of: "-", with: "+")
            .replacingOccurrences(of: "_", with: "/")

        // 补齐 Base64 编码长度
        while base64.count % 4 != 0 {
            base64.append("=")
        }

        return Data(base64Encoded: base64)
    }

    func addJWT(to request: inout URLRequest) {
        guard let token = jwtToken else { return }
        request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
    }
}
