//
//  BlackManGIFGenerator.swift
//  BDoggy
//
//  Created by K4 on 2024/12/9.
//

import Foundation
import AppKit

class BlackManGIFGenerator {
    
    static func generateGIFs(for folderNames: [String]) {
        let mainPath = Bundle.main.resourcePath!
        let mainURL = URL(fileURLWithPath: mainPath)
        let bdoggyURL = mainURL.appendingPathComponent("BDoggy")
        let resourcesURL = bdoggyURL.appendingPathComponent("Resources")
        
        for folderName in folderNames {
            print("\n开始处理文件夹: \(folderName)")
            processFolder(named: folderName, in: resourcesURL)
        }
    }
    
    private static func processFolder(named folderName: String, in resourcesURL: URL) {
        let folderPath = resourcesURL.appendingPathComponent(folderName)
        
        do {
            // 检查文件夹是否存在
            let fileManager = FileManager.default
            var isDirectory: ObjCBool = false
            
            if !fileManager.fileExists(atPath: folderPath.path, isDirectory: &isDirectory) || !isDirectory.boolValue {
                print("\(folderName) 文件夹不存在")
                return
            }
            
            // 获取文件夹中的所有文件
            let fileURLs = try fileManager.contentsOfDirectory(at: folderPath, includingPropertiesForKeys: nil)
            
            // 过滤出图片文件（排除GIF文件）并按文件名结尾数字排序
            let imageURLs = fileURLs.filter { url in
                let fileExtension = url.pathExtension.lowercased()
                return ["png", "jpg", "jpeg"].contains(fileExtension)
            }.sorted { url1, url2 in
                // 从文件名中提取数字
                let fileName1 = url1.deletingPathExtension().lastPathComponent
                let fileName2 = url2.deletingPathExtension().lastPathComponent
                
                // 使用正则表达式提取文件名末尾的数字
                let regex = try! NSRegularExpression(pattern: "\\d+$")
                let range1 = NSRange(fileName1.startIndex..., in: fileName1)
                let range2 = NSRange(fileName2.startIndex..., in: fileName2)
                
                let number1 = regex.firstMatch(in: fileName1, range: range1).map {
                    Int(fileName1[Range($0.range, in: fileName1)!]) ?? 0
                } ?? 0
                
                let number2 = regex.firstMatch(in: fileName2, range: range2).map {
                    Int(fileName2[Range($0.range, in: fileName2)!]) ?? 0
                } ?? 0
                
                return number1 < number2
            }
            
            if imageURLs.isEmpty {
                print("在 \(folderName) 中没有找到图片文件")
                return
            }
            
            // 加载图片
            var images: [NSImage] = []
            for imageURL in imageURLs {
                print("加载图片: \(imageURL.lastPathComponent)")
                if let image = NSImage(contentsOf: imageURL) {
                    images.append(image)
                }
            }
            
            if images.isEmpty {
                print("无法加载 \(folderName) 中的图片")
                return
            }
            
            // 创建GIF文件路径
            let gifFileName = "\(folderName).gif"
            let gifURL = folderPath.appendingPathComponent(gifFileName)
            
            // 检查是否已存在同名GIF文件
            if fileManager.fileExists(atPath: gifURL.path) {
                print("已存在同名GIF文件，将覆盖原文件")
            }
            
            // 创建延迟时间数组（每帧0.2秒）
            let delays = Array(repeating: 0.15, count: images.count)
            
            // 创建GIF配置
            let configuration = GIFHelper.GIFConfiguration(
                loopCount: 0,  // 无限循环
                colorModel: kCGImagePropertyColorModelRGB,
                depth: 8,
                hasGlobalColorMap: true
            )
            
            // 创建GIF文件
            let success = GIFHelper.createGIF(
                from: images,
                delays: delays,
                url: gifURL,
                configuration: configuration
            )
            
            if success {
                print("GIF文件创建成功: \(gifURL.path)")
            } else {
                print("GIF文件创建失败")
            }
            
        } catch {
            print("处理 \(folderName) 时出错: \(error.localizedDescription)")
        }
    }
}
