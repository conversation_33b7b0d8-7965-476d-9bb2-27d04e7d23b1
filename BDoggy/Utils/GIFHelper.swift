import ImageIO
import SwiftUI
import UniformTypeIdentifiers

class GIFHelper {
    struct GIFConfiguration {
        var loopCount: Int = 0 // 0 表示无限循环
        var colorModel: CFString = kCGImagePropertyColorModelRGB
        var depth: Int = 8
        var hasGlobalColorMap: Bool = true
    }
    
    // MARK: - GIF 加载和解析

    static func loadGIF(from url: URL) -> (frames: [NSImage], delays: [Double])? {
        guard let imageSource = CGImageSourceCreateWithURL(url as CFURL, nil) else { return nil }
        let frameCount = CGImageSourceGetCount(imageSource)
        var frames: [NSImage] = []
        var delays: [Double] = []
        
        for i in 0 ..< frameCount {
            if let cgImage = CGImageSourceCreateImageAtIndex(imageSource, i, nil) {
                let nsImage = NSImage(cgImage: cgImage, size: .zero)
                frames.append(nsImage)
                
                if let properties = CGImageSourceCopyPropertiesAtIndex(imageSource, i, nil) as? [String: Any],
                   let gifProperties = properties[kCGImagePropertyGIFDictionary as String] as? [String: Any],
                   let delay = gifProperties[kCGImagePropertyGIFDelayTime as String] as? Double
                {
                    delays.append(delay)
                } else {
                    delays.append(0.1)
                }
            }
        }
        
        return frames.isEmpty ? nil : (frames, delays)
    }
    
    // MARK: - GIF 加载和解析

    @MainActor
    static func loadGIFAsync(from url: URL) async -> (frames: [NSImage], delays: [Double])? {
        guard let (data, _) = try? await URLSession.shared.data(from: url),
              let imageSource = CGImageSourceCreateWithData(data as CFData, nil)
        else { return nil }
            
        // 后续解析逻辑...
        let frameCount = CGImageSourceGetCount(imageSource)
        var frames: [NSImage] = []
        var delays: [Double] = []
            
        for i in 0 ..< frameCount {
            if let cgImage = CGImageSourceCreateImageAtIndex(imageSource, i, nil) {
                let nsImage = NSImage(cgImage: cgImage, size: .zero)
                frames.append(nsImage)
                    
                if let properties = CGImageSourceCopyPropertiesAtIndex(imageSource, i, nil) as? [String: Any],
                   let gifProperties = properties[kCGImagePropertyGIFDictionary as String] as? [String: Any],
                   let delay = gifProperties[kCGImagePropertyGIFDelayTime as String] as? Double
                {
                    delays.append(delay)
                } else {
                    delays.append(0.1)
                }
            }
        }
        
        return frames.isEmpty ? nil : (frames, delays)
    }
    
    // MARK: - GIF 创建

    static func createGIF(from images: [NSImage],
                          delays: [Double],
                          url: URL,
                          configuration: GIFConfiguration = GIFConfiguration()) -> Bool
    {
        guard let destination = CGImageDestinationCreateWithURL(url as CFURL,
                                                                UTType.gif.identifier as CFString,
                                                                images.count, nil)
        else {
            return false
        }
        
        let gifProperties = [
            kCGImagePropertyGIFDictionary: [
                kCGImagePropertyGIFHasGlobalColorMap: configuration.hasGlobalColorMap,
                kCGImagePropertyColorModel: configuration.colorModel,
                kCGImagePropertyDepth: configuration.depth,
                kCGImagePropertyGIFLoopCount: configuration.loopCount
            ]
        ] as [String: Any]
        
        CGImageDestinationSetProperties(destination, gifProperties as CFDictionary)
        
        for (index, image) in images.enumerated() {
            guard let cgImage = image.cgImage(forProposedRect: nil, context: nil, hints: nil) else { continue }
            
            let frameProperties = [
                kCGImagePropertyGIFDictionary: [
                    kCGImagePropertyGIFDelayTime: delays[safe: index] ?? 0.1
                ]
            ]
            
            CGImageDestinationAddImage(destination, cgImage, frameProperties as CFDictionary)
        }
        
        return CGImageDestinationFinalize(destination)
    }
}

// MARK: - SwiftUI 支持

struct GIFImage: View {
    let frames: [NSImage]
    let delays: [Double]
    let loopCount: Int
    
    @State private var currentFrame = 0
    @State private var loopCounter = 0
    
    init(url: URL, loopCount: Int = 0) {
        if let (loadedFrames, loadedDelays) = GIFHelper.loadGIF(from: url) {
            self.frames = loadedFrames
            self.delays = loadedDelays
        } else {
            self.frames = []
            self.delays = []
        }
        self.loopCount = loopCount
    }
    
    @State private var isAnimating = false
    @State private var animationTask: Task<Void, Never>?
    
    var body: some View {
        Image(nsImage: frames[safe: currentFrame] ?? NSImage())
            .resizable()
            .scaledToFit()
            .onAppear {
                if !isAnimating {
                    startAnimation()
                }
            }
            .onDisappear {
                stopAnimation()
            }
    }
    
    private func stopAnimation() {
        isAnimating = false
        animationTask?.cancel()
        animationTask = nil
    }
    
    private func startAnimation() {
        guard !frames.isEmpty, !isAnimating else { return }
        isAnimating = true
        
        animationTask = Task { @MainActor in
            while !Task.isCancelled && isAnimating {
                let delay = delays[safe: currentFrame] ?? 0.1
                try? await Task.sleep(for: .seconds(delay))
                
                if !Task.isCancelled && isAnimating {
                    currentFrame = (currentFrame + 1) % frames.count
                    
                    if currentFrame == 0 {
                        loopCounter += 1
                        if loopCount != 0 && loopCounter >= loopCount {
                            stopAnimation()
                            break
                        }
                    }
                }
            }
        }
    }
}

// MARK: - SwiftUI 网络 GIF 支持

struct AsyncGIFImage: View {
    let url: URL
    let loopCount: Int
    let onFailure: (() -> Void)? // 添加失败回调
    
    @State private var frames: [NSImage] = []
    @State private var delays: [Double] = []
    @State private var currentFrame = 0
    @State private var loopCounter = 0
    @State private var isLoading = true
    @State private var loadFailed = false
    
    init(url: URL, loopCount: Int = 0, onFailure: (() -> Void)? = nil) {
        self.url = url
        self.loopCount = loopCount
        self.onFailure = onFailure
    }
    
    var body: some View {
        Group {
            if isLoading {
                ProgressView()
            } else if loadFailed {
                Image(systemName: "photo") // 加载失败显示默认图片
                    .resizable()
                    .scaledToFit()
            } else {
                Image(nsImage: frames[safe: currentFrame] ?? NSImage())
                    .resizable()
                    .scaledToFit()
            }
        }
        .task {
            // 异步加载 GIF
            do {
                if let (loadedFrames, loadedDelays) = await GIFHelper.loadGIFAsync(from: url) {
                    self.frames = loadedFrames
                    self.delays = loadedDelays
                    self.isLoading = false
                    startAnimation()
                } else {
                    throw NSError(domain: "GIFLoadingError", code: -1)
                }
            } catch {
                self.isLoading = false
                self.loadFailed = true
                onFailure?() // 调用失败回调
            }
        }
    }
    
    @State private var isAnimating = false
    @State private var animationTask: Task<Void, Never>?
    
    private func stopAnimation() {
        isAnimating = false
        animationTask?.cancel()
        animationTask = nil
    }
    
    private func startAnimation() {
        guard !frames.isEmpty, !isAnimating else { return }
        isAnimating = true
        
        animationTask = Task { @MainActor in
            while !Task.isCancelled && isAnimating {
                let delay = delays[safe: currentFrame] ?? 0.1
                try? await Task.sleep(for: .seconds(delay))
                
                if !Task.isCancelled && isAnimating {
                    currentFrame = (currentFrame + 1) % frames.count
                    
                    if currentFrame == 0 {
                        loopCounter += 1
                        if loopCount != 0 && loopCounter >= loopCount {
                            stopAnimation()
                            break
                        }
                    }
                }
            }
        }
    }
}
