//
//  COSSigner.swift
//  BDoggy
//
//  Created by K4 on 2025/2/10.
//

import CommonCrypto
import Foundation

// MARK: - COSSigner: 用于腾讯云 COS 请求签名

class COSSigner {
    private let lineSeparator = "\n"
    private let qSignAlgorithm = "sha1"
    
    /// 生成 COS 请求签名
    /// - Parameters:
    ///   - secretId: 用户的 SecretId
    ///   - secretKey: 用户的 SecretKey
    ///   - httpMethod: HTTP 请求方法（GET/POST 等）
    ///   - urlPath: 请求路径
    ///   - headers: HTTP 头部信息
    ///   - queryParams: URL 查询参数
    ///   - expiration: 签名有效期（秒）
    /// - Returns: 计算后的签名字符串
    func sign(
        secretId: String,
        secretKey: String,
        httpMethod: String,
        urlPath: String,
        headers: [String: String],
        queryParams: [String: String],
        expiration: TimeInterval
    ) -> String? {
        let currentTime = Date().timeIntervalSince1970
        let signTime = "\(Int(currentTime));\(Int(currentTime + expiration))"
        // 计算 SignKey
        guard let signKey = hmacSHA1(secretKey, signTime) else { return nil }
        // 生成 Key-Value 串
        let formattedHeaders = formatMap(headers)
        let formattedParams = formatMap(queryParams)
        // 拼接 httpString
        let httpString = "\(httpMethod.lowercased())\n\(urlPath)\n\(formattedParams)\n\(formattedHeaders)\n"
        // 计算 httpString
        guard let SHA1HttpString = sha1Hash(httpString) else { return nil }
        // 拼接 StringToSign
        let stringToSign = "\(qSignAlgorithm)\n\(signTime)\n\(SHA1HttpString)\n"
        // 计算 Signature
        guard let signature = hmacSHA1(signKey, stringToSign) else { return nil }
        
//        print("secretId", secretId)
//        print("secretKey", secretKey)
//        print("signTime", signTime)
//        print("formattedHeaders", formattedHeaders)
//        print("formattedParams", formattedParams)
//        print("httpString", httpString)
//        print("stringToSign", stringToSign)
//        print("signature", signature)
        return "q-sign-algorithm=\(qSignAlgorithm)&q-ak=\(secretId)&q-sign-time=\(signTime)&q-key-time=\(signTime)&q-header-list=\(headerList(headers))&q-url-param-list=\(headerList(queryParams))&q-signature=\(signature)"
    }
    
    /// 计算 HMAC-SHA1
    private func hmacSHA1(_ key: String, _ message: String) -> String? {
        guard let keyData = key.data(using: .utf8), let messageData = message.data(using: .utf8) else { return nil }
        
        var hmac = [UInt8](repeating: 0, count: Int(CC_SHA1_DIGEST_LENGTH))
        keyData.withUnsafeBytes { keyBytes in
            messageData.withUnsafeBytes { messageBytes in
                CCHmac(CCHmacAlgorithm(kCCHmacAlgSHA1), keyBytes.baseAddress, keyData.count, messageBytes.baseAddress, messageData.count, &hmac)
            }
        }
        
        return Data(hmac).map { String(format: "%02x", $0) }.joined()
    }
    
    private func sha1Hash(_ input: String) -> String? {
        let data = Data(input.utf8)
        var digest = [UInt8](repeating: 0, count: Int(CC_SHA1_DIGEST_LENGTH))

        // 计算 SHA1
        data.withUnsafeBytes { buffer in
            _ = CC_SHA1(buffer.baseAddress, CC_LONG(data.count), &digest)
        }

        // 转换成十六进制字符串
        return digest.map { String(format: "%02x", $0) }.joined()
    }

    /// 格式化 Map 为签名需要的格式
    private func formatMap(_ map: [String: String]) -> String {
        return map.sorted { $0.key < $1.key }.map { "\(urlEncode($0.key).lowercased())=\(urlEncode($0.value))" }.joined(separator: "&")
    }
    
    /// 获取 Header 或 URL 参数的 key 列表
    private func headerList(_ map: [String: String]) -> String {
        return map.keys.sorted().map { $0.lowercased() }.joined(separator: ";")
    }
    
    private func urlEncode(_ input: String) -> String {
        var allowedCharacterSet = CharacterSet.urlQueryAllowed
        // 根据 RFC 3986，`+` 和 `=` 应该被编码
        allowedCharacterSet.remove(charactersIn: ";!<\"=#>$?%@&['\\(])^*`+{,|/}:")
        return input.addingPercentEncoding(withAllowedCharacters: allowedCharacterSet) ?? ""
    }
}
