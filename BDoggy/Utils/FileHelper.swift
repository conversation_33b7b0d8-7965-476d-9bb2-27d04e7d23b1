//
//  FileHelper.swift
//  BDoggy
//
//  Created by K4 on 2025/2/12.
//
import Foundation

class FileHelper {
    static let shared = FileHelper() // 单例模式
    
    private let fileManager = FileManager.default
    
    private let rootDirectory: URL
    
    private init() {
        // 获取应用的 Application Support 目录
        let appSupportDirectory = fileManager.urls(for: .applicationSupportDirectory, in: .userDomainMask).first!
        rootDirectory = appSupportDirectory.appendingPathComponent("FrameSequences")
               
        // 创建根目录（如果不存在）
        if !fileManager.fileExists(atPath: rootDirectory.path) {
            try? fileManager.createDirectory(at: rootDirectory, withIntermediateDirectories: true, attributes: nil)
        }
    }
    
    func getUserFrameDirURL(ownerID: String, userID: String, groupName: String) -> URL {
        return getDirectoryURL(owner: ownerID, user: userID, groupName: groupName)
    }
    
    func getSystemFrameDirURL(groupName: String) -> URL {
        return getDirectoryURL(owner: ProductType.system.rawValue, user: ProductType.system.rawValue, groupName: groupName)
    }
    
    // MARK: - 本地文件转存

    func copyResource(from url: String, targetFolder: URL) throws -> URL {
        guard let fileURL = URL(string: url) else {
            throw FileError.invalidURL
        }
        //  生成最终的目标文件路径
        let destinationURL = targetFolder.appendingPathComponent(fileURL.lastPathComponent)

        do {
            if !fileManager.fileExists(atPath: targetFolder.path) {
                try fileManager.createDirectory(at: targetFolder, withIntermediateDirectories: true, attributes: nil)
            }
            if fileManager.fileExists(atPath: destinationURL.path) {
                try fileManager.removeItem(at: destinationURL)
            }
            if let sourceURL = Bundle.main.url(forResource: Utils.urlPathWithoutExtension(urlString: url), withExtension: Utils.urlExtension(urlString: url)) {
                try fileManager.copyItem(at: sourceURL, to: destinationURL)
            }
            return destinationURL
        } catch {
            throw FileError.fileCopyFailed(error)
        }
    }
    
    func copyResourcesToFolder(from urls: [String], targetFolder: URL, completion: @escaping (Result<[URL], FileError>) -> Void) {
        do {
            var savedFiles: [URL] = []
            for (_, url) in urls.enumerated() {
                let savedFile = try copyResource(from: url, targetFolder: targetFolder)
                savedFiles.append(savedFile)
            }
            completion(.success(savedFiles))
        } catch {
            // 检查目录是否存在
            if fileManager.fileExists(atPath: targetFolder.path) {
                // 删除目录及其子文件
                try? fileManager.removeItem(at: targetFolder)
                Logger.debug("转存资源失败，删除目录：\(targetFolder.path)")
            } else {
                Logger.debug("目录不存在，无法删除：\(targetFolder.path)")
            }
            completion(.failure(FileError.fileCopyFailed(error)))
        }
    }
    
    //
    private func getDirectoryURL(owner ownerID: String, user userID: String, groupName: String) -> URL {
        let newGroupDirectory = rootDirectory.appendingPathComponent("\(ownerID)&\(userID)&\(Utils.urlEncode(groupName))")
        return newGroupDirectory
    }
    
    func scanFolders(for owners: [String]) -> [FrameGroup] {
        var frameGroups: [FrameGroup] = []
        
        do {
            // 获取目录下的所有子文件夹
            let subdirectories = try fileManager.contentsOfDirectory(at: rootDirectory, includingPropertiesForKeys: nil, options: .skipsHiddenFiles)
            
            for subdirectory in subdirectories where subdirectory.hasDirectoryPath {
                let folderName = subdirectory.lastPathComponent
                
                // 检查是否符合命名规则 {owner}_{publisher}_{groupName}
                let components = folderName.split(separator: "&", omittingEmptySubsequences: false)
                guard components.count == 3 else { continue }
                
                let parsedOwner = String(components[0])
                let parsedPublisher = String(components[1])
                let groupName = Utils.urlDecode(String(components[2]))
                
                // 只处理匹配的 owner
                guard owners.contains(parsedOwner) else { continue }
                
                // 获取子文件夹中的所有文件
                let files = try fileManager.contentsOfDirectory(at: subdirectory, includingPropertiesForKeys: nil, options: .skipsHiddenFiles)
                
                // 查找以 groupName 开头并且扩展名符合的所有文件
                let matchingFiles = files.filter { fileURL in
                    let fileNameWithoutExtension = fileURL.deletingPathExtension().lastPathComponent
                    let fileExtension = fileURL.pathExtension.lowercased()
                    return fileNameWithoutExtension.hasPrefix(groupName) && Constants.ImageExtensions.contains(fileExtension)
                }
                .sorted { lhs, rhs -> Bool in
                    // 提取左侧字符串的数字部分
                    let lhsNumber = extractTrailingNumber(from: lhs.lastPathComponent)
                    // 提取右侧字符串的数字部分
                    let rhsNumber = extractTrailingNumber(from: rhs.lastPathComponent)
                    // 如果两个字符串都包含有效的数字，则比较它们
                    if let lhsNum = lhsNumber, let rhsNum = rhsNumber {
                        return lhsNum < rhsNum
                    }
                    // 如果只有一个字符串包含有效数字，那么含有数字的排在前面
                    if lhsNumber != nil {
                        return true
                    }
                    if rhsNumber != nil {
                        return false
                    }
                    // 如果都不含数字，或者都没有有效数字，则按原始字符串排序
                    return lhs.lastPathComponent < rhs.lastPathComponent
                } // 按文件名升序排序
                
                if !matchingFiles.isEmpty {
                    frameGroups.append(FrameGroup(dirName: folderName, groupName: groupName, owner: parsedOwner, publisher: parsedPublisher, fileURLs: matchingFiles))
                }
            }
        } catch {
            Logger.debug("扫描目录失败：\(error)")
        }
        
        return frameGroups
    }
    
    /// 检查指定文件夹是否存在，并返回文件夹路径和其中的图片文件名数组
    func scanFolder(from folderName: String) -> (folderPath: URL?, imageFileNames: [String]) {
        // 构建目标文件夹的完整路径
        let targetFolderPath = rootDirectory.appendingPathComponent(folderName)
        
        // 检查文件夹是否存在
        if !fileManager.fileExists(atPath: targetFolderPath.path) {
            Logger.debug("Folder '\(folderName)' does not exist.")
            return (nil, [])
        }
        
        // 获取文件夹中的所有内容
        do {
            let contents = try FileManager.default.contentsOfDirectory(at: targetFolderPath, includingPropertiesForKeys: nil)
            
            // 过滤出所有的图片文件
            let imageFiles = contents.filter { url in
                let pathExtension = url.pathExtension.lowercased()
                return Constants.ImageExtensions.contains(pathExtension)
            }
            // 提取图片文件的文件名
            let imageFileNames = imageFiles.map { $0.lastPathComponent }
            // 调用通用排序方法
            let sortedImageFileNames = sortStringsByTrailingNumber(strings: imageFileNames)
            
            return (targetFolderPath, sortedImageFileNames)
        } catch {
            Logger.debug("Failed to scan folder: \(error.localizedDescription)")
            return (nil, [])
        }
    }
    
    // 定义一个通用的排序函数
    private func sortStringsByTrailingNumber(strings: [String]) -> [String] {
        return strings.sorted { lhs, rhs -> Bool in
            // 提取左侧字符串的数字部分
            let lhsNumber = extractTrailingNumber(from: lhs)
            // 提取右侧字符串的数字部分
            let rhsNumber = extractTrailingNumber(from: rhs)
            
            // 如果两个字符串都包含有效的数字，则比较它们
            if let lhsNum = lhsNumber, let rhsNum = rhsNumber {
                return lhsNum < rhsNum
            }
            // 如果只有一个字符串包含有效数字，那么含有数字的排在前面
            if lhsNumber != nil {
                return true
            }
            if rhsNumber != nil {
                return false
            }
            // 如果都不含数字，或者都没有有效数字，则按原始字符串排序
            return lhs < rhs
        }
    }
    
    // 辅助函数：从字符串中提取末尾的数字
    private func extractTrailingNumber(from string: String) -> Int? {
        // 使用正则表达式匹配文件名中的最后一组数字
        let pattern = "[0-9]+"
        let regex = try! NSRegularExpression(pattern: pattern)
        let range = NSRange(location: 0, length: string.utf16.count)
        
        // 获取所有匹配项
        let matches = regex.matches(in: string, range: range)
        
        // 取最后一个匹配的数字（如果有的话）
        if let lastMatch = matches.last,
           let numberRange = Range(lastMatch.range, in: string)
        {
            let numberString = String(string[numberRange])
            return Int(numberString)
        }
        
        return nil
    }
}

struct FrameGroup: Codable, Hashable, Identifiable {
    var id = UUID()
    let dirName: String
    var groupName: String
    let owner: String
    let publisher: String
    let fileURLs: [URL]
}

/// **网络错误类型**
enum FileError: Error {
    case invalidURL
    case fileCopyFailed(Error)
}
