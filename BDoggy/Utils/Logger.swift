import Foundation
import os.log

/// 增强版日志级别
enum LogLevel: Int, CaseIterable, Codable {
    case debug = 0
    case info = 1
    case warning = 2
    case error = 3
    case success = 4
    case critical = 5

    var emoji: String {
        switch self {
        case .debug: return "🔍"
        case .info: return "ℹ️"
        case .warning: return "⚠️"
        case .error: return "❌"
        case .success: return "✅"
        case .critical: return "🚨"
        }
    }

    var displayName: String {
        switch self {
        case .debug: return "DEBUG"
        case .info: return "INFO"
        case .warning: return "WARNING"
        case .error: return "ERROR"
        case .success: return "SUCCESS"
        case .critical: return "CRITICAL"
        }
    }

    var osLogType: OSLogType {
        switch self {
        case .debug: return .debug
        case .info: return .info
        case .warning: return .default
        case .error: return .error
        case .success: return .info
        case .critical: return .fault
        }
    }
}

// MARK: - 数据模型

/// 统一日志条目
struct LogEntry: Codable {
    let timestamp: String
    let level: LogLevel
    let category: String
    let message: String
    let file: String
    let function: String
    let line: Int

    var formattedTimestamp: String {
        return timestamp
    }
}

/// 增强版日志管理器
/// 支持文件日志、系统日志和控制台输出
enum Logger {
    // MARK: - 配置
    static var currentLevel: LogLevel = .info
    static var enableFileLogging = true
    static var enableConsoleLogging = true
    private static var isPerformanceTrackingEnabled = true

    // MARK: - 私有属性

    private static let subsystem = "Bdoggy"
    private static let category = "General"
    private static let osLog = OSLog(subsystem: subsystem, category: category)

    private static let errorTracker = LoggerErrorTracker()
    private static let performanceTracker = LoggerPerformanceTracker()

    private static let dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss.SSS"
        return formatter
    }()

    private static let logFileURL: URL = {
        let appSupportURL = FileManager.default.urls(for: .applicationSupportDirectory, in: .userDomainMask).first!
        let appDataURL = appSupportURL.appendingPathComponent("BDoggy")

        // 确保目录存在
        try? FileManager.default.createDirectory(at: appDataURL, withIntermediateDirectories: true)

        return appDataURL.appendingPathComponent("bdoggy.log")
    }()

    private static let logQueue = DispatchQueue(label: "com.bdoggy.logger", qos: .utility)

    // MARK: - 公共方法（保持向后兼容）

    static func debug(_ items: Any..., separator: String = " ", terminator: String = "\n") {
        let message = items.map { "\($0)" }.joined(separator: separator)
        log(level: .debug, message: message, category: "General")
    }

    static func info(_ items: Any..., separator: String = " ", terminator: String = "\n") {
        let message = items.map { "\($0)" }.joined(separator: separator)
        log(level: .info, message: message, category: "General")
    }

    static func warning(_ items: Any..., separator: String = " ", terminator: String = "\n") {
        let message = items.map { "\($0)" }.joined(separator: separator)
        log(level: .warning, message: message, category: "General")
    }

    static func error(_ items: Any..., separator: String = " ", terminator: String = "\n") {
        let message = items.map { "\($0)" }.joined(separator: separator)
        log(level: .error, message: message, category: "General")
    }

    static func success(_ items: Any..., separator: String = " ", terminator: String = "\n") {
        let message = items.map { "\($0)" }.joined(separator: separator)
        log(level: .success, message: message, category: "General")
    }

    // MARK: - 增强版日志方法

    /// 记录调试信息（带文件位置）
    static func debug(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        log(level: .debug, message: message, category: "General", file: file, function: function, line: line)
    }

    /// 记录一般信息（带文件位置）
    static func info(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        log(level: .info, message: message, category: "General", file: file, function: function, line: line)
    }

    /// 记录警告信息（带文件位置）
    static func warning(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        log(level: .warning, message: message, category: "General", file: file, function: function, line: line)
    }

    /// 记录错误信息（带文件位置）
    static func error(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        log(level: .error, message: message, category: "General", file: file, function: function, line: line)
    }

    /// 记录成功信息（带文件位置）
    static func success(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        log(level: .success, message: message, category: "General", file: file, function: function, line: line)
    }

    // MARK: - 增强API（支持category和error对象）

    /// 调试日志（增强版）
    static func debug(
        _ message: String,
        category: String = "General",
        error: Error? = nil,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        log(level: .debug, message: message, category: category, error: error, file: file, function: function, line: line)
    }

    /// 信息日志（增强版）
    static func info(
        _ message: String,
        category: String = "General",
        error: Error? = nil,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        log(level: .info, message: message, category: category, error: error, file: file, function: function, line: line)
    }

    /// 警告日志（增强版）
    static func warning(
        _ message: String,
        category: String = "General",
        error: Error? = nil,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        log(level: .warning, message: message, category: category, error: error, file: file, function: function, line: line)
    }

    /// 错误日志（增强版）
    static func error(
        _ message: String,
        category: String = "General",
        error: Error? = nil,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        log(level: .error, message: message, category: category, error: error, file: file, function: function, line: line)
    }

    /// 成功日志（增强版）
    static func success(
        _ message: String,
        category: String = "General",
        error: Error? = nil,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        log(level: .success, message: message, category: category, error: error, file: file, function: function, line: line)
    }

    /// 严重错误日志（增强版）
    static func critical(
        _ message: String,
        category: String = "General",
        error: Error? = nil,
        file: String = #file,
        function: String = #function,
        line: Int = #line
    ) {
        log(level: .critical, message: message, category: category, error: error, file: file, function: function, line: line)
    }

    // MARK: - 核心日志方法

    private static func log(
        level: LogLevel,
        message: String,
        category: String,
        error: Error? = nil,
        file: String = "",
        function: String = "",
        line: Int = 0
    ) {
        guard level.rawValue >= currentLevel.rawValue else { return }

        let fileName = URL(fileURLWithPath: file).lastPathComponent
        let timestamp = dateFormatter.string(from: Date())

        var fullMessage = message
        if let error = error {
            fullMessage += " | Error: \(error.localizedDescription)"
        }

        let logEntry = LogEntry(
            timestamp: timestamp,
            level: level,
            category: category,
            message: fullMessage,
            file: fileName,
            function: function,
            line: line
        )

        // 输出到控制台
        if enableConsoleLogging {
            logToConsole(logEntry)
        }

        // 输出到系统日志
        logToSystem(logEntry)

        // 文件日志
        if enableFileLogging {
            logToFile(logEntry)
        }

        // 错误跟踪
        if level == .error || level == .critical {
            errorTracker.recordError(logEntry)
        }
    }

    private static func logToConsole(_ entry: LogEntry) {
        #if DEBUG
        let formattedMessage = "\(entry.level.emoji) [\(entry.formattedTimestamp)] [\(entry.level.displayName)] [\(entry.category)] \(entry.message) (\(entry.file):\(entry.function):\(entry.line))"
        print(formattedMessage)
        #endif
    }

    // MARK: - 系统日志

    private static func logToSystem(_ entry: LogEntry) {
        let osLog = OSLog(subsystem: "BDoggy", category: entry.category)
        let formattedMessage = "\(entry.message) (\(entry.file):\(entry.function):\(entry.line))"

        os_log("%{public}@", log: osLog, type: entry.level.osLogType, formattedMessage)
    }

    // MARK: - 文件日志

    private static func logToFile(_ entry: LogEntry) {
        logQueue.async {
            let formattedMessage = "[\(entry.formattedTimestamp)] [\(entry.level.displayName)] [\(entry.category)]\(entry.message) (\(entry.file):\(entry.function):\(entry.line))"
            let logMsg = formattedMessage + "\n"

            if let data = logMsg.data(using: .utf8) {
                if FileManager.default.fileExists(atPath: logFileURL.path) {
                    // 追加到现有文件
                    if let fileHandle = try? FileHandle(forWritingTo: logFileURL) {
                        fileHandle.seekToEndOfFile()
                        fileHandle.write(data)
                        fileHandle.closeFile()
                    }
                } else {
                    // 创建新文件
                    try? data.write(to: logFileURL)
                }
            }
        }
    }

    // MARK: - 性能监控API

    /// 测量代码块执行时间（向后兼容）
    @discardableResult
    static func measure<T>(_ description: String, block: () throws -> T) rethrows -> T {
        return try measureTime(description, category: "Performance", block: block)
    }

    /// 测量代码块执行时间（增强版）
    @discardableResult
    static func measureTime<T>(
        _ description: String,
        category: String = "Performance",
        block: () throws -> T
    ) rethrows -> T {
        let startTime = CFAbsoluteTimeGetCurrent()
        let result = try block()
        let executionTime = (CFAbsoluteTimeGetCurrent() - startTime) * 1000 // 毫秒

        if isPerformanceTrackingEnabled {
            performanceTracker.recordOperation(description, category: category, executionTime: executionTime)

            if executionTime > 100 { // 慢操作检测
                warning("慢操作检测: \(description) 耗时 \(String(format: "%.2f", executionTime))ms", category: category)
            } else {
                debug("操作完成: \(description) 耗时 \(String(format: "%.2f", executionTime))ms", category: category)
            }
        }

        return result
    }

    /// 记录性能日志（向后兼容）
    static func performance(_ message: String, file: String = #file, function: String = #function, line: Int = #line) {
        log(level: .info, message: "[PERFORMANCE] \(message)", category: "Performance", file: file, function: function, line: line)
    }

    // MARK: - 数据库操作专用API

    /// 记录数据库操作
    static func logDatabaseOperation(
        _ operation: String,
        table: String? = nil,
        rowsAffected: Int? = nil,
        executionTimeMs: Double? = nil
    ) {
        var message = "数据库操作: \(operation)"

        if let table = table {
            message += " | 表: \(table)"
        }

        if let rows = rowsAffected {
            message += " | 影响行数: \(rows)"
        }

        if let time = executionTimeMs {
            message += " | 耗时: \(String(format: "%.2f", time))ms"

            if time > 1000 {
                warning(message, category: "Database")
            } else {
                debug(message, category: "Database")
            }
        } else {
            info(message, category: "Database")
        }
    }

    // MARK: - 日志管理

    /// 获取日志文件路径
    static func getLogFilePath() -> String {
        return logFileURL.path
    }

    /// 获取日志文件大小
    static func getLogFileSize() -> Int64 {
        do {
            let attributes = try FileManager.default.attributesOfItem(atPath: logFileURL.path)
            return attributes[.size] as? Int64 ?? 0
        } catch {
            return 0
        }
    }

    /// 清除日志文件
    static func clearLogFile() -> Bool {
        do {
            if FileManager.default.fileExists(atPath: logFileURL.path) {
                try FileManager.default.removeItem(at: logFileURL)
            }
            return true
        } catch {
            Logger.error("清除日志文件失败: \(error)")
            return false
        }
    }

    /// 导出日志文件到指定位置
    static func exportLogFile(to destinationURL: URL) -> Bool {
        do {
            // 检查源文件是否存在
            guard FileManager.default.fileExists(atPath: logFileURL.path) else {
                error("日志文件不存在，无法导出")
                return false
            }

            // 如果目标文件已存在，先删除
            if FileManager.default.fileExists(atPath: destinationURL.path) {
                try FileManager.default.removeItem(at: destinationURL)
            }

            // 复制文件到目标位置
            try FileManager.default.copyItem(at: logFileURL, to: destinationURL)

            info("日志文件已成功导出到: \(destinationURL.path)")
            return true
        } catch {
            Logger.error("导出日志文件失败: \(error.localizedDescription)")
            return false
        }
    }

    /// 导出过滤后的日志文件
    /// - Parameters:
    ///   - destinationURL: 目标文件URL
    ///   - logLevel: 最低日志级别过滤（可选）
    ///   - startDate: 开始时间过滤（可选）
    ///   - endDate: 结束时间过滤（可选）
    ///   - maxLines: 最大行数限制（可选）
    /// - Returns: 导出是否成功
    static func exportFilteredLogFile(
        to destinationURL: URL,
        logLevel: LogLevel? = nil,
        startDate: Date? = nil,
        endDate: Date? = nil,
        maxLines: Int? = nil
    ) -> Bool {
        do {
            // 检查源文件是否存在
            guard FileManager.default.fileExists(atPath: logFileURL.path) else {
                error("日志文件不存在，无法导出")
                return false
            }

            // 读取原始日志内容
            let content = try String(contentsOf: logFileURL, encoding: .utf8)
            let allLines = content.components(separatedBy: .newlines).filter { !$0.isEmpty }

            // 应用过滤条件
            var filteredLines = allLines

            // 按日志级别过滤
            if let minLevel = logLevel {
                filteredLines = filteredLines.filter { line in
                    for level in LogLevel.allCases {
                        if level.rawValue >= minLevel.rawValue &&
                            (line.contains(level.emoji) || line.contains(level.displayName))
                        {
                            return true
                        }
                    }
                    return false
                }
            }

            // 按时间范围过滤
            if startDate != nil || endDate != nil {
                filteredLines = filteredLines.filter { line in
                    guard let timestamp = extractTimestamp(from: line) else { return true }

                    if let start = startDate, timestamp < start { return false }
                    if let end = endDate, timestamp > end { return false }

                    return true
                }
            }

            // 限制最大行数
            if let maxLines = maxLines, filteredLines.count > maxLines {
                filteredLines = Array(filteredLines.suffix(maxLines))
            }

            // 生成导出内容
            let exportContent = generateExportContent(lines: filteredLines, originalCount: allLines.count)

            // 如果目标文件已存在，先删除
            if FileManager.default.fileExists(atPath: destinationURL.path) {
                try FileManager.default.removeItem(at: destinationURL)
            }

            // 写入过滤后的内容
            try exportContent.write(to: destinationURL, atomically: true, encoding: .utf8)

            info("过滤后的日志文件已导出到: \(destinationURL.path)")
            info("原始行数: \(allLines.count), 导出行数: \(filteredLines.count)")
            return true
        } catch {
            Logger.error("导出过滤日志文件失败: \(error.localizedDescription)")
            return false
        }
    }

    /// 生成导出内容，包含元数据
    private static func generateExportContent(lines: [String], originalCount: Int) -> String {
        let header = """
        # BDoggy 系统监控日志导出
        # 导出时间: \(Date())
        # 原始日志行数: \(originalCount)
        # 导出日志行数: \(lines.count)
        # 日志文件路径: \(logFileURL.path)
        # =====================================

        """

        let content = lines.joined(separator: "\n")

        let footer = """

        # =====================================
        # 导出完成
        """

        return header + content + footer
    }

    /// 导出最近N天的日志
    static func exportRecentLogs(to destinationURL: URL, days: Int = 7) -> Bool {
        let startDate = Date().addingTimeInterval(-TimeInterval(days * 24 * 3600))
        return exportFilteredLogFile(
            to: destinationURL,
            startDate: startDate,
            endDate: Date()
        )
    }

    /// 导出错误和警告日志
    static func exportErrorsAndWarnings(to destinationURL: URL, maxLines: Int = 1000) -> Bool {
        return exportFilteredLogFile(
            to: destinationURL,
            logLevel: .warning,
            maxLines: maxLines
        )
    }

    /// 导出仅错误日志
    static func exportErrorsOnly(to destinationURL: URL, maxLines: Int = 500) -> Bool {
        return exportFilteredLogFile(
            to: destinationURL,
            logLevel: .error,
            maxLines: maxLines
        )
    }

    /// 获取建议的导出文件名
    static func getSuggestedExportFileName(prefix: String = "system_monitor") -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd_HH-mm-ss"
        let timestamp = dateFormatter.string(from: Date())
        return "\(prefix)_\(timestamp).log"
    }

    /// 获取导出统计信息
    static func getExportStats() -> [String: Any] {
        guard FileManager.default.fileExists(atPath: logFileURL.path) else {
            return ["error": "日志文件不存在"]
        }

        do {
            let content = try String(contentsOf: logFileURL, encoding: .utf8)
            let lines = content.components(separatedBy: .newlines).filter { !$0.isEmpty }

            let levelStats = getLogLevelStats()
            let fileSize = getLogFileSize()
            let timestamps = getLogFileTimestamps()

            return [
                "total_lines": lines.count,
                "file_size_bytes": fileSize,
                "file_size_mb": Double(fileSize) / (1024 * 1024),
                "level_stats": levelStats,
                "created_date": timestamps["created"] as Any,
                "modified_date": timestamps["modified"] as Any,
                "exportable": true
            ]
        } catch {
            return ["error": "读取日志文件失败: \(error.localizedDescription)"]
        }
    }

    /// 设置日志级别
    static func setLogLevel(_ level: LogLevel) {
        currentLevel = level
        info("日志级别已设置为: \(level.displayName)")
    }

    /// 启用/禁用文件日志
    static func setFileLogging(enabled: Bool) {
        enableFileLogging = enabled
        info("文件日志已\(enabled ? "启用" : "禁用")")
    }

    /// 启用/禁用控制台日志
    static func setConsoleLogging(enabled: Bool) {
        enableConsoleLogging = enabled
        if enabled {
            print("控制台日志已启用")
        }
    }

    /// 启用/禁用性能跟踪
    static func setPerformanceTracking(enabled: Bool) {
        isPerformanceTrackingEnabled = enabled
        info("性能跟踪已\(enabled ? "启用" : "禁用")", category: "Logger")
    }

    /// 获取日志统计信息
    static func getLogStats() -> [String: Any] {
        let fileSize = getLogFileSize()
        let fileSizeMB = Double(fileSize) / (1024 * 1024)

        // 分析日志文件内容（如果存在）
        var logStats: [String: Any] = [
            "log_file_path": logFileURL.path,
            "log_file_size_bytes": fileSize,
            "log_file_size_mb": String(format: "%.2f", fileSizeMB),
            "current_level": currentLevel.displayName,
            "file_logging_enabled": enableFileLogging,
            "console_logging_enabled": enableConsoleLogging
        ]

        // 如果日志文件存在，分析内容
        if FileManager.default.fileExists(atPath: logFileURL.path) {
            let contentStats = analyzeLogContent()
            logStats.merge(contentStats) { _, new in new }
        }

        return logStats
    }

    /// 分析日志文件内容
    private static func analyzeLogContent() -> [String: Any] {
        var stats: [String: Any] = [:]

        do {
            let content = try String(contentsOf: logFileURL, encoding: .utf8)
            let lines = content.components(separatedBy: .newlines).filter { !$0.isEmpty }

            stats["total_lines"] = lines.count

            // 统计各级别日志数量
            var levelCounts: [String: Int] = [:]
            var errorMessages: [String] = []
            var warningMessages: [String] = []

            for line in lines {
                // 检查日志级别
                for level in LogLevel.allCases {
                    if line.contains(level.emoji) || line.contains(level.displayName) {
                        levelCounts[level.displayName] = (levelCounts[level.displayName] ?? 0) + 1

                        // 收集错误和警告消息
                        if level == .error, errorMessages.count < 5 {
                            errorMessages.append(extractLogMessage(from: line))
                        } else if level == .warning, warningMessages.count < 5 {
                            warningMessages.append(extractLogMessage(from: line))
                        }
                        break
                    }
                }
            }

            stats["level_counts"] = levelCounts
            stats["recent_errors"] = errorMessages
            stats["recent_warnings"] = warningMessages

            // 分析时间范围
            if let firstLine = lines.first, let lastLine = lines.last {
                if let firstTime = extractTimestamp(from: firstLine),
                   let lastTime = extractTimestamp(from: lastLine)
                {
                    stats["first_log_time"] = firstTime.timeIntervalSince1970
                    stats["last_log_time"] = lastTime.timeIntervalSince1970
                    stats["log_duration_hours"] = lastTime.timeIntervalSince(firstTime) / 3600
                }
            }

        } catch {
            stats["analysis_error"] = error.localizedDescription
        }

        return stats
    }

    /// 获取错误统计
    static func getErrorStats(days: Int = 7) -> LoggerErrorStats {
        return errorTracker.getErrorStats(days: days)
    }

    /// 获取性能统计
    static func getPerformanceStats(days: Int = 7) -> [LoggerPerformanceStats] {
        return performanceTracker.getPerformanceStats(days: days)
    }

    /// 从日志行中提取消息内容
    private static func extractLogMessage(from line: String) -> String {
        // 查找 " - " 后的内容作为消息
        if let range = line.range(of: " - ") {
            return String(line[range.upperBound...]).trimmingCharacters(in: .whitespacesAndNewlines)
        }
        return line.trimmingCharacters(in: .whitespacesAndNewlines)
    }

    /// 从日志行中提取时间戳
    private static func extractTimestamp(from line: String) -> Date? {
        // 查找格式为 [yyyy-MM-dd HH:mm:ss.SSS] 的时间戳
        let pattern = #"\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3})\]"#

        do {
            let regex = try NSRegularExpression(pattern: pattern)
            let range = NSRange(location: 0, length: line.utf16.count)

            if let match = regex.firstMatch(in: line, range: range),
               let timestampRange = Range(match.range(at: 1), in: line)
            {
                let timestampString = String(line[timestampRange])
                return dateFormatter.date(from: timestampString)
            }
        } catch {
            // 正则表达式错误，忽略
        }

        return nil
    }

    /// 获取简化的日志统计信息
    static func getSimpleLogStats() -> [String: Any] {
        let fileSize = getLogFileSize()
        let fileSizeMB = Double(fileSize) / (1024 * 1024)

        return [
            "file_size_mb": String(format: "%.2f", fileSizeMB),
            "current_level": currentLevel.displayName,
            "file_logging": enableFileLogging,
            "console_logging": enableConsoleLogging
        ]
    }

    /// 获取日志级别统计
    static func getLogLevelStats() -> [String: Int] {
        guard FileManager.default.fileExists(atPath: logFileURL.path) else {
            return [:]
        }

        do {
            let content = try String(contentsOf: logFileURL, encoding: .utf8)
            let lines = content.components(separatedBy: .newlines).filter { !$0.isEmpty }

            var levelCounts: [String: Int] = [:]

            for line in lines {
                for level in LogLevel.allCases {
                    if line.contains(level.emoji) || line.contains(level.displayName) {
                        levelCounts[level.displayName] = (levelCounts[level.displayName] ?? 0) + 1
                        break
                    }
                }
            }

            return levelCounts
        } catch {
            Logger.error("获取日志级别统计失败: \(error)")
            return [:]
        }
    }

    /// 获取最近的错误日志
    static func getRecentErrors(limit: Int = 10) -> [String] {
        guard FileManager.default.fileExists(atPath: logFileURL.path) else {
            return []
        }

        do {
            let content = try String(contentsOf: logFileURL, encoding: .utf8)
            let lines = content.components(separatedBy: .newlines).filter { !$0.isEmpty }

            var errorMessages: [String] = []

            // 从后往前查找错误日志
            for line in lines.reversed() {
                if line.contains(LogLevel.error.emoji) || line.contains(LogLevel.error.displayName), errorMessages.count < limit {
                    errorMessages.append(extractLogMessage(from: line))
                }
            }

            return errorMessages.reversed()
        } catch {
            return ["获取错误日志失败: \(error.localizedDescription)"]
        }
    }

    /// 获取最近的警告日志
    static func getRecentWarnings(limit: Int = 10) -> [String] {
        guard FileManager.default.fileExists(atPath: logFileURL.path) else {
            return []
        }

        do {
            let content = try String(contentsOf: logFileURL, encoding: .utf8)
            let lines = content.components(separatedBy: .newlines).filter { !$0.isEmpty }

            var warningMessages: [String] = []

            // 从后往前查找警告日志
            for line in lines.reversed() {
                if line.contains(LogLevel.warning.emoji) || line.contains(LogLevel.warning.displayName), warningMessages.count < limit {
                    warningMessages.append(extractLogMessage(from: line))
                }
            }

            return warningMessages.reversed()
        } catch {
            return ["获取警告日志失败: \(error.localizedDescription)"]
        }
    }

    /// 获取日志文件的创建和修改时间
    static func getLogFileTimestamps() -> [String: Date?] {
        guard FileManager.default.fileExists(atPath: logFileURL.path) else {
            return ["created": nil, "modified": nil]
        }

        do {
            let attributes = try FileManager.default.attributesOfItem(atPath: logFileURL.path)
            return [
                "created": attributes[.creationDate] as? Date,
                "modified": attributes[.modificationDate] as? Date
            ]
        } catch {
            return ["created": nil, "modified": nil]
        }
    }

    /// 检查日志文件健康状态
    static func checkLogHealth() -> [String: Any] {
        var health: [String: Any] = [:]

        // 检查文件大小
        let fileSize = getLogFileSize()
        let fileSizeMB = Double(fileSize) / (1024 * 1024)

        health["file_exists"] = FileManager.default.fileExists(atPath: logFileURL.path)
        health["file_size_mb"] = fileSizeMB
        health["size_status"] = fileSizeMB > 50 ? "large" : (fileSizeMB > 10 ? "medium" : "normal")

        // 检查权限
        health["readable"] = FileManager.default.isReadableFile(atPath: logFileURL.path)
        health["writable"] = FileManager.default.isWritableFile(atPath: logFileURL.path)

        // 检查最近是否有日志写入
        let timestamps = getLogFileTimestamps()
        if let modified = timestamps["modified"] {
            let timeSinceModified = Date().timeIntervalSince(modified ?? Date())
            health["last_modified_hours_ago"] = timeSinceModified / 3600
            health["recently_active"] = timeSinceModified < 3600 // 1小时内有活动
        }

        // 检查错误率
        let levelStats = getLogLevelStats()
        let totalLogs = levelStats.values.reduce(0, +)
        let errorCount = levelStats["ERROR"] ?? 0

        if totalLogs > 0 {
            let errorRate = Double(errorCount) / Double(totalLogs)
            health["error_rate"] = errorRate
            health["error_status"] = errorRate > 0.1 ? "high" : (errorRate > 0.05 ? "medium" : "low")
        }

        return health
    }
}

// MARK: - 错误跟踪器

class LoggerErrorTracker {
    private var errors: [LogEntry] = []
    private let errorsLock = NSLock()
    private let maxErrors = 1000

    func recordError(_ entry: LogEntry) {
        errorsLock.lock()
        defer { errorsLock.unlock() }

        errors.append(entry)

        // 保持数组大小在限制内
        if errors.count > maxErrors {
            errors.removeFirst(errors.count - maxErrors)
        }
    }

    func getErrorStats(days: Int) -> LoggerErrorStats {
        errorsLock.lock()
        defer { errorsLock.unlock() }

        let cutoffDate = Date().addingTimeInterval(-TimeInterval(days * 24 * 3600))
        let recentErrors = errors.filter { $0.timestamp.toDate() >= cutoffDate }

        let totalErrors = recentErrors.filter { $0.level == .error }.count
        let totalWarnings = recentErrors.filter { $0.level == .warning }.count
        let totalCritical = recentErrors.filter { $0.level == .critical }.count

        let errorsByCategory = Dictionary(grouping: recentErrors, by: { $0.category })
            .mapValues { $0.count }

        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let errorsByDay = Dictionary(grouping: recentErrors, by: { $0.timestamp })
            .mapValues { $0.count }

        let topErrors = Dictionary(grouping: recentErrors, by: { $0.message })
            .sorted { $0.value.count > $1.value.count }
            .prefix(5)
            .map { $0.key }

        return LoggerErrorStats(
            totalErrors: totalErrors,
            totalWarnings: totalWarnings,
            totalCritical: totalCritical,
            errorsByCategory: errorsByCategory,
            errorsByDay: errorsByDay,
            topErrors: Array(topErrors),
            period: "\(days)天"
        )
    }
}

/// 统一错误统计
struct LoggerErrorStats: Codable {
    let totalErrors: Int
    let totalWarnings: Int
    let totalCritical: Int
    let errorsByCategory: [String: Int]
    let errorsByDay: [String: Int]
    let topErrors: [String]
    let period: String

    var hasIssues: Bool {
        return totalErrors > 0 || totalWarnings > 0 || totalCritical > 0
    }

    var severityLevel: String {
        if totalCritical > 0 {
            return "严重"
        } else if totalErrors > 10 {
            return "高"
        } else if totalErrors > 0 || totalWarnings > 5 {
            return "中"
        } else {
            return "低"
        }
    }
}

/// 统一性能统计
struct LoggerPerformanceStats: Codable {
    let operation: String
    let category: String
    let averageTime: Double
    let maxTime: Double
    let minTime: Double
    let callCount: Int
    let slowOperations: Int
}

// MARK: - 性能跟踪器

class LoggerPerformanceTracker {
    private var operations: [PerformanceRecord] = []
    private let operationsLock = NSLock()
    private let maxRecords = 1000

    private struct PerformanceRecord {
        let operation: String
        let category: String
        let executionTime: Double
        let timestamp: Date
    }

    func recordOperation(_ operation: String, category: String, executionTime: Double) {
        operationsLock.lock()
        defer { operationsLock.unlock() }

        let record = PerformanceRecord(
            operation: operation,
            category: category,
            executionTime: executionTime,
            timestamp: Date()
        )

        operations.append(record)

        // 保持数组大小在限制内
        if operations.count > maxRecords {
            operations.removeFirst(operations.count - maxRecords)
        }
    }

    func getPerformanceStats(days: Int) -> [LoggerPerformanceStats] {
        operationsLock.lock()
        defer { operationsLock.unlock() }

        let cutoffDate = Date().addingTimeInterval(-TimeInterval(days * 24 * 3600))
        let recentOperations = operations.filter { $0.timestamp >= cutoffDate }

        let groupedOperations = Dictionary(grouping: recentOperations) { "\($0.operation)_\($0.category)" }

        return groupedOperations.map { _, records in
            let times = records.map { $0.executionTime }
            let slowCount = records.filter { $0.executionTime > 100 }.count

            return LoggerPerformanceStats(
                operation: records.first?.operation ?? "",
                category: records.first?.category ?? "",
                averageTime: times.reduce(0, +) / Double(times.count),
                maxTime: times.max() ?? 0,
                minTime: times.min() ?? 0,
                callCount: records.count,
                slowOperations: slowCount
            )
        }
    }
}
