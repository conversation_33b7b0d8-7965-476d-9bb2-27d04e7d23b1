//
//  SystemMonitorDatabase.swift
//  BDoggy
//
//  Created by System Monitor on 2024/12/11.
//

import Foundation
import SQLite3

/// 系统监控数据库管理器
class SystemMonitorDatabase {
    static let shared = SystemMonitorDatabase()
    
    private var db: OpaquePointer?
    private let dbPath: String
    
    private init() {
        // 数据库存储路径：~/Library/Application Support/BDoggy/
        let appSupportURL = FileManager.default.urls(for: .applicationSupportDirectory, 
                                                    in: .userDomainMask).first!
        let bdoggyURL = appSupportURL.appendingPathComponent("BDoggy")
        
        // 确保目录存在
        try? FileManager.default.createDirectory(at: bdoggyURL, 
                                                withIntermediateDirectories: true, 
                                                attributes: nil)
        
        self.dbPath = bdoggyURL.appendingPathComponent("system_monitor.db").path
        
        openDatabase()
        createTables()
    }
    
    deinit {
        closeDatabase()
    }
    
    // MARK: - 数据库连接管理
    private func openDatabase() {
        if sqlite3_open(dbPath, &db) != SQLITE_OK {
            Logger.error("无法打开数据库: \(String(cString: sqlite3_errmsg(db)))")
            db = nil
        } else {
            Logger.info("数据库已打开: \(dbPath)")
        }
    }
    
    private func closeDatabase() {
        if sqlite3_close(db) != SQLITE_OK {
            Logger.error("无法关闭数据库: \(String(cString: sqlite3_errmsg(db)))")
        }
        db = nil
    }
    
    // MARK: - 创建数据表
    private func createTables() {
        createSystemInfoTable()
        createMetricsLogTable()
        createAppUsageTable()
        createConfigTable()
    }
    
    /// 创建系统信息表（静态信息）
    private func createSystemInfoTable() {
        let createTableSQL = """
            CREATE TABLE IF NOT EXISTS system_info (
                id TEXT PRIMARY KEY,
                device_model TEXT NOT NULL,
                cpu_name TEXT NOT NULL,
                total_memory_gb REAL NOT NULL,
                total_disk_gb REAL NOT NULL,
                macos_version TEXT NOT NULL,
                recorded_at DATETIME NOT NULL
            );
        """
        
        executeSQL(createTableSQL, description: "创建系统信息表")
    }
    
    /// 创建指标日志表（动态数据）
    private func createMetricsLogTable() {
        let createTableSQL = """
            CREATE TABLE IF NOT EXISTS metrics_log (
                id TEXT PRIMARY KEY,
                timestamp DATETIME NOT NULL,
                cpu_usage_percent REAL NOT NULL,
                top_processes TEXT,
                memory_usage_percent REAL NOT NULL,
                memory_used_gb REAL NOT NULL,
                memory_available_gb REAL NOT NULL,
                swap_used_gb REAL NOT NULL,
                swap_total_gb REAL NOT NULL,
                disk_free_gb REAL NOT NULL,
                disk_usage_percent REAL NOT NULL,
                battery_level INTEGER,
                battery_health REAL,
                battery_cycle_count INTEGER,
                power_source_type TEXT NOT NULL,
                cpu_temperature REAL,
                gpu_temperature REAL,
                fan_speed INTEGER,
                active_app_bundle_id TEXT,
                active_app_name TEXT
            );
        """
        
        executeSQL(createTableSQL, description: "创建指标日志表")
        
        // 创建时间索引以提高查询性能
        let createIndexSQL = "CREATE INDEX IF NOT EXISTS idx_metrics_timestamp ON metrics_log(timestamp);"
        executeSQL(createIndexSQL, description: "创建时间索引")
    }
    
    /// 创建应用使用记录表
    private func createAppUsageTable() {
        let createTableSQL = """
            CREATE TABLE IF NOT EXISTS app_usage (
                id TEXT PRIMARY KEY,
                bundle_id TEXT NOT NULL,
                app_name TEXT NOT NULL,
                start_time DATETIME NOT NULL,
                end_time DATETIME,
                duration REAL NOT NULL DEFAULT 0
            );
        """
        
        executeSQL(createTableSQL, description: "创建应用使用表")
        
        // 创建索引
        let createIndexSQL = "CREATE INDEX IF NOT EXISTS idx_app_usage_start_time ON app_usage(start_time);"
        executeSQL(createIndexSQL, description: "创建应用使用时间索引")
    }
    
    /// 创建配置表
    private func createConfigTable() {
        let createTableSQL = """
            CREATE TABLE IF NOT EXISTS monitor_config (
                key TEXT PRIMARY KEY,
                value TEXT NOT NULL,
                updated_at DATETIME NOT NULL
            );
        """
        
        executeSQL(createTableSQL, description: "创建配置表")
    }
    
    private func executeSQL(_ sql: String, description: String) {
        guard let db = db else {
            Logger.error("数据库未初始化")
            return
        }
        
        if sqlite3_exec(db, sql, nil, nil, nil) != SQLITE_OK {
            Logger.error("\(description)失败: \(String(cString: sqlite3_errmsg(db)))")
        } else {
            Logger.debug("\(description)成功")
        }
    }
    
    // MARK: - 数据插入
    
    /// 插入系统信息
    func insertSystemInfo(_ systemInfo: SystemInfo) -> Bool {
        let insertSQL = """
            INSERT OR REPLACE INTO system_info 
            (id, device_model, cpu_name, total_memory_gb, total_disk_gb, macos_version, recorded_at)
            VALUES (?, ?, ?, ?, ?, ?, ?);
        """
        
        return executeInsert(sql: insertSQL) { statement in
            sqlite3_bind_text(statement, 1, systemInfo.id, -1, nil)
            sqlite3_bind_text(statement, 2, systemInfo.deviceModel, -1, nil)
            sqlite3_bind_text(statement, 3, systemInfo.cpuName, -1, nil)
            sqlite3_bind_double(statement, 4, systemInfo.totalMemoryGB)
            sqlite3_bind_double(statement, 5, systemInfo.totalDiskGB)
            sqlite3_bind_text(statement, 6, systemInfo.macOSVersion, -1, nil)
            sqlite3_bind_text(statement, 7, ISO8601DateFormatter().string(from: systemInfo.recordedAt), -1, nil)
        }
    }
    
    /// 插入系统指标
    func insertSystemMetrics(_ metrics: SystemMetrics) -> Bool {
        let insertSQL = """
            INSERT INTO metrics_log 
            (id, timestamp, cpu_usage_percent, top_processes, memory_usage_percent, 
             memory_used_gb, memory_available_gb, swap_used_gb, swap_total_gb,
             disk_free_gb, disk_usage_percent, battery_level, battery_health, 
             battery_cycle_count, power_source_type, cpu_temperature, gpu_temperature, 
             fan_speed, active_app_bundle_id, active_app_name)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);
        """
        
        return executeInsert(sql: insertSQL) { statement in
            sqlite3_bind_text(statement, 1, metrics.id, -1, nil)
            sqlite3_bind_text(statement, 2, ISO8601DateFormatter().string(from: metrics.timestamp), -1, nil)
            sqlite3_bind_double(statement, 3, metrics.cpuUsagePercent)
            
            // 序列化 top processes
            let processesData = try? JSONEncoder().encode(metrics.topProcesses)
            let processesString = processesData.flatMap { String(data: $0, encoding: .utf8) } ?? "[]"
            sqlite3_bind_text(statement, 4, processesString, -1, nil)
            
            sqlite3_bind_double(statement, 5, metrics.memoryUsagePercent)
            sqlite3_bind_double(statement, 6, metrics.memoryUsedGB)
            sqlite3_bind_double(statement, 7, metrics.memoryAvailableGB)
            sqlite3_bind_double(statement, 8, metrics.swapUsedGB)
            sqlite3_bind_double(statement, 9, metrics.swapTotalGB)
            sqlite3_bind_double(statement, 10, metrics.diskFreeGB)
            sqlite3_bind_double(statement, 11, metrics.diskUsagePercent)
            
            if let batteryLevel = metrics.batteryLevel {
                sqlite3_bind_int(statement, 12, Int32(batteryLevel))
            } else {
                sqlite3_bind_null(statement, 12)
            }
            
            if let batteryHealth = metrics.batteryHealth {
                sqlite3_bind_double(statement, 13, batteryHealth)
            } else {
                sqlite3_bind_null(statement, 13)
            }
            
            if let batteryCycleCount = metrics.batteryCycleCount {
                sqlite3_bind_int(statement, 14, Int32(batteryCycleCount))
            } else {
                sqlite3_bind_null(statement, 14)
            }
            
            sqlite3_bind_text(statement, 15, metrics.powerSourceType, -1, nil)
            
            if let cpuTemp = metrics.cpuTemperature {
                sqlite3_bind_double(statement, 16, cpuTemp)
            } else {
                sqlite3_bind_null(statement, 16)
            }
            
            if let gpuTemp = metrics.gpuTemperature {
                sqlite3_bind_double(statement, 17, gpuTemp)
            } else {
                sqlite3_bind_null(statement, 17)
            }
            
            if let fanSpeed = metrics.fanSpeed {
                sqlite3_bind_int(statement, 18, Int32(fanSpeed))
            } else {
                sqlite3_bind_null(statement, 18)
            }
            
            sqlite3_bind_text(statement, 19, metrics.activeAppBundleID, -1, nil)
            sqlite3_bind_text(statement, 20, metrics.activeAppName, -1, nil)
        }
    }
    
    private func executeInsert(sql: String, bindParameters: (OpaquePointer) -> Void) -> Bool {
        guard let db = db else {
            Logger.error("数据库未初始化")
            return false
        }
        
        var statement: OpaquePointer?
        
        guard sqlite3_prepare_v2(db, sql, -1, &statement, nil) == SQLITE_OK else {
            Logger.error("准备SQL语句失败: \(String(cString: sqlite3_errmsg(db)))")
            return false
        }
        
        defer {
            sqlite3_finalize(statement)
        }
        
        bindParameters(statement!)
        
        if sqlite3_step(statement) == SQLITE_DONE {
            return true
        } else {
            Logger.error("执行插入失败: \(String(cString: sqlite3_errmsg(db)))")
            return false
        }
    }
    
    // MARK: - 数据清理
    
    /// 清理过期数据
    func cleanupOldData(olderThanDays days: Int) -> Bool {
        let cutoffDate = Calendar.current.date(byAdding: .day, value: -days, to: Date()) ?? Date()
        let dateString = ISO8601DateFormatter().string(from: cutoffDate)
        
        let deleteSQL = "DELETE FROM metrics_log WHERE timestamp < ?;"
        
        return executeInsert(sql: deleteSQL) { statement in
            sqlite3_bind_text(statement, 1, dateString, -1, nil)
        }
    }
    
    /// 清除所有数据
    func clearAllData() -> Bool {
        let tables = ["system_info", "metrics_log", "app_usage", "monitor_config"]
        
        for table in tables {
            let deleteSQL = "DELETE FROM \(table);"
            if !executeInsert(sql: deleteSQL) { _ in } {
                return false
            }
        }
        
        return true
    }
    
    /// 获取数据库文件大小
    func getDatabaseSize() -> Int64 {
        do {
            let attributes = try FileManager.default.attributesOfItem(atPath: dbPath)
            return attributes[.size] as? Int64 ?? 0
        } catch {
            Logger.error("获取数据库大小失败: \(error)")
            return 0
        }
    }

    // MARK: - 数据查询

    /// 获取最近的系统指标数据
    func getRecentMetrics(limit: Int = 100) -> [SystemMetrics] {
        let querySQL = """
            SELECT * FROM metrics_log
            ORDER BY timestamp DESC
            LIMIT ?;
        """

        return executeQuery(sql: querySQL, bindParameters: { statement in
            sqlite3_bind_int(statement, 1, Int32(limit))
        }, parseRow: parseMetricsRow)
    }

    /// 获取指定时间范围的系统指标
    func getMetrics(from startDate: Date, to endDate: Date) -> [SystemMetrics] {
        let querySQL = """
            SELECT * FROM metrics_log
            WHERE timestamp BETWEEN ? AND ?
            ORDER BY timestamp ASC;
        """

        let formatter = ISO8601DateFormatter()

        return executeQuery(sql: querySQL, bindParameters: { statement in
            sqlite3_bind_text(statement, 1, formatter.string(from: startDate), -1, nil)
            sqlite3_bind_text(statement, 2, formatter.string(from: endDate), -1, nil)
        }, parseRow: parseMetricsRow)
    }

    /// 获取系统信息
    func getSystemInfo() -> SystemInfo? {
        let querySQL = "SELECT * FROM system_info ORDER BY recorded_at DESC LIMIT 1;"

        let results: [SystemInfo] = executeQuery(sql: querySQL, bindParameters: { _ in }, parseRow: parseSystemInfoRow)
        return results.first
    }

    private func executeQuery<T>(sql: String,
                                bindParameters: (OpaquePointer) -> Void,
                                parseRow: (OpaquePointer) -> T?) -> [T] {
        guard let db = db else {
            Logger.error("数据库未初始化")
            return []
        }

        var statement: OpaquePointer?
        var results: [T] = []

        guard sqlite3_prepare_v2(db, sql, -1, &statement, nil) == SQLITE_OK else {
            Logger.error("准备查询语句失败: \(String(cString: sqlite3_errmsg(db)))")
            return []
        }

        defer {
            sqlite3_finalize(statement)
        }

        bindParameters(statement!)

        while sqlite3_step(statement) == SQLITE_ROW {
            if let row = parseRow(statement!) {
                results.append(row)
            }
        }

        return results
    }

    private func parseMetricsRow(_ statement: OpaquePointer) -> SystemMetrics? {
        // 这里需要从数据库行解析 SystemMetrics
        // 由于 SystemMetrics 的初始化器会重新采集数据，我们需要创建一个新的构造器
        // 暂时返回 nil，在实际实现中需要添加从数据库数据构造 SystemMetrics 的方法
        return nil
    }

    private func parseSystemInfoRow(_ statement: OpaquePointer) -> SystemInfo? {
        // 类似地，需要从数据库行解析 SystemInfo
        return nil
    }

    // MARK: - 统计查询

    /// 获取CPU使用率趋势数据（用于图表）
    func getCPUUsageTrend(hours: Int = 24) -> [(Date, Double)] {
        let startDate = Calendar.current.date(byAdding: .hour, value: -hours, to: Date()) ?? Date()
        let formatter = ISO8601DateFormatter()

        let querySQL = """
            SELECT timestamp, cpu_usage_percent
            FROM metrics_log
            WHERE timestamp >= ?
            ORDER BY timestamp ASC;
        """

        guard let db = db else { return [] }

        var statement: OpaquePointer?
        var results: [(Date, Double)] = []

        guard sqlite3_prepare_v2(db, querySQL, -1, &statement, nil) == SQLITE_OK else {
            return []
        }

        defer { sqlite3_finalize(statement) }

        sqlite3_bind_text(statement, 1, formatter.string(from: startDate), -1, nil)

        while sqlite3_step(statement) == SQLITE_ROW {
            if let timestampString = sqlite3_column_text(statement, 0),
               let timestamp = formatter.date(from: String(cString: timestampString)) {
                let cpuUsage = sqlite3_column_double(statement, 1)
                results.append((timestamp, cpuUsage))
            }
        }

        return results
    }

    /// 获取内存使用率趋势数据
    func getMemoryUsageTrend(hours: Int = 24) -> [(Date, Double)] {
        let startDate = Calendar.current.date(byAdding: .hour, value: -hours, to: Date()) ?? Date()
        let formatter = ISO8601DateFormatter()

        let querySQL = """
            SELECT timestamp, memory_usage_percent
            FROM metrics_log
            WHERE timestamp >= ?
            ORDER BY timestamp ASC;
        """

        guard let db = db else { return [] }

        var statement: OpaquePointer?
        var results: [(Date, Double)] = []

        guard sqlite3_prepare_v2(db, querySQL, -1, &statement, nil) == SQLITE_OK else {
            return []
        }

        defer { sqlite3_finalize(statement) }

        sqlite3_bind_text(statement, 1, formatter.string(from: startDate), -1, nil)

        while sqlite3_step(statement) == SQLITE_ROW {
            if let timestampString = sqlite3_column_text(statement, 0),
               let timestamp = formatter.date(from: String(cString: timestampString)) {
                let memoryUsage = sqlite3_column_double(statement, 1)
                results.append((timestamp, memoryUsage))
            }
        }

        return results
    }
}
