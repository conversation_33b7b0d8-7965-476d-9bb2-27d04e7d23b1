//
//  PermissionManager.swift
//  BDoggy
//
//  Created by K4 on 2025/1/11.
//

import AppKit
import Cocoa
import Foundation

/// 权限管理器
/// 负责检查和请求各种系统权限
@Observable
class PermissionManager {
    static let shared = PermissionManager()
    
    // MARK: - 权限状态
    
    var hasAccessibilityPermission = false
    var hasFullDiskAccess = false
    var hasScreenRecordingPermission = false
    
    // MARK: - 初始化
    
    private init() {
        checkAllPermissions()
        setupPermissionMonitoring()
    }
    
    // MARK: - 权限检查
    
    /// 检查所有权限
    func checkAllPermissions() {
        checkAccessibilityPermission()
        checkFullDiskAccess()
        checkScreenRecordingPermission()
    }
    
    /// 检查辅助功能权限
    private func checkAccessibilityPermission() {
        hasAccessibilityPermission = AXIsProcessTrusted()
    }
    
    /// 检查完全磁盘访问权限
    private func checkFullDiskAccess() {
        // 尝试访问一个需要完全磁盘访问权限的路径
        let testPath = "/Library/Application Support"
        hasFullDiskAccess = FileManager.default.isReadableFile(atPath: testPath)
    }
    
    /// 检查屏幕录制权限
    private func checkScreenRecordingPermission() {
        // 在macOS 10.15+中，可以通过尝试获取屏幕内容来检查权限
        if #available(macOS 10.15, *) {
            let displayID = CGMainDisplayID()
            if let image = CGDisplayCreateImage(displayID) {
                hasScreenRecordingPermission = true
                // 立即释放图像
                _ = image
            } else {
                hasScreenRecordingPermission = false
            }
        } else {
            hasScreenRecordingPermission = true // 旧版本不需要此权限
        }
    }
    
    // MARK: - 权限请求
    
    /// 请求辅助功能权限
    func requestAccessibilityPermission() {
        let options = [kAXTrustedCheckOptionPrompt.takeUnretainedValue(): true]
        AXIsProcessTrustedWithOptions(options as CFDictionary)
        
        // 延迟检查权限状态
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.checkAccessibilityPermission()
        }
    }
    
    /// 打开系统偏好设置到隐私设置
    func openPrivacySettings() {
        if let url = URL(string: "x-apple.systempreferences:com.apple.preference.security?Privacy") {
            NSWorkspace.shared.open(url)
        }
    }
    
    /// 打开系统偏好设置到辅助功能设置
    func openAccessibilitySettings() {
        if let url = URL(string: "x-apple.systempreferences:com.apple.preference.security?Privacy_Accessibility") {
            NSWorkspace.shared.open(url)
        }
    }
    
    /// 打开系统偏好设置到完全磁盘访问设置
    func openFullDiskAccessSettings() {
        if let url = URL(string: "x-apple.systempreferences:com.apple.preference.security?Privacy_AllFiles") {
            NSWorkspace.shared.open(url)
        }
    }
    
    /// 打开系统偏好设置到屏幕录制设置
    func openScreenRecordingSettings() {
        if let url = URL(string: "x-apple.systempreferences:com.apple.preference.security?Privacy_ScreenCapture") {
            NSWorkspace.shared.open(url)
        }
    }
    
    // MARK: - 权限监控
    
    private func setupPermissionMonitoring() {
        // 每5秒检查一次权限状态
        Timer.scheduledTimer(withTimeInterval: 5.0, repeats: true) { _ in
            self.checkAllPermissions()
        }
    }
    
    // MARK: - 权限状态查询
    
    /// 获取所有权限状态
    func getAllPermissionStatus() -> [String: Bool] {
        return [
            "accessibility": hasAccessibilityPermission,
            "full_disk_access": hasFullDiskAccess,
            "screen_recording": hasScreenRecordingPermission
        ]
    }
    
    /// 检查是否有足够的权限进行系统监控
    func hasRequiredPermissionsForMonitoring() -> Bool {
        return hasAccessibilityPermission
    }
    
    /// 获取缺失的权限列表
    func getMissingPermissions() -> [PermissionType] {
        var missing: [PermissionType] = []
        
        if !hasAccessibilityPermission {
            missing.append(.accessibility)
        }
        
        if !hasFullDiskAccess {
            missing.append(.fullDiskAccess)
        }
        
        if !hasScreenRecordingPermission {
            missing.append(.screenRecording)
        }
        
        return missing
    }
    
    // MARK: - 权限说明
    
    /// 获取权限说明文本
    func getPermissionDescription(_ type: PermissionType) -> String {
        switch type {
        case .accessibility:
            return "辅助功能权限用于获取当前活跃应用信息，这对于应用使用时间统计是必需的。"
        case .fullDiskAccess:
            return "完全磁盘访问权限用于读取系统文件和获取详细的系统信息。"
        case .screenRecording:
            return "屏幕录制权限用于获取屏幕相关的系统信息。"
        }
    }
    
    /// 获取权限请求指导
    func getPermissionInstructions(_ type: PermissionType) -> [String] {
        switch type {
        case .accessibility:
            return [
                "1. 点击下方按钮打开系统偏好设置",
                "2. 在「隐私与安全性」中选择「辅助功能」",
                "3. 点击锁图标并输入密码",
                "4. 勾选 BDoggy 应用",
                "5. 重启应用以使权限生效"
            ]
        case .fullDiskAccess:
            return [
                "1. 点击下方按钮打开系统偏好设置",
                "2. 在「隐私与安全性」中选择「完全磁盘访问权限」",
                "3. 点击锁图标并输入密码",
                "4. 点击 + 号添加 BDoggy 应用",
                "5. 重启应用以使权限生效"
            ]
        case .screenRecording:
            return [
                "1. 点击下方按钮打开系统偏好设置",
                "2. 在「隐私与安全性」中选择「屏幕录制」",
                "3. 点击锁图标并输入密码",
                "4. 勾选 BDoggy 应用",
                "5. 重启应用以使权限生效"
            ]
        }
    }
}

// MARK: - 权限类型枚举

enum PermissionType: String, CaseIterable {
    case accessibility
    case fullDiskAccess = "full_disk_access"
    case screenRecording = "screen_recording"
    
    var displayName: String {
        switch self {
        case .accessibility:
            return "辅助功能"
        case .fullDiskAccess:
            return "完全磁盘访问"
        case .screenRecording:
            return "屏幕录制"
        }
    }
    
    var isRequired: Bool {
        switch self {
        case .accessibility:
            return true // 必需，用于应用使用跟踪
        case .fullDiskAccess:
            return false // 可选，用于更详细的系统信息
        case .screenRecording:
            return false // 可选，用于屏幕相关功能
        }
    }
}

// MARK: - 权限状态视图模型

@Observable
class PermissionStatusViewModel {
    let permissionManager = PermissionManager.shared
    
    var showingPermissionAlert = false
    var currentPermissionType: PermissionType?
    
    func requestPermission(_ type: PermissionType) {
        currentPermissionType = type
        
        switch type {
        case .accessibility:
            permissionManager.requestAccessibilityPermission()
        case .fullDiskAccess, .screenRecording:
            showingPermissionAlert = true
        }
    }
    
    func openSettingsForCurrentPermission() {
        guard let type = currentPermissionType else { return }
        
        switch type {
        case .accessibility:
            permissionManager.openAccessibilitySettings()
        case .fullDiskAccess:
            permissionManager.openFullDiskAccessSettings()
        case .screenRecording:
            permissionManager.openScreenRecordingSettings()
        }
        
        showingPermissionAlert = false
    }
    
    func getRequiredPermissions() -> [PermissionType] {
        return PermissionType.allCases.filter { $0.isRequired }
    }
    
    func getOptionalPermissions() -> [PermissionType] {
        return PermissionType.allCases.filter { !$0.isRequired }
    }
    
    func hasAllRequiredPermissions() -> Bool {
        return getRequiredPermissions().allSatisfy { type in
            switch type {
            case .accessibility:
                return permissionManager.hasAccessibilityPermission
            case .fullDiskAccess:
                return permissionManager.hasFullDiskAccess
            case .screenRecording:
                return permissionManager.hasScreenRecordingPermission
            }
        }
    }
}
