//
//  FrameIntervalCalculator.swift
//  BDoggy
//
//  Created by K4 on 2024/12/10.
//

import Foundation

class FrameIntervalCalculator {
    private let totalMaxInterval: Int
    private let frameMinInterval: Int
    
    init(totalMaxInterval: Int, frameMinInterval: Int) {
        self.totalMaxInterval = totalMaxInterval
        self.frameMinInterval = frameMinInterval
    }
    
    /// 根据系统资源使用率计算帧间隔
    /// - Parameters:
    ///   - totalUsage: 系统资源使用率百分比 (0-100)
    ///   - frameCount: 动画帧数量
    /// - Returns: 计算后的帧间隔时间
    func calculateInterval(totalUsage: Double, frameCount: Int) -> Int {
        let frameInterval = Int(Double(totalMaxInterval) * (1 - totalUsage / 100.0)) / frameCount
        return max(frameInterval, frameMinInterval)
    }
}
