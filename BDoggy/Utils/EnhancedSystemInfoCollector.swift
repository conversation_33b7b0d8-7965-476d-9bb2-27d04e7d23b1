//
//  EnhancedSystemInfoCollector.swift
//  BDoggy
//
//  Created by K4 on 2025/1/11.
//

import Foundation
import IOKit
import IOKit.ps
import IOKit.pwr_mgt
import AppKit
import Cocoa

/// 增强版系统信息采集器
/// 使用IOKit、sysctl、NSWorkspace等原生API采集系统信息
class EnhancedSystemInfoCollector {
    
    // MARK: - CPU信息采集
    
    /// 获取CPU使用率
    static func getCPUUsage() -> Double {
        var cpuInfo: processor_info_array_t!
        var numCpuInfo: mach_msg_type_number_t = 0
        var numCpus: natural_t = 0
        
        let result = host_processor_info(mach_host_self(), PROCESSOR_CPU_LOAD_INFO, &numCpus, &cpuInfo, &numCpuInfo)
        
        guard result == KERN_SUCCESS else {
            Logger.error("获取CPU信息失败")
            return 0.0
        }
        
        defer {
            let size = vm_size_t(numCpuInfo) * vm_size_t(MemoryLayout<integer_t>.size)
            vm_deallocate(mach_task_self_, vm_address_t(bitPattern: cpuInfo), size)
        }
        
        var totalUser: UInt32 = 0
        var totalSystem: UInt32 = 0
        var totalIdle: UInt32 = 0
        var totalNice: UInt32 = 0
        
        for i in 0..<Int(numCpus) {
            let cpuLoadInfo = cpuInfo.advanced(by: i * Int(CPU_STATE_MAX)).withMemoryRebound(to: UInt32.self, capacity: Int(CPU_STATE_MAX)) { $0 }
            
            totalUser += cpuLoadInfo[Int(CPU_STATE_USER)]
            totalSystem += cpuLoadInfo[Int(CPU_STATE_SYSTEM)]
            totalIdle += cpuLoadInfo[Int(CPU_STATE_IDLE)]
            totalNice += cpuLoadInfo[Int(CPU_STATE_NICE)]
        }
        
        let totalTicks = totalUser + totalSystem + totalIdle + totalNice
        let usedTicks = totalUser + totalSystem + totalNice
        
        return totalTicks > 0 ? Double(usedTicks) / Double(totalTicks) * 100.0 : 0.0
    }
    
    /// 获取CPU核心数
    static func getCPUCoreCount() -> Int {
        var size = MemoryLayout<Int>.size
        var coreCount: Int = 0
        
        if sysctlbyname("hw.ncpu", &coreCount, &size, nil, 0) == 0 {
            return coreCount
        }
        
        return 1
    }
    
    /// 获取CPU温度
    static func getCPUTemperature() -> Double? {
        // 使用IOKit获取CPU温度
        let service = IOServiceGetMatchingService(kIOMainPortDefault, IOServiceMatching("IOPMrootDomain"))
        guard service != 0 else { return nil }
        
        defer { IOObjectRelease(service) }
        
        // 尝试获取温度传感器数据
        // 注意：这个方法在不同的Mac型号上可能有所不同
        return getThermalState()
    }
    
    private static func getThermalState() -> Double? {
        var size = MemoryLayout<UInt32>.size
        var thermalState: UInt32 = 0
        
        if sysctlbyname("machdep.xcpm.cpu_thermal_level", &thermalState, &size, nil, 0) == 0 {
            // 将热状态转换为大概的温度值
            return Double(thermalState) * 10.0 + 40.0 // 粗略估算
        }
        
        return nil
    }
    
    // MARK: - 内存信息采集
    
    /// 获取内存信息
    static func getMemoryInfo() -> MemoryInfo {
        var vmStat = vm_statistics64()
        var count = mach_msg_type_number_t(MemoryLayout<vm_statistics64>.size / MemoryLayout<integer_t>.size)
        
        let result = withUnsafeMutablePointer(to: &vmStat) {
            $0.withMemoryRebound(to: integer_t.self, capacity: Int(count)) {
                host_statistics64(mach_host_self(), HOST_VM_INFO64, $0, &count)
            }
        }
        
        guard result == KERN_SUCCESS else {
            Logger.error("获取内存统计失败")
            return MemoryInfo(usagePercent: 0, usedGB: 0, availableGB: 0, totalGB: 0, swapUsedGB: 0, swapTotalGB: 0)
        }
        
        let pageSize = vm_kernel_page_size
        
        let totalPages = vmStat.free_count + vmStat.active_count + vmStat.inactive_count + vmStat.wire_count + vmStat.compressor_page_count
        let usedPages = vmStat.active_count + vmStat.inactive_count + vmStat.wire_count + vmStat.compressor_page_count
        let freePages = vmStat.free_count
        
        let totalGB = Double(totalPages) * Double(pageSize) / (1024 * 1024 * 1024)
        let usedGB = Double(usedPages) * Double(pageSize) / (1024 * 1024 * 1024)
        let availableGB = Double(freePages) * Double(pageSize) / (1024 * 1024 * 1024)
        
        let usagePercent = totalGB > 0 ? (usedGB / totalGB) * 100.0 : 0.0
        
        // 获取Swap信息
        let swapInfo = getSwapInfo()
        
        return MemoryInfo(
            usagePercent: usagePercent,
            usedGB: usedGB,
            availableGB: availableGB,
            totalGB: totalGB,
            swapUsedGB: swapInfo.used,
            swapTotalGB: swapInfo.total
        )
    }
    
    private static func getSwapInfo() -> (used: Double, total: Double) {
        var swapUsage = xsw_usage()
        var size = MemoryLayout<xsw_usage>.size
        
        if sysctlbyname("vm.swapusage", &swapUsage, &size, nil, 0) == 0 {
            let usedGB = Double(swapUsage.xsu_used) / (1024 * 1024 * 1024)
            let totalGB = Double(swapUsage.xsu_total) / (1024 * 1024 * 1024)
            return (usedGB, totalGB)
        }
        
        return (0, 0)
    }
    
    // MARK: - 磁盘信息采集
    
    /// 获取磁盘信息
    static func getDiskInfo() -> DiskInfo {
        let fileManager = FileManager.default
        
        do {
            let homeURL = fileManager.homeDirectoryForCurrentUser
            let resourceValues = try homeURL.resourceValues(forKeys: [
                .volumeTotalCapacityKey,
                .volumeAvailableCapacityKey
            ])
            
            let totalBytes = resourceValues.volumeTotalCapacity ?? 0
            let availableBytes = resourceValues.volumeAvailableCapacity ?? 0
            let usedBytes = totalBytes - availableBytes
            
            let totalGB = Double(totalBytes) / (1024 * 1024 * 1024)
            let freeGB = Double(availableBytes) / (1024 * 1024 * 1024)
            let usagePercent = totalGB > 0 ? (Double(usedBytes) / Double(totalBytes)) * 100.0 : 0.0
            
            return DiskInfo(freeGB: freeGB, usagePercent: usagePercent, totalGB: totalGB)
        } catch {
            Logger.error("获取磁盘信息失败: \(error)")
            return DiskInfo(freeGB: 0, usagePercent: 0, totalGB: 0)
        }
    }
    
    // MARK: - 电池信息采集
    
    /// 获取电池信息
    static func getBatteryInfo() -> BatteryInfo {
        let powerSources = IOPSCopyPowerSourcesInfo()?.takeRetainedValue()
        let powerSourcesList = IOPSCopyPowerSourcesList(powerSources)?.takeRetainedValue() as? [CFDictionary]
        
        var batteryLevel: Int?
        var batteryHealth: Double?
        var cycleCount: Int?
        var powerSourceType = "AC Power"
        
        if let powerSourcesList = powerSourcesList {
            for powerSource in powerSourcesList {
                let psDict = powerSource as NSDictionary
                
                if let type = psDict[kIOPSTypeKey] as? String, type == kIOPSInternalBatteryType {
                    // 电池电量
                    if let currentCapacity = psDict[kIOPSCurrentCapacityKey] as? Int,
                       let maxCapacity = psDict[kIOPSMaxCapacityKey] as? Int, maxCapacity > 0 {
                        batteryLevel = (currentCapacity * 100) / maxCapacity
                    }
                    
                    // 电池健康度
                    if let designCapacity = psDict["DesignCapacity"] as? Int,
                       let maxCapacity = psDict[kIOPSMaxCapacityKey] as? Int, designCapacity > 0 {
                        batteryHealth = (Double(maxCapacity) / Double(designCapacity)) * 100.0
                    }
                    
                    // 循环次数
                    cycleCount = psDict["CycleCount"] as? Int
                    
                    // 电源状态
                    if let powerSourceState = psDict[kIOPSPowerSourceStateKey] as? String {
                        powerSourceType = powerSourceState == kIOPSACPowerValue ? "AC Power" : "Battery"
                    }
                    
                    break
                }
            }
        }
        
        return BatteryInfo(
            level: batteryLevel,
            health: batteryHealth,
            cycleCount: cycleCount,
            powerSourceType: powerSourceType
        )
    }
    
    // MARK: - 温度和风扇信息
    
    /// 获取GPU温度
    static func getGPUTemperature() -> Double? {
        // GPU温度获取比较复杂，需要访问特定的IOKit服务
        // 这里提供一个基础实现，实际可能需要根据具体硬件调整
        return nil
    }
    
    /// 获取风扇转速
    static func getFanSpeed() -> Int? {
        // 风扇转速获取需要访问SMC（System Management Controller）
        // 这里提供一个基础实现
        return nil
    }
    
    // MARK: - 当前活跃应用
    
    /// 获取当前活跃应用
    static func getActiveApplication() -> ActiveAppInfo {
        let workspace = NSWorkspace.shared
        
        if let frontmostApp = workspace.frontmostApplication {
            return ActiveAppInfo(
                bundleID: frontmostApp.bundleIdentifier,
                name: frontmostApp.localizedName
            )
        }
        
        return ActiveAppInfo(bundleID: nil, name: nil)
    }
    
    // MARK: - 系统负载
    
    /// 获取系统负载平均值
    static func getLoadAverage() -> LoadAverageInfo {
        var loadAvg: [Double] = [0, 0, 0]
        
        if getloadavg(&loadAvg, 3) != -1 {
            return LoadAverageInfo(
                oneMin: loadAvg[0],
                fiveMin: loadAvg[1],
                fifteenMin: loadAvg[2]
            )
        }
        
        return LoadAverageInfo(oneMin: 0, fiveMin: 0, fifteenMin: 0)
    }
    
    // MARK: - 网络信息
    
    /// 获取网络信息
    static func getNetworkInfo() -> NetworkInfo {
        // 这里需要实现网络流量统计
        // 可以通过读取系统网络统计信息来实现
        return NetworkInfo(bytesIn: 0, bytesOut: 0)
    }
    
    // MARK: - 进程信息
    
    /// 获取Top进程信息
    static func getTopProcesses(limit: Int = 10) -> [ProcessInfoModel] {
        // 这里需要实现进程信息获取
        // 可以通过sysctl或者task_info来获取进程信息
        return []
    }
}
