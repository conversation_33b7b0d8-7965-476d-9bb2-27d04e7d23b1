import CryptoKit
import Foundation
import UniformTypeIdentifiers

class TencentCOSDownloader {
    static let shared = TencentCOSDownloader() // 单例模式
    private let session: URLSession
    private let fileManager = FileManager.default
    private static let expiration = 3600

    private init() {
        // 配置 URLSession
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 30 // 设置请求超时时间（秒）
        config.timeoutIntervalForResource = 60 // 资源加载超时
        config.httpAdditionalHeaders = [
            "Content-Type": "application/json",
            "User-Agent": "\(Constants.APP_NAME)/1.0"
        ]
        config.requestCachePolicy = .reloadIgnoringLocalCacheData // 不使用缓存

        self.session = URLSession(configuration: config)
    }

    private let signer = COSSigner()
    private static let securityToken = "x-cos-security-token"
    private static let Authorization = "Authorization"

    func fetchTemporaryCredentials(urls: [String], targetFolder: URL, completion: @escaping (Result<[URL], NetworkError>) -> Void) {
        Task {
            do {
                let cred: COSSTSResponse = try await NetworkManager.shared.request(url: "\(Constants.apiBaseURL)/api/cos/sts/get")
                let desURL = try await downloadFilesForCOS(from: urls, cred: cred, targetFolder: targetFolder)
                completion(.success(desURL))
            } catch {
                // 检查目录是否存在
                if fileManager.fileExists(atPath: targetFolder.path) {
                    // 删除目录及其子文件
                    try fileManager.removeItem(at: targetFolder)
                    Logger.debug("下载失败，删除目录：\(targetFolder.path)")
                } else {
                    Logger.debug("目录不存在，无法删除：\(targetFolder.path)")
                }
                completion(.failure(NetworkError.fileDownloadFailed(error)))
            }
        }
    }

    func fetchPutCredentials(from files: [String],
                             folderName: String,
                             groupName: String,
                             completion: @escaping (Result<[String], NetworkError>) -> Void)
    {
        Task {
            do {
                let cred: COSSTSResponse = try await NetworkManager.shared.request(url: "\(Constants.apiBaseURL)/api/cos/sts/put")
                let desURL = try await uploadFiles(fileURLs: files, cred: cred, folderName: folderName, groupName: groupName)
                completion(.success(desURL))
            } catch {
                completion(.failure(NetworkError.fileDownloadFailed(error)))
            }
        }
    }

    // MARK: - 📥 文件下载（支持 async/await）

    func download(from url: String, cred: COSSTSResponse, targetFolder: URL) async throws -> URL {
        guard let fileURL = URL(string: url) else { throw NetworkError.invalidURL }
        //  生成最终的目标文件路径
        let destinationURL = targetFolder.appendingPathComponent(fileURL.lastPathComponent)

        var request = URLRequest(url: fileURL)
        request.httpMethod = HTTPMethod.GET.rawValue

        // 生成签名
        let authorization = signer.sign(secretId: cred.credentials.tmpSecretId,
                                        secretKey: cred.credentials.tmpSecretKey,
                                        httpMethod: HTTPMethod.GET.rawValue,
                                        urlPath: fileURL.path(),
                                        headers: request.allHTTPHeaderFields ?? [:],
                                        queryParams: getQueryParams(from: request),
                                        expiration: TimeInterval(TencentCOSDownloader.expiration))
        request.setValue(authorization, forHTTPHeaderField: TencentCOSDownloader.Authorization)
        request.setValue(cred.credentials.sessionToken, forHTTPHeaderField: TencentCOSDownloader.securityToken)

        let (tempURL, response) = try await session.download(for: request)

        guard let httpResponse = response as? HTTPURLResponse, (200 ... 299).contains(httpResponse.statusCode) else {
            throw NetworkError.invalidResponse
        }

        do {
            if !fileManager.fileExists(atPath: targetFolder.path) {
                try fileManager.createDirectory(at: targetFolder, withIntermediateDirectories: true, attributes: nil)
            }
            if fileManager.fileExists(atPath: destinationURL.path) {
                try fileManager.removeItem(at: destinationURL)
            }
            try fileManager.moveItem(at: tempURL, to: destinationURL)
            return destinationURL
        } catch {
            throw NetworkError.fileDownloadFailed(error)
        }
    }

    // MARK: - 📥 批量文件下载（支持 async/await）

    private func downloadFilesForCOS(from urls: [String],
                                     cred: COSSTSResponse,
                                     targetFolder: URL) async throws -> [URL]
    {
        var savedFiles: [URL] = []
        for (_, url) in urls.enumerated() {
            let savedFile = try await download(from: url, cred: cred, targetFolder: targetFolder)
            savedFiles.append(savedFile)
        }
        return savedFiles
    }

    // MARK: - 📥 批量文件并发下载（支持 async/await）

    private func downloadFilesForCOSAsync(from urls: [String],
                                          cred: COSSTSResponse,
                                          targetFolder: URL) async throws -> [URL]
    {
        var savedFiles: [URL] = []

        // 使用 TaskGroup 实现并发下载
        try await withThrowingTaskGroup(of: URL.self) { group in
            for url in urls {
                group.addTask {
                    try await self.download(from: url, cred: cred, targetFolder: targetFolder)
                }
            }

            for try await savedFile in group {
                savedFiles.append(savedFile)
            }
        }

        return savedFiles
    }

    // MARK: - 📤 单文件上传（支持 async/await）

    func uploadFile(fileURL: String,
                    cred: COSSTSResponse,
                    folderName: String,
                    uploadFileName: String) async throws -> String
    {
        guard let file = URL(string: fileURL) else { throw NetworkError.invalidURL }
        let pathExtension = file.pathExtension
        guard let uploadURL = URL(string: "https://\(cred.bucket).cos.\(cred.region).myqcloud.com/\(folderName)/\(uploadFileName).\(pathExtension)") else { throw NetworkError.invalidURL }

        var request = URLRequest(url: uploadURL)
        request.httpMethod = HTTPMethod.PUT.rawValue

        // 获取文件属性
        let fileAttributes = try FileManager.default.attributesOfItem(atPath: file.path)
        let fileSize = fileAttributes[.size] as? UInt64 ?? 0

        // 设置 Content-Length
        request.setValue("\(fileSize)", forHTTPHeaderField: "Content-Length")

        // 设置 Content-Type
        if let mimeType = mimeTypeForPath(path: file.path) {
            request.setValue(mimeType, forHTTPHeaderField: "Content-Type")
        }
        // 计算 Content-MD5
        if let fileData = try? Data(contentsOf: file) {
            let md5Data = Insecure.MD5.hash(data: fileData)
            let md5Base64 = Data(md5Data).base64EncodedString()
            request.setValue(md5Base64, forHTTPHeaderField: "Content-MD5")
        }
        // 生成签名
        let authorization = signer.sign(secretId: cred.credentials.tmpSecretId,
                                        secretKey: cred.credentials.tmpSecretKey,
                                        httpMethod: HTTPMethod.PUT.rawValue,
                                        urlPath: uploadURL.path(),
                                        headers: request.allHTTPHeaderFields ?? [:],
                                        queryParams: getQueryParams(from: request),
                                        expiration: TimeInterval(TencentCOSDownloader.expiration))
        request.setValue(authorization, forHTTPHeaderField: TencentCOSDownloader.Authorization)
        request.setValue(cred.credentials.sessionToken, forHTTPHeaderField: TencentCOSDownloader.securityToken)

        let (data, response) = try await session.upload(for: request, fromFile: file)

        guard let httpResponse = response as? HTTPURLResponse, (200 ... 299).contains(httpResponse.statusCode) else {
            throw NetworkError.invalidResponse
        }

        return String(data: data, encoding: .utf8) ?? "Upload Success"
    }

    // MARK: - 📤 批量文件上传（支持 async/await）

    func uploadFiles(fileURLs: [String],
                     cred: COSSTSResponse,
                     folderName: String,
                     groupName: String) async throws -> [String]
    {
        var results: [String] = []
        for (index, fileURL) in fileURLs.enumerated() {
            let result = try await uploadFile(fileURL: fileURL, cred: cred, folderName: folderName, uploadFileName: "\(groupName)\(index)")
            results.append(result)
        }
        return results
    }

    // MARK: 获取文件的后缀

    private func extensionForPath(path: String) -> String {
        let url = URL(fileURLWithPath: path)
        return url.pathExtension
    }

    // 获取文件的 MIME 类型
    private func mimeTypeForPath(path: String) -> String? {
        let url = URL(fileURLWithPath: path)
        let pathExtension = url.pathExtension
        if let uti = UTType(filenameExtension: pathExtension),
           let mimeType = uti.preferredMIMEType
        {
            return mimeType
        }
        return nil
    }

    // MARK: - 生成签名相关参数

    private func getQueryParams(from request: URLRequest) -> [String: String] {
        guard let url = request.url,
              let components = URLComponents(url: url, resolvingAgainstBaseURL: false),
              let queryItems = components.queryItems
        else {
            return [:] // 没有 query 参数时返回空字典
        }

        // 转换 QueryItems 为 [String: String] 字典
        var queryParams: [String: String] = [:]
        for item in queryItems {
            if let value = item.value { // 确保 value 不为 nil
                queryParams[item.name] = value
            }
        }

        return queryParams
    }
}

struct COSCredentials: Codable {
    let sessionToken: String
    let tmpSecretId: String
    let tmpSecretKey: String
    let startTime: Int
    let expiredTime: Int
}

struct COSSTSResponse: Codable {
    let credentials: COSCredentials
    let region: String
    let bucket: String
    let allowPrefix: String
}
