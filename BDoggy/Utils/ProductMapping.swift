import Foundation

struct ProductInfo: Codable {
    let downloadUrls: [String]
    let displayIcon: String
    let publisher: String
    let groupName: String
    let productType: String
    let order: Int
    let update: Bool?
}

struct ProductConfig: Codable {
    var products: [String: ProductInfo]
    var system: [ProductInfo]
}

class ProductMapping {
    static let shared = ProductMapping()
    private var config: ProductConfig?
    var purchasedItems = [ProductInfo]()
    private let configURL = URL(string: "https://your-api.com/product_config.json")! // 远程配置URL
    
    // 获取所有产品标识符
    var productIdentifiers: [String] {
        return config?.products.keys.map { $0 } ?? []
    }
    
    // 获取所有产品标识符
    var systemProducts: [ProductInfo] {
        return config?.system ?? []
    }
    
    private init() {
        loadLocalConfig()
        downloadAll()
    }
    
    // 加载本地配置
    private func loadLocalConfig() {
        guard let configPath = Bundle.main.path(forResource: "product_config", ofType: "json"),
              let configData = try? Data(contentsOf: URL(fileURLWithPath: configPath)),
              let loadedConfig = try? JSONDecoder().decode(ProductConfig.self, from: configData)
        else {
            Logger.error("无法加载本地配置文件")
            return
        }
        config = loadedConfig
    }
    
    // 从服务器获取配置
    func fetchRemoteConfig() async {
        do {
            let (data, response) = try await URLSession.shared.data(from: configURL)
            // 验证 HTTP 响应
            guard let httpResponse = response as? HTTPURLResponse,
                  (200 ... 299).contains(httpResponse.statusCode)
            else {
                Logger.error("服务器响应错误: \(response)")
                return
            }
        
            // 验证数据不为空
            guard !data.isEmpty else {
                Logger.error("获取到的配置数据为空")
                return
            }
        
            let remoteConfig = try JSONDecoder().decode(ProductConfig.self, from: data)
            // 验证配置数据的有效性
            guard !remoteConfig.products.isEmpty else {
                Logger.error("配置中没有产品数据")
                return
            }
        
            await MainActor.run {
                self.config = remoteConfig
                // 保存到本地缓存
                self.saveConfigToCache(remoteConfig)
            }
        } catch {
            Logger.error("获取远程配置失败: \(error.localizedDescription)")
        }
    }
    
    // 保存配置到本地缓存
    private func saveConfigToCache(_ config: ProductConfig) {
        guard let cacheDir = FileManager.default.urls(for: .cachesDirectory, in: .userDomainMask).first else {
            return
        }
        
        let cacheFile = cacheDir.appendingPathComponent("product_config_cache.json")
        
        do {
            let data = try JSONEncoder().encode(config)
            try data.write(to: cacheFile)
        } catch {
            Logger.error("保存配置到缓存失败: \(error.localizedDescription)")
        }
    }
    
    // 获取产品信息
    func getProductInfo(_ productId: String) -> ProductInfo? {
        return config?.products[productId]
    }
    
    private func downloadAll() {
        guard let config = config, !config.system.isEmpty else {
            return
        }
        for product in config.system {
            download(product: product)
        }
    }
    
    //
    private func download(product: ProductInfo) {
        let dir = FileHelper.shared.getUserFrameDirURL(ownerID: product.publisher, userID: product.publisher, groupName: product.groupName)

        // 根据 update 标志决定下载策略
        let downloadUrls: [String]
        if product.update == true {
            // 如果需要更新，下载所有文件
            downloadUrls = product.downloadUrls
            // 删除现有文件夹并重新创建
            do {
                if FileManager.default.fileExists(atPath: dir.path) {
                    try FileManager.default.removeItem(at: dir)
                }
                try FileManager.default.createDirectory(at: dir, withIntermediateDirectories: true, attributes: nil)
            } catch {
                Logger.error("更新文件夹失败: \(error.localizedDescription)")
            }
        } else {
            // 如果不需要更新，只下载未下载的文件
            downloadUrls = product.downloadUrls.filter {
                guard let url = URL(string: $0) else { return false }
                return !self.isImageDownloaded(url: url, downloadDirURL: dir)
            }
        }
        
        guard !product.downloadUrls.isEmpty else {
            return
        }
        
        guard !downloadUrls.isEmpty else {
            return
        }
        
        // TODO: - 需优化未检查每个url是否是远程地址或本地地址
        if Utils.isRemote(urlString: product.downloadUrls[0]) {
            TencentCOSDownloader.shared.fetchTemporaryCredentials(urls: downloadUrls, targetFolder: dir) { (_: Result<[URL], NetworkError>) in
            }
        } else {
            FileHelper.shared.copyResourcesToFolder(from: downloadUrls, targetFolder: dir) { (_: Result<[URL], FileError>) in
            }
        }
        Logger.info("✅下载到本地\(product.groupName)")
    }
    
    private func isAllDownloaded(urls: [String], downloadDirURL: URL) -> Bool {
        // 判断是否所有图片已下载
        let allImagesDownloaded = urls.allSatisfy {
            guard let url = URL(string: $0) else { return true }
            return self.isImageDownloaded(url: url, downloadDirURL: downloadDirURL)
        }
        return allImagesDownloaded
    }

    // 判断图片是否已下载
    private func isImageDownloaded(url: URL, downloadDirURL: URL) -> Bool {
        let fileName = url.lastPathComponent
        let fileURL = downloadDirURL.appendingPathComponent(fileName)
        return FileManager.default.fileExists(atPath: fileURL.path)
    }
}
