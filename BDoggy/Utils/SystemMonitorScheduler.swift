//
//  SystemMonitorScheduler.swift
//  BDoggy
//
//  Created by K4 on 2025/1/11.
//

import AppKit
import Combine
import Foundation

/// 系统监控调度器
/// 负责定时采集系统指标并存储到数据库
@Observable
class SystemMonitorScheduler {
    static let shared = SystemMonitorScheduler()
    
    // MARK: - 属性
    
    private var metricsTimer: Timer?
    private var appUsageTimer: Timer?
    private var currentAppUsage: AppUsageRecord?
    private var appUsages: [String: AppUsageRecord] = [:]
    private var lastActiveApp: String?
    
    let database = EnhancedSystemMonitorDatabase.shared
    private let userDefaults = UserDefaults.standard
    
    // 配置
    var config = MonitorConfig() {
        didSet {
            saveConfig()
            restartMonitoring()
        }
    }
    
    // 状态
    var isMonitoring = false
    var lastCollectionTime: Date?
    var totalDataPoints = 0
    var databaseSize: Int64 = 0
    var saveCount: Int64 = 0
    
    // 权限状态
    var hasAccessibilityPermission = false
    
    // MARK: - 初始化
    
    private init() {
        loadConfig()
        checkPermissions()
    }

    func initialize() {
        setupNotifications()
        
        // 初始化时记录系统信息（只记录一次）
        recordSystemInfoIfNeeded()
    }
    
    deinit {
        stopMonitoring()
    }
    
    // MARK: - 配置管理
    
    private func loadConfig() {
        if let data = userDefaults.data(forKey: "SystemMonitorConfig"),
           let savedConfig = try? JSONDecoder().decode(MonitorConfig.self, from: data)
        {
            config = savedConfig
        }
    }
    
    private func saveConfig() {
        if let data = try? JSONEncoder().encode(config) {
            userDefaults.set(data, forKey: "SystemMonitorConfig")
        }
    }
    
    // MARK: - 权限检查
    
    private func checkPermissions() {
        // 检查辅助功能权限（用于获取当前活跃应用）
        hasAccessibilityPermission = AXIsProcessTrusted()
    }
    
    /// 请求辅助功能权限
    func requestAccessibilityPermission() {
        let options = [kAXTrustedCheckOptionPrompt.takeUnretainedValue(): true]
        AXIsProcessTrustedWithOptions(options as CFDictionary)
        
        // 延迟检查权限状态
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.checkPermissions()
        }
    }
    
    // MARK: - 监控控制
    
    /// 开始监控
    func startMonitoring() {
        guard !isMonitoring else { return }
        
        Logger.info("开始系统监控，采集间隔: \(config.collectionInterval)秒")
        
        // 立即采集一次数据
        collectMetrics()
        
        // 设置定时器
        metricsTimer = Timer.scheduledTimer(withTimeInterval: config.collectionInterval, repeats: true) { _ in
            self.collectMetrics()
        }
        
        // 如果启用应用使用跟踪，设置应用监控
        if config.enableAppUsageTracking {
            startAppUsageTracking()
        }
        
        isMonitoring = true
        updateStatus()
    }
    
    /// 停止监控
    func stopMonitoring() {
        guard isMonitoring else { return }
        
        Logger.info("停止系统监控")
        
        metricsTimer?.invalidate()
        metricsTimer = nil
        
        appUsageTimer?.invalidate()
        appUsageTimer = nil
        
        // 结束当前应用使用记录
        endCurrentAppUsage()
        
        isMonitoring = false
        updateStatus()
    }
    
    /// 重启监控（配置更改时）
    private func restartMonitoring() {
        if isMonitoring {
            stopMonitoring()
            startMonitoring()
        }
    }
    
    // MARK: - 数据采集
    
    /// 采集系统指标
    private func collectMetrics() {
        DispatchQueue.global(qos: .background).async {
            let metrics = EnhancedSystemMetrics()

            // 存储到数据库
            if self.database.insertMetricsLog(metrics) {
                DispatchQueue.main.async {
                    self.lastCollectionTime = Date()
                    self.totalDataPoints += 1
                    self.updateStatus()
                    Logger.debug("系统指标采集成功")
                }
            } else {
                Logger.error("系统指标存储失败")
            }
        }
    }
    
    /// 记录系统信息（只记录一次）
    private func recordSystemInfoIfNeeded() {
        // 检查是否已经记录过系统信息
        if database.getSystemInfo() == nil {
            let systemInfo = SystemInfo()
            if database.insertSystemInfo(systemInfo) {
                Logger.info("系统信息记录成功")
            } else {
                Logger.error("系统信息记录失败")
            }
        }
    }
    
    // MARK: - 应用使用跟踪
    
    /// 开始应用使用跟踪
    private func startAppUsageTracking() {
//        guard hasAccessibilityPermission else {
//            Logger.warning("没有辅助功能权限，无法跟踪应用使用")
//            return
//        }
        
        // 每10秒检查一次活跃应用
        appUsageTimer = Timer.scheduledTimer(withTimeInterval: 10.0, repeats: true) { _ in
            self.trackActiveApp()
        }
        
        // 立即检查一次
        trackActiveApp()
    }
    
    /// 跟踪当前活跃应用
    private func trackActiveApp() {
        let activeApp = EnhancedSystemInfoCollector.getActiveApplication()
        
        guard let bundleID = activeApp.bundleID,
              let appName = activeApp.name
        else {
            return
        }
        // 如果应用发生变化
        if lastActiveApp != bundleID {
            // 结束之前的应用使用记录
            endCurrentAppUsage()
            
            // 开始新的应用使用记录
            startNewAppUsage(bundleID: bundleID, appName: appName)
            
            lastActiveApp = bundleID
        } else {
            // 更新当前应用的使用数据
            updateCurrentAppUsage()
        }
        saveCount += 1
        if saveCount == 3 {}
    }
    
    /// 开始新的应用使用记录
    private func startNewAppUsage(bundleID: String, appName: String) {
        currentAppUsage = appUsages[bundleID]
        if currentAppUsage == nil {
            currentAppUsage = AppUsageRecord(bundleID: bundleID, appName: appName)
        } else {
            currentAppUsage?.resetStartTime()
        }
        
        appUsages[bundleID] = currentAppUsage
        Logger.debug("开始跟踪应用: \(appName)")
    }
    
    /// 更新当前应用使用数据
    private func updateCurrentAppUsage() {
        guard var usage = currentAppUsage else { return }
        
        // 获取当前CPU和内存使用率
        let cpuUsage = EnhancedSystemInfoCollector.getCPUUsage()
        let memoryInfo = EnhancedSystemInfoCollector.getMemoryInfo()
        
        usage.updateCPUUsage(cpuUsage)
        usage.updateMemoryUsage(memoryInfo.usedGB * 1024) // 转换为MB
        
        currentAppUsage = usage
        
        Logger.debug("更新跟踪应用: \(usage.appName)")
    }
    
    /// 结束当前应用使用记录
    private func endCurrentAppUsage() {
        guard var usage = currentAppUsage else { return }
        
        usage.endSession()
        
        appUsages[usage.bundleID] = currentAppUsage
        // 只记录使用时间超过30秒的应用
        if usage.duration > 30 {
            if database.insertAppUsage(usage) {
                Logger.debug("应用使用记录保存成功: \(usage.appName), 使用时长: \(Int(usage.duration))秒")
            } else {
                Logger.error("应用使用记录保存失败")
            }
        }
        currentAppUsage = nil
    }
    
    // MARK: - 通知处理
    
    private func setupNotifications() {
        // 监听某个应用失去激活状态（退到后台）
        NSWorkspace.shared.notificationCenter.addObserver(
            forName: NSWorkspace.didDeactivateApplicationNotification,
            object: nil,
            queue: .main
        ) { _ in
            Logger.debug("应用使用didDeactivateApplicationNotification")
        }
        
        // 监听应用切换事件
        NSWorkspace.shared.notificationCenter.addObserver(
            forName: NSWorkspace.didActivateApplicationNotification,
            object: nil,
            queue: .main
        ) { _ in
            Logger.debug("应用使用记录保存成功: \(String(describing: self.currentAppUsage?.appName)), 使用时长: \(Int(self.currentAppUsage?.getDurationNoRecord() ?? 0.0))秒")
        }
    }
    
    // MARK: - 状态更新
    
    private func updateStatus() {
        databaseSize = database.getDatabaseSize()
    }
    
    // MARK: - 数据管理
    
    /// 清除所有数据
    func clearAllData() -> Bool {
        let success = database.clearAllData()
        if success {
            totalDataPoints = 0
            updateStatus()
            Logger.info("所有监控数据已清除")
        }
        return success
    }
    
    /// 清除旧数据
    func cleanupOldData() -> Bool {
        let success = database.cleanupOldData(olderThanDays: config.maxDataRetentionDays)
        if success {
            updateStatus()
            Logger.info("已清除\(config.maxDataRetentionDays)天前的旧数据")
        }
        return success
    }
    
    /// 获取数据统计
    func getDataStats() -> [String: Any] {
        return [
            "total_data_points": totalDataPoints,
            "database_size_mb": Double(databaseSize) / (1024 * 1024),
            "last_collection": lastCollectionTime?.timeIntervalSince1970 ?? 0,
            "is_monitoring": isMonitoring,
            "collection_interval": config.collectionInterval,
            "has_accessibility_permission": hasAccessibilityPermission
        ]
    }
    
    /// 获取最近的指标数据（用于图表显示）
    func getRecentMetrics(days: Int = 7) -> [[String: Any]] {
        let endDate = Date()
        let startDate = endDate.addingTimeInterval(-TimeInterval(days * 24 * 3600))
        return database.getMetricsForChart(from: startDate, to: endDate)
    }
    
    /// 获取应用使用汇总
    func getAppUsageSummary(days: Int = 7) -> [[String: Any]] {
        return database.getAppUsageSummary(days: days)
    }
}
