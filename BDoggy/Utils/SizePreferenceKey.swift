//
//  SizePreferenceKey.swift
//  BDoggy
//
//  Created by K4 on 2025/5/7.
//


import SwiftUI

// PreferenceKey 用于传递尺寸信息
struct SizePreferenceKey: PreferenceKey {
    static var defaultValue: CGSize = .zero
    static func reduce(value: inout CGSize, nextValue: () -> CGSize) {
        value = nextValue()
    }
}

// ViewModifier 封装 GeometryReader
struct MeasureSizeModifier: ViewModifier {
    let onChange: (CGSize) -> Void

    func body(content: Content) -> some View {
        content
            .background(
                GeometryReader { geometry in
                    Color.clear
                        .preference(key: SizePreferenceKey.self, value: geometry.size)
                }
            )
            .onPreferenceChange(SizePreferenceKey.self, perform: onChange)
    }
}

// View 扩展，使用简便修饰符
extension View {
    func measureSize(onChange: @escaping (CGSize) -> Void) -> some View {
        self.modifier(MeasureSizeModifier(onChange: onChange))
    }
}