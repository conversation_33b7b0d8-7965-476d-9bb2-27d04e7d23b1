//
//  SystemMonitorQuickStart.swift
//  BDoggy
//
//  Created by K4 on 2025/1/11.
//

import Foundation

/// 系统监控快速启动工具
/// 提供便捷的初始化、测试和调试功能
class SystemMonitorQuickStart {
    
    /// 快速初始化并启动系统监控
    static func quickStart() {
        Logger.info("🚀 系统监控快速启动...")

        // 1. 初始化系统监控
        SystemMonitorInitializer.shared.initialize()

        // 2. 检查权限
        let permissionManager = PermissionManager.shared
        permissionManager.checkAllPermissions()

        if !permissionManager.hasRequiredPermissionsForMonitoring() {
            Logger.warning("⚠️ 缺少必要权限，请授予辅助功能权限")
            permissionManager.requestAccessibilityPermission()
            return
        }

        // 3. 启动监控
        SystemMonitorInitializer.shared.startMonitoring()

        // 4. 显示状态
        showStatus()

        Logger.success("✅ 系统监控启动完成")
    }
    
    /// 显示当前状态
    static func showStatus() {
        let status = SystemMonitorInitializer.shared.getMonitoringStatus()

        Logger.info("📊 系统监控状态:")
        Logger.info("  - 已初始化: \(status["is_initialized"] as? Bool ?? false)")
        Logger.info("  - 监控中: \(status["is_monitoring"] as? Bool ?? false)")
        Logger.info("  - 数据点: \(status["total_data_points"] as? Int ?? 0)")

        if let dbSize = status["database_size_bytes"] as? Int64 {
            Logger.info("  - 数据库大小: \(formatFileSize(dbSize))")
        }

        if let permissions = status["permissions"] as? [String: Bool] {
            Logger.info("  - 权限状态: \(permissions)")
        }

        if let config = status["config"] as? [String: Any] {
            if let interval = config["collection_interval"] as? TimeInterval {
                Logger.info("  - 采集间隔: \(formatInterval(interval))")
            }
        }

        // 显示日志统计
        let logStats = Logger.getSimpleLogStats()
        Logger.info("📝 日志状态:")
        Logger.info("  - 文件大小: \(logStats["file_size_mb"] as? String ?? "0.00")MB")
        Logger.info("  - 当前级别: \(logStats["current_level"] as? String ?? "INFO")")
        Logger.info("  - 文件日志: \(logStats["file_logging"] as? Bool ?? false ? "启用" : "禁用")")
    }
    
    /// 运行诊断
    static func runDiagnostics() {
        Logger.info("🔍 运行系统监控诊断...")

        // 1. 检查权限
        diagnosePermissions()

        // 2. 检查数据库
        diagnoseDatabase()

        // 3. 检查系统信息采集
        diagnoseSystemCollection()

        // 4. 检查调度器
        diagnoseScheduler()

        // 5. 检查日志系统
        diagnoseLogging()

        Logger.success("✅ 诊断完成")
    }
    
    private static func diagnosePermissions() {
        Logger.info("检查权限状态...")
        
        let permissionManager = PermissionManager.shared
        let permissions = permissionManager.getAllPermissionStatus()
        
        for (key, value) in permissions {
            let status = value ? "✅" : "❌"
            Logger.info("  \(status) \(key): \(value)")
        }
        
        let missingPermissions = permissionManager.getMissingPermissions()
        if !missingPermissions.isEmpty {
            Logger.warning("缺少权限: \(missingPermissions.map { $0.displayName }.joined(separator: ", "))")
        }
    }
    
    private static func diagnoseDatabase() {
        Logger.info("检查数据库状态...")
        
        let database = EnhancedSystemMonitorDatabase.shared
        let dbPath = database.getDatabasePath()
        let dbSize = database.getDatabaseSize()
        
        Logger.info("  📁 数据库路径: \(dbPath)")
        Logger.info("  📊 数据库大小: \(formatFileSize(dbSize))")
        
        // 检查系统信息
        if let systemInfo = database.getSystemInfo() {
            Logger.info("  ✅ 系统信息已记录: \(systemInfo.deviceModel)")
        } else {
            Logger.warning("  ⚠️ 系统信息未记录")
        }
        
        // 检查最近数据
        let recentData = database.getMetricsForChart(from: Date().addingTimeInterval(-3600), to: Date())
        Logger.info("  📈 最近1小时数据点: \(recentData.count)")
    }
    
    private static func diagnoseSystemCollection() {
        Logger.info("检查系统信息采集...")
        
        // 测试CPU采集
        let cpuUsage = EnhancedSystemInfoCollector.getCPUUsage()
        Logger.info("  🖥️ CPU使用率: \(String(format: "%.2f", cpuUsage))%")
        
        // 测试内存采集
        let memoryInfo = EnhancedSystemInfoCollector.getMemoryInfo()
        Logger.info("  💾 内存使用率: \(String(format: "%.2f", memoryInfo.usagePercent))%")
        
        // 测试磁盘采集
        let diskInfo = EnhancedSystemInfoCollector.getDiskInfo()
        Logger.info("  💿 磁盘使用率: \(String(format: "%.2f", diskInfo.usagePercent))%")
        
        // 测试电池采集
        let batteryInfo = EnhancedSystemInfoCollector.getBatteryInfo()
        Logger.info("  🔋 电源类型: \(batteryInfo.powerSourceType)")
    }
    
    private static func diagnoseScheduler() {
        Logger.info("检查监控调度器...")
        
        let scheduler = SystemMonitorScheduler.shared
        Logger.info("  ⏰ 监控状态: \(scheduler.isMonitoring ? "运行中" : "已停止")")
        Logger.info("  📊 数据点总数: \(scheduler.totalDataPoints)")
        Logger.info("  ⚙️ 采集间隔: \(formatInterval(scheduler.config.collectionInterval))")
        
        if let lastCollection = scheduler.lastCollectionTime {
            let timeSince = Date().timeIntervalSince(lastCollection)
            Logger.info("  🕐 最后采集: \(formatTimeAgo(timeSince))前")
        }
    }

    private static func diagnoseLogging() {
        Logger.info("检查日志系统...")

        // 获取日志健康状态
        let health = Logger.checkLogHealth()

        Logger.info("  📁 文件存在: \(health["file_exists"] as? Bool ?? false ? "是" : "否")")
        Logger.info("  📊 文件大小: \(health["file_size_mb"] as? Double ?? 0.0)MB")
        Logger.info("  📖 可读: \(health["readable"] as? Bool ?? false ? "是" : "否")")
        Logger.info("  ✏️ 可写: \(health["writable"] as? Bool ?? false ? "是" : "否")")

        if let sizeStatus = health["size_status"] as? String {
            let statusEmoji = sizeStatus == "large" ? "⚠️" : (sizeStatus == "medium" ? "📈" : "✅")
            Logger.info("  \(statusEmoji) 大小状态: \(sizeStatus)")
        }

        if let recentlyActive = health["recently_active"] as? Bool {
            Logger.info("  🔄 最近活跃: \(recentlyActive ? "是" : "否")")
        }

        if let errorRate = health["error_rate"] as? Double {
            Logger.info("  ❌ 错误率: \(String(format: "%.2f", errorRate * 100))%")
        }

        // 显示日志级别统计
        let levelStats = Logger.getLogLevelStats()
        if !levelStats.isEmpty {
            Logger.info("  📊 日志级别统计:")
            for (level, count) in levelStats.sorted(by: { $0.key < $1.key }) {
                Logger.info("    - \(level): \(count)")
            }
        }

        // 显示最近的错误（如果有）
        let recentErrors = Logger.getRecentErrors(limit: 3)
        if !recentErrors.isEmpty {
            Logger.info("  🚨 最近错误:")
            for error in recentErrors {
                Logger.info("    - \(error)")
            }
        }
    }
    
    /// 性能测试
    static func runPerformanceTest() {
        Logger.info("🏃‍♂️ 运行性能测试...")
        
        let iterations = 10
        var totalTime: TimeInterval = 0
        
        for i in 1...iterations {
            let startTime = Date()
            
            // 采集一次完整的系统指标
            let _ = EnhancedSystemMetrics()
            
            let duration = Date().timeIntervalSince(startTime)
            totalTime += duration
            
            Logger.debug("第 \(i) 次采集耗时: \(String(format: "%.3f", duration * 1000))ms")
        }
        
        let averageTime = totalTime / Double(iterations)
        Logger.info("📊 性能测试结果:")
        Logger.info("  - 总耗时: \(String(format: "%.3f", totalTime * 1000))ms")
        Logger.info("  - 平均耗时: \(String(format: "%.3f", averageTime * 1000))ms")
        Logger.info("  - 每秒可采集: \(String(format: "%.1f", 1.0 / averageTime))次")
    }
    
    /// 数据导出测试
    static func testDataExport() {
        Logger.info("📤 测试数据导出...")

        let tempURL = FileManager.default.temporaryDirectory.appendingPathComponent("system_monitor_test_export.json")

        if SystemMonitorInitializer.shared.exportData(to: tempURL, days: 1) {
            Logger.success("✅ 系统数据导出成功: \(tempURL.path)")

            // 检查文件大小
            do {
                let attributes = try FileManager.default.attributesOfItem(atPath: tempURL.path)
                if let fileSize = attributes[.size] as? Int64 {
                    Logger.info("📁 导出文件大小: \(formatFileSize(fileSize))")
                }
            } catch {
                Logger.error("无法获取导出文件大小: \(error)")
            }

            // 清理临时文件
            try? FileManager.default.removeItem(at: tempURL)
        } else {
            Logger.error("❌ 系统数据导出失败")
        }
    }

    /// 日志导出测试
    static func testLogExport() {
        Logger.info("📝 测试日志导出...")

        let tempDir = FileManager.default.temporaryDirectory

        // 测试完整日志导出
        let fullLogURL = tempDir.appendingPathComponent("test_full_log.log")
        if Logger.exportLogFile(to: fullLogURL) {
            Logger.success("✅ 完整日志导出成功")
            let fileSize = getFileSize(at: fullLogURL)
            Logger.info("📁 完整日志大小: \(formatFileSize(fileSize))")
        } else {
            Logger.error("❌ 完整日志导出失败")
        }

        // 测试过滤日志导出
        let filteredLogURL = tempDir.appendingPathComponent("test_filtered_log.log")
        if Logger.exportErrorsAndWarnings(to: filteredLogURL) {
            Logger.success("✅ 过滤日志导出成功")
            let fileSize = getFileSize(at: filteredLogURL)
            Logger.info("📁 过滤日志大小: \(formatFileSize(fileSize))")
        } else {
            Logger.error("❌ 过滤日志导出失败")
        }

        // 测试最近日志导出
        let recentLogURL = tempDir.appendingPathComponent("test_recent_log.log")
        if Logger.exportRecentLogs(to: recentLogURL, days: 1) {
            Logger.success("✅ 最近日志导出成功")
            let fileSize = getFileSize(at: recentLogURL)
            Logger.info("📁 最近日志大小: \(formatFileSize(fileSize))")
        } else {
            Logger.error("❌ 最近日志导出失败")
        }

        // 显示导出统计
        let exportStats = Logger.getExportStats()
        Logger.info("📊 导出统计: \(exportStats)")

        // 清理临时文件
        try? FileManager.default.removeItem(at: fullLogURL)
        try? FileManager.default.removeItem(at: filteredLogURL)
        try? FileManager.default.removeItem(at: recentLogURL)
    }

    private static func getFileSize(at url: URL) -> Int64 {
        do {
            let attributes = try FileManager.default.attributesOfItem(atPath: url.path)
            return attributes[.size] as? Int64 ?? 0
        } catch {
            return 0
        }
    }
    
    /// 维护操作
    static func performMaintenance() {
        Logger.info("🧹 执行维护操作...")
        
        SystemMonitorInitializer.shared.performMaintenance()
        
        // 显示维护后状态
        showStatus()
    }
    
    /// 重置系统监控
    static func reset() {
        Logger.warning("🔄 重置系统监控...")
        
        // 停止监控
        SystemMonitorInitializer.shared.stopMonitoring()
        
        // 清除数据
        let database = EnhancedSystemMonitorDatabase.shared
        if database.clearAllData() {
            Logger.info("✅ 数据已清除")
        }
        
        // 清除日志
        if Logger.clearLogFile() {
            Logger.info("✅ 日志已清除")
        }
        
        Logger.success("✅ 重置完成")
    }
    
    // MARK: - 辅助方法
    
    private static func formatFileSize(_ bytes: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.countStyle = .file
        return formatter.string(fromByteCount: bytes)
    }
    
    private static func formatInterval(_ seconds: TimeInterval) -> String {
        if seconds < 60 {
            return "\(Int(seconds))秒"
        } else if seconds < 3600 {
            return "\(Int(seconds / 60))分钟"
        } else {
            return "\(Int(seconds / 3600))小时"
        }
    }
    
    private static func formatTimeAgo(_ seconds: TimeInterval) -> String {
        if seconds < 60 {
            return "\(Int(seconds))秒"
        } else if seconds < 3600 {
            return "\(Int(seconds / 60))分钟"
        } else if seconds < 86400 {
            return "\(Int(seconds / 3600))小时"
        } else {
            return "\(Int(seconds / 86400))天"
        }
    }
}

// MARK: - 调试工具

extension SystemMonitorQuickStart {
    
    /// 启用调试模式
    static func enableDebugMode() {
        Logger.setLogLevel(.debug)
        Logger.setConsoleLogging(enabled: true)
        Logger.info("🐛 调试模式已启用")
    }
    
    /// 禁用调试模式
    static func disableDebugMode() {
        Logger.setLogLevel(.info)
        Logger.setConsoleLogging(enabled: false)
        Logger.info("🔇 调试模式已禁用")
    }
    
    /// 生成测试数据
    static func generateTestData(count: Int = 10) {
        Logger.info("🎲 生成 \(count) 条测试数据...")
        
        let database = EnhancedSystemMonitorDatabase.shared
        var successCount = 0
        
        for i in 1...count {
            let metrics = EnhancedSystemMetrics()
            if database.insertMetricsLog(metrics) {
                successCount += 1
            }
            
            // 添加小延迟避免时间戳重复
            Thread.sleep(forTimeInterval: 0.1)
        }
        
        Logger.info("✅ 成功生成 \(successCount)/\(count) 条测试数据")
    }
    
    /// 监控实时数据
    static func monitorRealTimeData(duration: TimeInterval = 60) {
        Logger.info("📡 开始实时监控 \(Int(duration)) 秒...")
        
        let startTime = Date()
        let timer = Timer.scheduledTimer(withTimeInterval: 5.0, repeats: true) { timer in
            let elapsed = Date().timeIntervalSince(startTime)
            
            if elapsed >= duration {
                timer.invalidate()
                Logger.info("⏹️ 实时监控结束")
                return
            }
            
            // 显示当前系统状态
            let cpuUsage = EnhancedSystemInfoCollector.getCPUUsage()
            let memoryInfo = EnhancedSystemInfoCollector.getMemoryInfo()
            
            Logger.info("📊 CPU: \(String(format: "%.1f", cpuUsage))%, 内存: \(String(format: "%.1f", memoryInfo.usagePercent))%")
        }
        
        RunLoop.current.add(timer, forMode: .common)
    }
}
