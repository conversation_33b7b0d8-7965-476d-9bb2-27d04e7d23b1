//
//  SystemInfoCollector.swift
//  BDoggy
//
//  Created by System Monitor on 2024/12/11.
//  Enhanced by K4 on 2025/1/11.
//

import Foundation
import IOKit
import IOKit
import IOKit.ps
import IOKit.pwr_mgt
import AppKit

/// 系统基础信息采集器（静态信息，只需采集一次）
class SystemInfoCollector {
    // MARK: - 设备型号

    static func getDeviceModel() -> String {
        var size = 0
        sysctlbyname("hw.model", nil, &size, nil, 0)
        var model = [CChar](repeating: 0, count: size)
        sysctlbyname("hw.model", &model, &size, nil, 0)
        return String(cString: model)
    }
    
    // MARK: - CPU 名称

    static func getCPUName() -> String {
        var size = 0
        sysctlbyname("machdep.cpu.brand_string", nil, &size, nil, 0)
        var cpuName = [CChar](repeating: 0, count: size)
        sysctlbyname("machdep.cpu.brand_string", &cpuName, &size, nil, 0)
        return String(cString: cpuName)
    }
    
    // MARK: - 总内存大小 (GB)

    static func getTotalMemoryGB() -> Double {
        var size = 0
        sysctlbyname("hw.memsize", nil, &size, nil, 0)
        var memSize: UInt64 = 0
        sysctlbyname("hw.memsize", &memSize, &size, nil, 0)
        return Double(memSize) / (1024 * 1024 * 1024) // 转换为 GB
    }
    
    // MARK: - 总磁盘容量 (GB)

    static func getTotalDiskGB() -> Double {
        do {
            let homeURL = FileManager.default.homeDirectoryForCurrentUser
            let resourceValues = try homeURL.resourceValues(forKeys: [.volumeTotalCapacityKey])
            if let totalCapacity = resourceValues.volumeTotalCapacity {
                return Double(totalCapacity) / (1024 * 1024 * 1024) // 转换为 GB
            }
        } catch {
            Logger.error("获取磁盘总容量失败", category: "SystemInfo", error: error)
        }
        return 0.0
    }
    
    // MARK: - macOS 版本

    static func getMacOSVersion() -> String {
        let version = ProcessInfo.processInfo.operatingSystemVersion

        return "\(version.majorVersion).\(version.minorVersion).\(version.patchVersion)"
    }
    
    // MARK: - CPU 核心数

    static func getCPUCoreCount() -> Int {
        var size = MemoryLayout<Int>.size
        var coreCount = 0
        sysctlbyname("hw.ncpu", &coreCount, &size, nil, 0)
        return coreCount
    }
    
    // MARK: - CPU 频率 (MHz)

    static func getCPUFrequency() -> Double {
        var size = MemoryLayout<UInt64>.size
        var frequency: UInt64 = 0
        sysctlbyname("hw.cpufrequency_max", &frequency, &size, nil, 0)
        return Double(frequency) / 1000000 // 转换为 MHz
    }
    
    // MARK: - 系统启动时间

    static func getSystemBootTime() -> Date {
        var size = MemoryLayout<timeval>.size
        var bootTime = timeval()
        sysctlbyname("kern.boottime", &bootTime, &size, nil, 0)
        return Date(timeIntervalSince1970: Double(bootTime.tv_sec))
    }
    
    // MARK: - 系统运行时间 (秒)

    static func getSystemUptime() -> TimeInterval {
        let bootTime = getSystemBootTime()
        return Date().timeIntervalSince(bootTime)
    }
    
    // MARK: - 获取完整系统信息

    static func getCompleteSystemInfo() -> [String: Any] {
        return [
            "deviceModel": getDeviceModel(),
            "cpuName": getCPUName(),
            "cpuCoreCount": getCPUCoreCount(),
            "cpuFrequency": getCPUFrequency(),
            "totalMemoryGB": getTotalMemoryGB(),
            "totalDiskGB": getTotalDiskGB(),
            "macOSVersion": getMacOSVersion(),
            "systemBootTime": getSystemBootTime(),
            "systemUptimeSeconds": getSystemUptime()
        ]
    }
}

// MARK: - 扩展：格式化输出

extension SystemInfoCollector {
    /// 格式化内存大小显示
    static func formatMemorySize(_ bytes: UInt64) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useGB, .useMB]
        formatter.countStyle = .decimal
        return formatter.string(fromByteCount: Int64(bytes))
    }
    
    /// 格式化磁盘大小显示
    static func formatDiskSize(_ bytes: UInt64) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useTB, .useGB]
        formatter.countStyle = .decimal
        return formatter.string(fromByteCount: Int64(bytes))
    }
    
    /// 格式化运行时间显示
    static func formatUptime(_ seconds: TimeInterval) -> String {
        let days = Int(seconds) / 86400
        let hours = (Int(seconds) % 86400) / 3600
        let minutes = (Int(seconds) % 3600) / 60
        
        if days > 0 {
            return "\(days)天 \(hours)小时 \(minutes)分钟"
        } else if hours > 0 {
            return "\(hours)小时 \(minutes)分钟"
        } else {
            return "\(minutes)分钟"
        }
    }
}

// MARK: - 调试和日志

extension SystemInfoCollector {
    /// 打印完整系统信息（用于调试）
    static func printSystemInfo() {
        let info = getCompleteSystemInfo()
        Logger.info("=== 系统信息 ===")
        Logger.info("设备型号: \(info["deviceModel"] ?? "未知")")
        Logger.info("CPU: \(info["cpuName"] ?? "未知")")
        Logger.info("CPU核心数: \(info["cpuCoreCount"] ?? 0)")
        Logger.info("CPU频率: \(info["cpuFrequency"] ?? 0) MHz")
        Logger.info("总内存: \(String(format: "%.1f", info["totalMemoryGB"] as? Double ?? 0)) GB")
        Logger.info("总磁盘: \(String(format: "%.1f", info["totalDiskGB"] as? Double ?? 0)) GB")
        Logger.info("macOS版本: \(info["macOSVersion"] ?? "未知")")
        Logger.info("系统启动时间: \(info["systemBootTime"] ?? Date())")
        Logger.info("运行时间: \(formatUptime(info["systemUptimeSeconds"] as? TimeInterval ?? 0))")
        Logger.info("===============")
    }


    /// 获取CPU架构
    static func getCPUArchitecture() -> String {
        var size = 0
        sysctlbyname("hw.targettype", nil, &size, nil, 0)
        var targetType = [CChar](repeating: 0, count: size)
        sysctlbyname("hw.targettype", &targetType, &size, nil, 0)
        let target = String(cString: targetType)

        // 如果无法获取targettype，尝试其他方法
        if target.isEmpty {
            #if arch(arm64)
            return "Apple Silicon"
            #elseif arch(x86_64)
            return "Intel x86_64"
            #else
            return "Unknown"
            #endif
        }

        return target
    }

    /// 获取内核版本
    static func getKernelVersion() -> String {
        var size = 0
        sysctlbyname("kern.version", nil, &size, nil, 0)
        var version = [CChar](repeating: 0, count: size)
        sysctlbyname("kern.version", &version, &size, nil, 0)
        let fullVersion = String(cString: version)

        // 提取版本号部分
        let components = fullVersion.components(separatedBy: " ")
        if components.count > 2 {
            return components[2] // 通常是第三个部分
        }

        return fullVersion.trimmingCharacters(in: .whitespacesAndNewlines)
    }

    /// 获取系统序列号
    static func getSystemSerial() -> String {
        let service = IOServiceGetMatchingService(kIOMasterPortDefault, IOServiceMatching("IOPlatformExpertDevice"))

        guard service != 0 else {
            return "Unknown"
        }

        defer { IOObjectRelease(service) }

        if let serialNumber = IORegistryEntryCreateCFProperty(service, "IOPlatformSerialNumber" as CFString, kCFAllocatorDefault, 0)?.takeRetainedValue() as? String {
            return serialNumber
        }

        return "Unknown"
        }
}
