//
//  NetworkService.swift
//  BDoggy
//
//  Created by K4 on 2025/1/13.
//

import Foundation

class NetworkManager {
    static let shared = NetworkManager() // 单例模式
    private let session: URLSession
//    private let APP_NAME = "BDoggy"

    private init() {
        // 配置 URLSession
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 30 // 设置请求超时时间（秒）
        config.timeoutIntervalForResource = 60 // 资源加载超时
        config.httpAdditionalHeaders = [
            "Content-Type": "application/json",
            "User-Agent": "\(Constants.APP_NAME)/1.0"
        ]
        config.requestCachePolicy = .reloadIgnoringLocalCacheData // 不使用缓存

        self.session = URLSession(configuration: config)
    }

    //  MARK: - **✅ `async/await` 方式请求**

    func request<T: Decodable>(
        url: String,
        method: HTTPMethod = .GET,
        headers: [String: String]? = nil,
        body: Data? = nil
    ) async throws -> T {
        guard let url = URL(string: url) else { throw NetworkError.invalidURL }

        var request = URLRequest(url: url)
        request.httpMethod = method.rawValue
        request.httpBody = body
        headers?.forEach { request.setValue($1, forHTTPHeaderField: $0) }
        JWTManager.shared.addJWT(to: &request)

        let (data, response) = try await session.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse, (200...299).contains(httpResponse.statusCode) else {
            throw NetworkError.invalidResponse
        }

        do {
            return try JSONDecoder().decode(T.self, from: data)
        } catch {
            throw NetworkError.decodingError(error)
        }
    }

    //  MARK: - **✅ 传统回调方式**

    func request<T: Decodable>(
        url: String,
        method: HTTPMethod = .GET,
        headers: [String: String]? = nil,
        body: Data? = nil,
        completion: @escaping (Result<T, NetworkError>) -> Void
    ) {
        guard let url = URL(string: url) else {
            completion(.failure(.invalidURL))
            return
        }

        var request = URLRequest(url: url)
        request.httpMethod = method.rawValue
        request.httpBody = body
        headers?.forEach { request.setValue($1, forHTTPHeaderField: $0) }
        JWTManager.shared.addJWT(to: &request)

        let task = session.dataTask(with: request) { data, response, error in
            if let error = error {
                completion(.failure(.requestFailed(error)))
                return
            }

            guard let httpResponse = response as? HTTPURLResponse, (200...299).contains(httpResponse.statusCode) else {
                completion(.failure(.invalidResponse))
                return
            }

            guard let data = data else {
                completion(.failure(.noData))
                return
            }

            do {
                let decodedData = try JSONDecoder().decode(T.self, from: data)
                completion(.success(decodedData))
            } catch {
                completion(.failure(.decodingError(error)))
            }
        }
        task.resume()
    }

    //  MARK: - **✅ JSON 编码请求**

    func jsonRequest<T: Decodable, U: Encodable>(
        url: String,
        method: HTTPMethod,
        headers: [String: String]? = nil,
        body: U
    ) async throws -> T {
        let jsonData = try JSONEncoder().encode(body)
        return try await request(url: url, method: method, headers: headers, body: jsonData)
    }

    //  MARK: - **✅ 表单数据请求 (`application/x-www-form-urlencoded`)**

    func formRequest<T: Decodable>(
        url: String,
        method: HTTPMethod,
        parameters: [String: String],
        headers: [String: String]? = nil
    ) async throws -> T {
        let bodyString = parameters.map { "\($0.key)=\($0.value)" }.joined(separator: "&")
        let bodyData = bodyString.data(using: .utf8)
        let contentTypeHeader = ["Content-Type": "application/x-www-form-urlencoded"]
        let totalHeader = contentTypeHeader.merging(headers ?? [:]) { first, _ -> String in return first }
        return try await request(url: url, method: method, headers: totalHeader, body: bodyData)
    }

    // MARK: - 📥 文件下载（支持 async/await）

    func downloadFile(
        from url: String,
        saveTo destinationURL: URL,
        headers: [String: String]? = nil
    ) async throws -> URL {
        guard let fileURL = URL(string: url) else { throw NetworkError.invalidURL }

        var request = URLRequest(url: fileURL)
        headers?.forEach { request.setValue($1, forHTTPHeaderField: $0) }
        JWTManager.shared.addJWT(to: &request)

        let (tempURL, response) = try await session.download(for: request)

        guard let httpResponse = response as? HTTPURLResponse, (200...299).contains(httpResponse.statusCode) else {
            throw NetworkError.invalidResponse
        }

        do {
            let fileManager = FileManager.default
            if fileManager.fileExists(atPath: destinationURL.path) {
                try fileManager.removeItem(at: destinationURL)
            }
            try fileManager.moveItem(at: tempURL, to: destinationURL)
            return destinationURL
        } catch {
            throw NetworkError.fileDownloadFailed(error)
        }
    }

    // MARK: - 📥 批量文件下载（支持 async/await）

    func downloadFiles(
        from urls: [String],
        saveTo directory: URL,
        headers: [String: String]? = nil
    ) async throws -> [URL] {
        var savedFiles: [URL] = []
        for (_, url) in urls.enumerated() {
            guard let fileURL = URL(string: url) else { throw NetworkError.invalidURL }
            let destination = directory.appendingPathComponent(fileURL.lastPathComponent)
            let savedFile = try await downloadFile(from: url, saveTo: destination, headers: headers)
            savedFiles.append(savedFile)
        }
        return savedFiles
    }

    // MARK: - 📥 传统回调方式：单个文件下载

    func downloadFile(
        from url: String,
        saveTo destinationURL: URL,
        headers: [String: String]? = nil,
        completion: @escaping (Result<URL, NetworkError>) -> Void
    ) {
        guard let fileURL = URL(string: url) else {
            completion(.failure(.invalidURL))
            return
        }

        var request = URLRequest(url: fileURL)
        headers?.forEach { request.setValue($1, forHTTPHeaderField: $0) }
        JWTManager.shared.addJWT(to: &request)

        let task = session.downloadTask(with: request) { tempURL, response, error in
            if let error = error {
                completion(.failure(.fileDownloadFailed(error)))
                return
            }

            guard let tempURL = tempURL,
                  let httpResponse = response as? HTTPURLResponse,
                  (200...299).contains(httpResponse.statusCode)
            else {
                completion(.failure(.invalidResponse))
                return
            }

            do {
                let fileManager = FileManager.default
                if fileManager.fileExists(atPath: destinationURL.path) {
                    try fileManager.removeItem(at: destinationURL)
                }
                try fileManager.moveItem(at: tempURL, to: destinationURL)
                completion(.success(destinationURL))
            } catch {
                completion(.failure(.fileDownloadFailed(error)))
            }
        }
        task.resume()
    }

    // MARK: - 📥 传统回调方式：批量文件下载

    func downloadFiles(
        from urls: [String],
        saveTo directory: URL,
        headers: [String: String]? = nil,
        completion: @escaping (Result<[URL], NetworkError>) -> Void
    ) {
        var savedFiles: [URL] = []
        let dispatchGroup = DispatchGroup()

        for (index, url) in urls.enumerated() {
            dispatchGroup.enter()
            let destination = directory.appendingPathComponent("file_\(index).tmp")

            downloadFile(from: url, saveTo: destination, headers: headers) { result in
                switch result {
                case .success(let savedURL):
                    savedFiles.append(savedURL)
                case .failure(let error):
                    print("下载失败: \(error)")
                }
                dispatchGroup.leave()
            }
        }

        dispatchGroup.notify(queue: .main) {
            completion(.success(savedFiles))
        }
    }

    // MARK: - 📤 单文件上传（支持 async/await）

    func uploadFile(
        to url: String,
        fileURL: URL,
        headers: [String: String]? = nil
    ) async throws -> String {
        guard let uploadURL = URL(string: url) else { throw NetworkError.invalidURL }

        var request = URLRequest(url: uploadURL)
        request.httpMethod = HTTPMethod.POST.rawValue
        headers?.forEach { request.setValue($1, forHTTPHeaderField: $0) }
        JWTManager.shared.addJWT(to: &request)

        let (data, response) = try await session.upload(for: request, fromFile: fileURL)

        guard let httpResponse = response as? HTTPURLResponse, (200...299).contains(httpResponse.statusCode) else {
            throw NetworkError.invalidResponse
        }

        return String(data: data, encoding: .utf8) ?? "Upload Success"
    }

    // MARK: - 📤 批量文件上传（支持 async/await）

    func uploadFiles(
        to url: String,
        fileURLs: [URL],
        headers: [String: String]? = nil
    ) async throws -> [String] {
        var results: [String] = []
        for fileURL in fileURLs {
            let result = try await uploadFile(to: url, fileURL: fileURL, headers: headers)
            results.append(result)
        }
        return results
    }
}

/// **网络错误类型**
enum NetworkError: Error {
    case invalidURL
    case requestFailed(Error)
    case invalidResponse
    case noData
    case decodingError(Error)
    case fileDownloadFailed(Error)
    case fileCopyFailed(Error)
}

/// **请求方法枚举**
enum HTTPMethod: String {
    case GET, POST, PUT, DELETE
}
