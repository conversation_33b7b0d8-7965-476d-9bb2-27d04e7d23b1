//
//  ProductConfigGeneratorTool.swift
//  BDoggy
//
//  Created by K4 on 2024/12/9.
//

import Foundation

// @main
enum ProductConfigGeneratorTool {
    static func main() {
        // 获取默认输入值（已有的runner产品的groupName）
        let defaultInput = getExistingRunnerGroupNames()
        // 获取Resources目录下所有子文件夹
        let allFolders = getAllResourceFolders()
        
        print("请输入要添加到products中的子文件夹名称，用空格分隔（直接回车将只更新system数组）：")
        print("已有Products: \(defaultInput)")
        print("所有可用Products: \(allFolders)")
        
        // 读取用户输入，如果为空则使用默认值
        let input = readLine() ?? ""
        
        if input.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            // 如果没有输入，则只更新system数组
            ProductConfigGenerator.generateSystemConfig()
        } else {
            // 处理输入的文件夹名称，并更新system和products
            let folderNames = input.split(separator: " ").map(String.init)
            ProductConfigGenerator.generateProductsConfig(for: folderNames)
        }
    }
    
    // 获取Resources目录下所有非国际化子文件夹
    private static func getAllResourceFolders() -> String {
        let mainPath = Bundle.main.resourcePath!
        let mainURL = URL(fileURLWithPath: mainPath)
        let bdoggyURL = mainURL.appendingPathComponent("BDoggy")
        let resourcesURL = bdoggyURL.appendingPathComponent("Resources")
        
        do {
            // 获取Resources目录下的所有子文件夹
            let fileManager = FileManager.default
            let contents = try fileManager.contentsOfDirectory(at: resourcesURL, includingPropertiesForKeys: [.isDirectoryKey], options: [.skipsHiddenFiles])
            
            // 过滤出子文件夹，排除国际化文件夹（以.lproj结尾）
            let subfolders = contents.filter { url in
                let isDirectory = (try? url.resourceValues(forKeys: [.isDirectoryKey]).isDirectory) ?? false
                let isLocalizationFolder = url.lastPathComponent.hasSuffix(".lproj")
                return isDirectory && !isLocalizationFolder
            }
            
            // 提取文件夹名称并拼接
            let folderNames = subfolders.map { $0.lastPathComponent }
            return folderNames.joined(separator: " ")
            
        } catch {
            print("获取Resources子文件夹时出错：\(error.localizedDescription)")
            return ""
        }
    }
    
    // 获取现有的runner产品的groupName
    private static func getExistingRunnerGroupNames() -> String {
        let mainPath = Bundle.main.resourcePath!
        let mainURL = URL(fileURLWithPath: mainPath)
        let bdoggyURL = mainURL.appendingPathComponent("BDoggy")
        let resourcesURL = bdoggyURL.appendingPathComponent("Resources")
        let configPath = resourcesURL.appendingPathComponent("product_config.json")
        
        do {
            // 检查配置文件是否存在
            let fileManager = FileManager.default
            if !fileManager.fileExists(atPath: configPath.path) {
                print("配置文件不存在")
                return ""
            }
            
            // 读取配置文件
            let configData = try Data(contentsOf: configPath)
            let configDict = try JSONSerialization.jsonObject(with: configData, options: []) as? [String: Any]
            
            // 获取products字典
            guard let products = configDict?["products"] as? [String: [String: Any]] else {
                print("无法获取products字典")
                return ""
            }
            
            // 找出productType为"runner"的元素，并获取它们的groupName
            var runnerGroupNames: [String] = []
            for (_, productInfo) in products {
                if let productType = productInfo["productType"] as? String,
                   productType == "runner",
                   let groupName = productInfo["groupName"] as? String
                {
                    runnerGroupNames.append(groupName)
                }
            }
            
            // 将groupName拼接成字符串
            return runnerGroupNames.joined(separator: " ")
            
        } catch {
            print("读取配置文件时出错：\(error.localizedDescription)")
            return ""
        }
    }
}
