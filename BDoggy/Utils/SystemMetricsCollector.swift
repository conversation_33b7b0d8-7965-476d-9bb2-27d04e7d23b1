//
//  SystemMetricsCollector.swift
//  BDoggy
//
//  Created by System Monitor on 2024/12/11.
//

import AppKit
import Cocoa
import Foundation
import IOKit
import IOKit.ps
import IOKit.pwr_mgt

/// 系统指标采集器（兼容现有代码）
/// 为了保持向后兼容性，这个类作为EnhancedSystemInfoCollector的包装器
class SystemMetricsCollector {
    // MARK: - CPU指标

    struct CPUMetrics {
        let usage: Double
        let topProcesses: [ProcessInfoModel]
    }

    static func getCPUMetrics() -> CPUMetrics {
        let usage = EnhancedSystemInfoCollector.getCPUUsage()
        let topProcesses = EnhancedSystemInfoCollector.getTopProcesses()
        return CPUMetrics(usage: usage, topProcesses: topProcesses)
    }

    // MARK: - 内存指标

    struct MemoryMetrics {
        let usagePercent: Double
        let usedGB: Double
        let availableGB: Double
        let swapUsedGB: Double
        let swapTotalGB: Double
    }

    static func getMemoryMetrics() -> MemoryMetrics {
        let memoryInfo = EnhancedSystemInfoCollector.getMemoryInfo()
        return MemoryMetrics(
            usagePercent: memoryInfo.usagePercent,
            usedGB: memoryInfo.usedGB,
            availableGB: memoryInfo.availableGB,
            swapUsedGB: memoryInfo.swapUsedGB,
            swapTotalGB: memoryInfo.swapTotalGB
        )
    }

    // MARK: - 磁盘指标

    struct DiskMetrics {
        let freeGB: Double
        let usagePercent: Double
    }

    static func getDiskMetrics() -> DiskMetrics {
        let diskInfo = EnhancedSystemInfoCollector.getDiskInfo()
        return DiskMetrics(
            freeGB: diskInfo.freeGB,
            usagePercent: diskInfo.usagePercent
        )
    }

    // MARK: - 电池指标

    struct BatteryMetrics {
        let level: Int?
        let health: Double?
        let cycleCount: Int?
        let powerSourceType: String
    }

    static func getBatteryMetrics() -> BatteryMetrics {
        let batteryInfo = EnhancedSystemInfoCollector.getBatteryInfo()
        return BatteryMetrics(
            level: batteryInfo.level,
            health: batteryInfo.health,
            cycleCount: batteryInfo.cycleCount,
            powerSourceType: batteryInfo.powerSourceType
        )
    }

    // MARK: - 温度指标

    struct ThermalMetrics {
        let cpuTemp: Double?
        let gpuTemp: Double?
        let fanSpeed: Int?
    }

    static func getThermalMetrics() -> ThermalMetrics {
        return ThermalMetrics(
            cpuTemp: EnhancedSystemInfoCollector.getCPUTemperature(),
            gpuTemp: EnhancedSystemInfoCollector.getGPUTemperature(),
            fanSpeed: EnhancedSystemInfoCollector.getFanSpeed()
        )
    }

    // MARK: - 活跃应用

    struct ActiveApplication {
        let bundleID: String?
        let name: String?
    }

    static func getActiveApplication() -> ActiveApplication {
        let activeApp = EnhancedSystemInfoCollector.getActiveApplication()
        return ActiveApplication(
            bundleID: activeApp.bundleID,
            name: activeApp.name
        )
    }
}

/// 系统动态指标采集器（需要定时采集的动态数据）
// class SystemMetricsCollector {
//
//
//    /// 获取 Top CPU 进程
//    private static func getTopCPUProcesses(limit: Int) -> [ProcessInfoModel] {
//        let task = Process()
//        task.launchPath = "/bin/ps"
//        task.arguments = ["-eo", "pid,comm,%cpu,rss", "-r"]
//
//        let pipe = Pipe()
//        task.standardOutput = pipe
//
//        do {
//            try task.run()
//            task.waitUntilExit()
//
//            let data = pipe.fileHandleForReading.readDataToEndOfFile()
//            let output = String(data: data, encoding: .utf8) ?? ""
//
//            return parseProcessOutput(output, limit: limit)
//        } catch {
//            Logger.error("获取进程信息失败: \(error)")
//            return []
//        }
//    }
//
//    private static func parseProcessOutput(_ output: String, limit: Int) -> [ProcessInfoModel] {
//        let lines = output.components(separatedBy: .newlines)
//        var processes: [ProcessInfoModel] = []
//
//        for line in lines.dropFirst() { // 跳过标题行
//            let components = line.trimmingCharacters(in: .whitespaces).components(separatedBy: .whitespaces)
//            guard components.count >= 4,
//                  let pid = Int32(components[0]),
//                  let cpuUsage = Double(components[2]),
//                  let memoryKB = Double(components[3]) else { continue }
//
//            let name = components[1]
//            let memoryMB = memoryKB / 1024.0
//
//            processes.append(ProcessInfoModel(pid: pid, name: name, cpuUsage: cpuUsage, memoryUsageMB: memoryMB))
//
//            if processes.count >= limit { break }
//        }
//
//        return processes
//    }
// }
