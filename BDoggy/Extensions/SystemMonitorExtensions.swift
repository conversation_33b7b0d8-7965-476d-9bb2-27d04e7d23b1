//
//  SystemMonitorExtensions.swift
//  BDoggy
//
//  Created by System Monitor on 2024/12/11.
//

import Foundation

// MARK: - Array 安全访问扩展

extension Array {
    subscript(safe index: Index) -> Element? {
        return indices.contains(index) ? self[index] : nil
    }
}

// MARK: - 字符串转时间

extension String {
    func toDate(format: String = "yyyy-MM-dd HH:mm:ss.SSS") -> Date {
        let formatter = DateFormatter()
        formatter.dateFormat = format
        return formatter.date(from: self) ?? Date()
    }
}

// MARK: - Date 格式化扩展

extension Date {
    func formatted(style: DateFormatter.Style = .medium) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = style
        formatter.timeStyle = style
        return formatter.string(from: self)
    }

    func timeAgo() -> String {
        let now = Date()
        let timeInterval = now.timeIntervalSince(self)

        if timeInterval < 60 {
            return "刚刚"
        } else if timeInterval < 3600 {
            let minutes = Int(timeInterval / 60)
            return "\(minutes)分钟前"
        } else if timeInterval < 86400 {
            let hours = Int(timeInterval / 3600)
            return "\(hours)小时前"
        } else {
            let days = Int(timeInterval / 86400)
            return "\(days)天前"
        }
    }
}

// MARK: - Double 格式化扩展

extension Double {
    func rounded(toPlaces places: Int) -> Double {
        let divisor = pow(10.0, Double(places))
        return (self * divisor).rounded() / divisor
    }

    func formatAsPercentage() -> String {
        return String(format: "%.1f%%", self)
    }

    func formatAsMemory() -> String {
        if self >= 1024 {
            return String(format: "%.1f TB", self / 1024)
        } else {
            return String(format: "%.1f GB", self)
        }
    }

    func formatAsTemperature() -> String {
        return String(format: "%.1f°C", self)
    }
}

// MARK: - TimeInterval 格式化扩展

extension TimeInterval {
    func formatAsDuration() -> String {
        let hours = Int(self) / 3600
        let minutes = (Int(self) % 3600) / 60
        let seconds = Int(self) % 60

        if hours > 0 {
            return String(format: "%d:%02d:%02d", hours, minutes, seconds)
        } else {
            return String(format: "%02d:%02d", minutes, seconds)
        }
    }
}

// MARK: - UserDefaults 扩展

extension UserDefaults {
    func setObject<T: Codable>(_ object: T, forKey key: String) {
        if let data = try? JSONEncoder().encode(object) {
            set(data, forKey: key)
        }
    }

    func getObject<T: Codable>(_ type: T.Type, forKey key: String) -> T? {
        guard let data = data(forKey: key) else { return nil }
        return try? JSONDecoder().decode(type, from: data)
    }
}

// MARK: - Logger 扩展

extension Logger {
    static func systemMonitor(_ message: String, level: LogLevel = .info) {
        let categoryMessage = "[SystemMonitor] \(message)"
        switch level {
        case .debug:
            Logger.debug(categoryMessage)
        case .info:
            Logger.info(categoryMessage)
        case .warning:
            Logger.warning(categoryMessage)
        case .error:
            Logger.error(categoryMessage)
        case .success:
            Logger.success(categoryMessage)
        }
    }
}

// MARK: - 系统监控相关的常量

enum SystemMonitorConstants {
    static let databaseName = "system_monitor.db"
    static let configKey = "SystemMonitorConfig"
    static let enabledKey = "SystemMonitorEnabled"

    // 数据保留策略
    static let defaultRetentionDays = 30
    static let maxRetentionDays = 365
    static let minRetentionDays = 1

    // 采集间隔
    static let minCollectionInterval: TimeInterval = 60 // 1分钟
    static let maxCollectionInterval: TimeInterval = 3600 // 1小时

    // 性能阈值
    static let highCPUThreshold: Double = 80.0
    static let highMemoryThreshold: Double = 85.0
    static let lowDiskSpaceThreshold: Double = 90.0
    static let lowBatteryThreshold: Int = 20

    // UI 相关
    static let chartMaxDataPoints = 100
    static let refreshInterval: TimeInterval = 5.0
}

// MARK: - 错误处理扩展

extension SystemMonitorError {
    var userFriendlyDescription: String {
        switch self {
        case .databaseNotInitialized:
            return "数据库初始化失败，请重启应用"
        case .dataCollectionFailed(let reason):
            return "数据采集失败：\(reason)"
        case .permissionDenied(let permission):
            return "缺少必要权限：\(permission)"
        case .invalidConfiguration:
            return "配置无效，请检查设置"
        }
    }

    var recoverySuggestion: String {
        switch self {
        case .databaseNotInitialized:
            return "尝试重启应用或清除应用数据"
        case .dataCollectionFailed:
            return "检查系统状态或重启监控功能"
        case .permissionDenied:
            return "在系统偏好设置中授予相应权限"
        case .invalidConfiguration:
            return "重置监控配置到默认值"
        }
    }
}

// MARK: - 通知名称扩展

extension Notification.Name {
    static let systemMonitorDataUpdated = Notification.Name("systemMonitorDataUpdated")
    static let systemMonitorConfigChanged = Notification.Name("systemMonitorConfigChanged")
    static let systemMonitorPermissionChanged = Notification.Name("systemMonitorPermissionChanged")
}

// MARK: - 颜色扩展

#if canImport(SwiftUI)
import SwiftUI

extension Color {
    static let systemMonitorPrimary = Color.blue
    static let systemMonitorSecondary = Color.orange
    static let systemMonitorSuccess = Color.green
    static let systemMonitorWarning = Color.yellow
    static let systemMonitorDanger = Color.red

    static func cpuUsageColor(for usage: Double) -> Color {
        switch usage {
        case 0..<50: return .green
        case 50..<80: return .yellow
        default: return .red
        }
    }

    static func memoryUsageColor(for usage: Double) -> Color {
        switch usage {
        case 0..<60: return .green
        case 60..<85: return .yellow
        default: return .red
        }
    }

    static func batteryLevelColor(for level: Int) -> Color {
        switch level {
        case 0..<20: return .red
        case 20..<50: return .yellow
        default: return .green
        }
    }
}
#endif

// MARK: - 数据验证扩展

extension SystemMetrics {
    var isValid: Bool {
        return cpuUsagePercent >= 0 && cpuUsagePercent <= 100 &&
            memoryUsagePercent >= 0 && memoryUsagePercent <= 100 &&
            diskUsagePercent >= 0 && diskUsagePercent <= 100
    }
}

extension MonitoringConfig {
    var isValid: Bool {
        return dataRetentionDays >= SystemMonitorConstants.minRetentionDays &&
            dataRetentionDays <= SystemMonitorConstants.maxRetentionDays &&
            collectionInterval.timeInterval >= SystemMonitorConstants.minCollectionInterval &&
            collectionInterval.timeInterval <= SystemMonitorConstants.maxCollectionInterval
    }

    mutating func sanitize() {
        if dataRetentionDays < SystemMonitorConstants.minRetentionDays {
            dataRetentionDays = SystemMonitorConstants.minRetentionDays
        } else if dataRetentionDays > SystemMonitorConstants.maxRetentionDays {
            dataRetentionDays = SystemMonitorConstants.maxRetentionDays
        }
    }
}
