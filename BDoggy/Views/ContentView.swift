//
//  ContentView.swift
//  BDoggy
//
//  Created by K4 on 2024/12/9.
//

import SwiftUI

struct ContentView: View {
    private var gifURL: URL? {
        Bundle.main.url(forResource: "logo", withExtension: "gif")
    }

    var body: some View {
        VStack {
            if let url = gifURL {
                GIFImage(url: url, loopCount: 0) // loopCount: 0 表示无限循环
                    .frame(width: 400, height: 400) // 可以根据需要调整尺寸
            }

            Text("Enjoy Your Doggy".localized)
                .font(.custom("Marker Felt", size: 32))
                .fontWeight(.bold)
                .foregroundStyle(LinearGradient(
                    colors: [.purple, .blue],
                    startPoint: .leading,
                    endPoint: .trailing
                ))
                .shadow(color: .pink.opacity(0.3), radius: 3, x: 1, y: 1)
                .padding(.top, 5)
                .padding(.bottom, 20)
        }.background(Color.white)
    }
}
