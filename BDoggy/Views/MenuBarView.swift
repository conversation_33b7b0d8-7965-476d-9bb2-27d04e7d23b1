//
//  MenuBarView.swift
//  BDoggy
//
//  Created by K4 on 2024/12/9.
//

import Foundation
import SwiftUI

struct MenuBarView: View {
    var mainViewModel: SystemUsageModel
    @State private var currentPage: NavigationTag = .Main
    @StateObject private var runnersViewModel = RunnersViewModel()

    var body: some View {
        VStack {
            switch currentPage {
            case .Main:
                MainView(model: mainViewModel, currentPage: $currentPage)
            case .Runner:
                RunnerView(viewModel: runnersViewModel, currentPage: $currentPage, onBack: { currentPage = .Main })
            case .More:
                MoreView(currentPage: $currentPage, onBack: { currentPage = .Main })
            case .About:
                AboutView(currentPage: $currentPage, onBack: { currentPage = .More })
            }
        }
        .animation(.easeInOut, value: currentPage)
        .fixedSize()
    }
}

enum NavigationTag: Hashable {
    case Main
    case About
    case Runner
    case More
}
