//
//  MonitoringSettingsView.swift
//  BDoggy
//
//  Created by K4 on 2025/1/11.
//

import SwiftUI

/// 现代化监控设置视图
struct MonitoringSettingsView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme
    @State private var scheduler = SystemMonitorScheduler.shared
    @State private var config: MonitorConfig
    @State private var showingResetAlert = false
    @State private var showingClearAlert = false
    @State private var selectedSection: SettingsSection = .basic
    @State private var animateCards = false
    @State private var scrollPosition: CGFloat = 0

    // 用于滚动到指定区域的 ScrollViewReader
    @State private var scrollReader: ScrollViewProxy?

    init() {
        _config = State(initialValue: SystemMonitorScheduler.shared.config)
    }

    var body: some View {
        VStack(spacing: 0) {
            // 顶部标签栏
            sectionTabBar

            // 主内容滚动区域
            mainScrollView
        }
        .navigationTitle("监控设置")
        .toolbar {
            ToolbarItem(placement: .cancellationAction) {
                ModernButton(
                    title: "取消",
                    icon: "xmark",
                    style: .secondary
                ) {
                    dismiss()
                }
            }

            ToolbarItem(placement: .confirmationAction) {
                ModernButton(
                    title: "保存",
                    icon: "checkmark",
                    style: .primary
                ) {
                    saveSettings()
                    dismiss()
                }
            }
        }
        .onAppear {
            withAnimation(.easeInOut(duration: 0.6).delay(0.1)) {
                animateCards = true
            }
        }
        .alert("重置设置", isPresented: $showingResetAlert) {
            Button("重置", role: .destructive) { resetSettings() }
            Button("取消", role: .cancel) {}
        } message: {
            Text("这将重置所有监控设置到默认值，此操作不可撤销。")
        }
        .alert("清除数据", isPresented: $showingClearAlert) {
            Button("清除", role: .destructive) { scheduler.clearAllData() }
            Button("取消", role: .cancel) {}
        } message: {
            Text("这将删除所有监控数据，此操作不可撤销。")
        }
    }

    // MARK: - 顶部标签栏

    private var sectionTabBar: some View {
        VStack(spacing: 0) {
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(SettingsSection.allCases, id: \.self) { section in
                        SectionTabButton(
                            section: section,
                            isSelected: selectedSection == section
                        ) {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                selectedSection = section
                            }
                            scrollToSection(section)
                        }
                    }
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
            }

            // 进度指示器
            HStack {
                ForEach(SettingsSection.allCases, id: \.self) { section in
                    Rectangle()
                        .frame(height: 2)
                        .foregroundColor(selectedSection == section ? section.color : .clear)
                        .animation(.easeInOut(duration: 0.3), value: selectedSection)
                }
            }
            .padding(.horizontal, 20)
        }
        .background(.regularMaterial)
        .overlay(
            Rectangle()
                .frame(height: 1)
                .foregroundColor(.accentColor)
                .opacity(0.3),
            alignment: .bottom
        )
    }

    // MARK: - 主滚动视图

    private var mainScrollView: some View {
        ScrollViewReader { proxy in
            ScrollView {
                LazyVStack(spacing: 32) {
                    // 基本设置区域
                    SectionContainer(section: .basic, isVisible: selectedSection == .basic) {
                        basicSettingsSection
                    }
                    .id(SettingsSection.basic)

                    // 监控功能区域
                    SectionContainer(section: .monitoring, isVisible: selectedSection == .monitoring) {
                        monitoringFeaturesSection
                    }
                    .id(SettingsSection.monitoring)

                    // 日志设置区域
                    SectionContainer(section: .logging, isVisible: selectedSection == .logging) {
                        loggingSettingsSection
                    }
                    .id(SettingsSection.logging)

                    // 数据管理区域
                    SectionContainer(section: .data, isVisible: selectedSection == .data) {
                        dataManagementSection
                    }
                    .id(SettingsSection.data)

                    // 高级设置区域
                    SectionContainer(section: .advanced, isVisible: selectedSection == .advanced) {
                        advancedSettingsSection
                    }
                    .id(SettingsSection.advanced)

                    // 底部间距
                    Spacer()
                        .frame(height: 20)
                }
                .padding(.horizontal, 24)
                .padding(.top, 20)
            }
            .background(
                LinearGradient(
                    colors: [
                        Color(.controlBackgroundColor).opacity(0.3),
                        Color(.controlBackgroundColor).opacity(0.1)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .coordinateSpace(name: "scroll")
            .onAppear {
                scrollReader = proxy
            }
        }
    }

    // MARK: - 滚动控制方法

    private func scrollToSection(_ section: SettingsSection) {
        withAnimation(.easeInOut(duration: 0.5)) {
            scrollReader?.scrollTo(section, anchor: .top)
        }
    }

    // MARK: - 基本设置区域

    private var basicSettingsSection: some View {
        VStack(spacing: 20) {
            ModernSectionHeader(
                title: "基本设置",
                subtitle: "配置监控的基础参数",
                icon: "slider.horizontal.3",
                color: .blue
            )

            ModernCard {
                VStack(spacing: 20) {
                    // 采集间隔设置
                    ModernSettingRow(
                        title: "采集间隔",
                        subtitle: "系统数据采集的时间间隔",
                        icon: "clock.fill",
                        color: .blue
                    ) {
                        Picker("采集间隔", selection: $config.collectionInterval) {
                            ForEach(MonitorConfig.CollectionInterval.allCases, id: \.rawValue) { interval in
                                HStack {
                                    Text(interval.displayName)
                                    Spacer()
                                    Text("(\(Utils.formatInterval(interval.rawValue)))")
                                        .foregroundColor(.secondary)
                                        .font(.caption)
                                }
                                .tag(interval.rawValue)
                            }
                        }
                        .pickerStyle(.menu)
                        .frame(width:200)
                    }

                    Divider()

                    // 数据保留天数设置
                    ModernSettingRow(
                        title: "数据保留天数",
                        subtitle: "历史数据的保存时长",
                        icon: "calendar",
                        color: .green
                    ) {
                        VStack(alignment: .trailing, spacing: 4) {
                            Stepper(
                                value: $config.maxDataRetentionDays,
                                in: 7 ... 365,
                                step: 1
                            ) {
                                Text("\(config.maxDataRetentionDays) 天")
                                    .font(.headline)
                                    .fontWeight(.semibold)
                            }

                            Text("约 \(estimateDataSize(days: config.maxDataRetentionDays))")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
            }
        }
        .opacity(animateCards ? 1 : 0)
        .offset(y: animateCards ? 0 : 20)
        .animation(.easeInOut(duration: 0.5), value: animateCards)
    }

    // MARK: - 监控功能区域

    private var monitoringFeaturesSection: some View {
        VStack(spacing: 20) {
            ModernSectionHeader(
                title: "监控功能",
                subtitle: "选择要启用的监控模块",
                icon: "eye.fill",
                color: .purple
            )

            ModernCard {
                VStack(spacing: 16) {
                    ModernToggleRow(
                        title: "电池监控",
                        description: "监控电池电量、健康状态和充电信息",
                        icon: "battery.100",
                        color: .green,
                        isOn: $config.enableBatteryMonitoring
                    )

                    Divider()

                    ModernToggleRow(
                        title: "温度监控",
                        description: "监控CPU和系统温度变化",
                        icon: "thermometer",
                        color: .red,
                        isOn: $config.enableTemperatureMonitoring
                    )

                    Divider()

                    ModernToggleRow(
                        title: "应用使用跟踪",
                        description: "跟踪应用使用时间和资源占用情况",
                        icon: "app.badge",
                        color: .blue,
                        isOn: $config.enableAppUsageTracking
                    )

                    Divider()

                    ModernToggleRow(
                        title: "详细进程信息",
                        description: "收集详细的进程和系统运行信息",
                        icon: "list.bullet.rectangle",
                        color: .purple,
                        isOn: $config.enableDetailedProcessInfo
                    )
                }
            }

            // 监控状态概览卡片
            MonitoringStatusCard(config: config)
        }
        .opacity(animateCards ? 1 : 0)
        .offset(y: animateCards ? 0 : 20)
        .animation(.easeInOut(duration: 0.5).delay(0.1), value: animateCards)
    }

    // MARK: - 日志设置区域

    private var loggingSettingsSection: some View {
        VStack(spacing: 20) {
            ModernSectionHeader(
                title: "日志设置",
                subtitle: "配置应用日志记录选项",
                icon: "doc.text.fill",
                color: .orange
            )

            ModernCard {
                VStack(spacing: 20) {
                    // 日志级别设置
                    ModernSettingRow(
                        title: "日志级别",
                        subtitle: "设置记录日志的详细程度",
                        icon: "slider.horizontal.3",
                        color: .orange
                    ) {
                        Picker("日志级别", selection: Binding(
                            get: { Logger.currentLevel },
                            set: { Logger.setLogLevel($0) }
                        )) {
                            ForEach(LogLevel.allCases, id: \.rawValue) { level in
                                HStack {
                                    Circle()
                                        .fill(level.color)
                                        .frame(width: 8, height: 8)
                                    Text(level.displayName)
                                }
                                .tag(level)
                            }
                        }
                        .pickerStyle(.menu)
                        .frame(width: 200)
                    }

                    Divider()

                    // 文件日志开关
                    ModernToggleRow(
                        title: "文件日志",
                        description: "将日志保存到本地文件",
                        icon: "doc.circle.fill",
                        color: .blue,
                        isOn: Binding(
                            get: { Logger.enableFileLogging },
                            set: { Logger.setFileLogging(enabled: $0) }
                        )
                    )

                    Divider()

                    // 控制台日志开关
                    ModernToggleRow(
                        title: "控制台日志",
                        description: "在开发控制台输出日志信息",
                        icon: "terminal.fill",
                        color: .green,
                        isOn: Binding(
                            get: { Logger.enableConsoleLogging },
                            set: { Logger.setConsoleLogging(enabled: $0) }
                        )
                    )
                }
            }

            // 日志统计卡片
            LoggingStatsCard()
        }
        .opacity(animateCards ? 1 : 0)
        .offset(y: animateCards ? 0 : 20)
        .animation(.easeInOut(duration: 0.5).delay(0.2), value: animateCards)
    }

    // MARK: - 数据管理区域

    private var dataManagementSection: some View {
        VStack(spacing: 20) {
            ModernSectionHeader(
                title: "数据管理",
                subtitle: "管理存储的监控数据和日志",
                icon: "folder.fill",
                color: .indigo
            )

            // 存储统计卡片
            ModernCard {
                VStack(spacing: 16) {
                    HStack {
                        Text("存储统计")
                            .font(.headline)
                            .fontWeight(.semibold)
                        Spacer()
                    }

                    LazyVGrid(columns: [
                        GridItem(.flexible()),
                        GridItem(.flexible())
                    ], spacing: 12) {
                        ModernDataSizeCard(
                            title: "数据库",
                            size: scheduler.databaseSize,
                            icon: "internaldrive.fill",
                            color: .blue
                        )

                        ModernDataSizeCard(
                            title: "日志文件",
                            size: Logger.getLogFileSize(),
                            icon: "doc.fill",
                            color: .green
                        )
                    }
                }
            }

            // 数据操作卡片
            ModernCard {
                VStack(spacing: 16) {
                    HStack {
                        Text("数据操作")
                            .font(.headline)
                            .fontWeight(.semibold)
                        Spacer()
                    }

                    VStack(spacing: 12) {
                        ModernActionButton(
                            title: "清理旧数据",
                            subtitle: "删除超过保留期限的数据",
                            icon: "trash.circle.fill",
                            color: .orange,
                            style: .secondary
                        ) {
                            scheduler.cleanupOldData()
                        }

                        ModernActionButton(
                            title: "清除所有数据",
                            subtitle: "删除所有监控数据（不可恢复）",
                            icon: "trash.fill",
                            color: .red,
                            style: .destructive
                        ) {
                            showingClearAlert = true
                        }
                    }
                }
            }

            // 日志统计展开视图
            DisclosureGroup {
                LogStatsView()
                    .padding(.top, 8)
            } label: {
                ModernDisclosureLabel(
                    title: "详细日志统计",
                    icon: "chart.bar.fill",
                    color: .blue
                )
            }
            .padding()
            .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))
        }
        .opacity(animateCards ? 1 : 0)
        .offset(y: animateCards ? 0 : 20)
        .animation(.easeInOut(duration: 0.5).delay(0.3), value: animateCards)
    }

    // MARK: - 高级设置区域

    private var advancedSettingsSection: some View {
        VStack(spacing: 20) {
            ModernSectionHeader(
                title: "高级设置",
                subtitle: "系统路径和导出选项",
                icon: "gearshape.2.fill",
                color: .gray
            )

            // 路径信息卡片
            ModernCard {
                VStack(spacing: 16) {
                    HStack {
                        Text("文件路径")
                            .font(.headline)
                            .fontWeight(.semibold)
                        Spacer()
                    }

                    VStack(spacing: 12) {
                        ModernPathRow(
                            title: "数据库路径",
                            path: scheduler.database.getDatabasePath(),
                            icon: "internaldrive.fill",
                            color: .blue
                        )

                        Divider()

                        ModernPathRow(
                            title: "日志文件路径",
                            path: Logger.getLogFilePath(),
                            icon: "doc.fill",
                            color: .green
                        )
                    }
                }
            }

            // 导出选项卡片
            ModernCard {
                VStack(spacing: 16) {
                    HStack {
                        Text("导出选项")
                            .font(.headline)
                            .fontWeight(.semibold)
                        Spacer()
                    }

                    Menu {
                        Button("导出完整日志") { exportLogFile() }
                        Button("导出最近7天") { exportRecentLogs(days: 7) }
                        Button("导出错误和警告") { exportErrorsAndWarnings() }
                        Button("仅导出错误") { exportErrorsOnly() }
                    } label: {
                        ModernMenuButton(
                            title: "导出日志文件",
                            icon: "square.and.arrow.up.fill",
                            color: .blue
                        )
                    }
                }
            }

            // 重置设置卡片
            ModernCard {
                VStack(spacing: 16) {
                    HStack {
                        Text("系统重置")
                            .font(.headline)
                            .fontWeight(.semibold)
                        Spacer()
                    }

                    ModernActionButton(
                        title: "重置所有设置",
                        subtitle: "恢复到默认配置（不可恢复）",
                        icon: "arrow.clockwise.circle.fill",
                        color: .red,
                        style: .destructive
                    ) {
                        showingResetAlert = true
                    }
                }
            }
        }
        .opacity(animateCards ? 1 : 0)
        .offset(y: animateCards ? 0 : 20)
        .animation(.easeInOut(duration: 0.5).delay(0.4), value: animateCards)
    }

    // MARK: - 私有方法

    private func saveSettings() {
        withAnimation(.easeInOut(duration: 0.3)) {
            scheduler.config = config
        }
        Logger.info("监控设置已保存")
    }

    private func resetSettings() {
        withAnimation(.easeInOut(duration: 0.3)) {
            config = MonitorConfig()
        }
        Logger.info("监控设置已重置")
    }

    private func estimateDataSize(days: Int) -> String {
        let bytesPerDay: Int64 = 1024 * 1024 // 约1MB每天
        let totalBytes = bytesPerDay * Int64(days)
        return Utils.formatFileSize(totalBytes)
    }
}

private func exportLogFile() {
    let panel = NSSavePanel()
    panel.allowedContentTypes = [.log, .plainText]
    panel.nameFieldStringValue = Logger.getSuggestedExportFileName()

    panel.begin { response in
        if response == .OK, let url = panel.url {
            if Logger.exportLogFile(to: url) {
                Logger.info("完整日志文件已导出到: \(url.path)")
            } else {
                Logger.error("日志文件导出失败")
            }
        }
    }
}

private func exportRecentLogs(days: Int) {
    let panel = NSSavePanel()
    panel.allowedContentTypes = [.log, .plainText]
    panel.nameFieldStringValue = Logger.getSuggestedExportFileName(prefix: "recent_\(days)days")

    panel.begin { response in
        if response == .OK, let url = panel.url {
            if Logger.exportRecentLogs(to: url, days: days) {
                Logger.info("最近\(days)天日志已导出到: \(url.path)")
            } else {
                Logger.error("最近日志导出失败")
            }
        }
    }
}

private func exportErrorsAndWarnings() {
    let panel = NSSavePanel()
    panel.allowedContentTypes = [.log, .plainText]
    panel.nameFieldStringValue = Logger.getSuggestedExportFileName(prefix: "errors_warnings")

    panel.begin { response in
        if response == .OK, let url = panel.url {
            if Logger.exportErrorsAndWarnings(to: url) {
                Logger.info("错误和警告日志已导出到: \(url.path)")
            } else {
                Logger.error("错误和警告日志导出失败")
            }
        }
    }
}

private func exportErrorsOnly() {
    let panel = NSSavePanel()
    panel.allowedContentTypes = [.log, .plainText]
    panel.nameFieldStringValue = Logger.getSuggestedExportFileName(prefix: "errors_only")

    panel.begin { response in
        if response == .OK, let url = panel.url {
            if Logger.exportErrorsOnly(to: url) {
                Logger.info("错误日志已导出到: \(url.path)")
            } else {
                Logger.error("错误日志导出失败")
            }
        }
    }
}

// MARK: - 设置区域枚举

enum SettingsSection: String, CaseIterable {
    case basic
    case monitoring
    case logging
    case data
    case advanced

    var title: String {
        switch self {
        case .basic: return "基本设置"
        case .monitoring: return "监控功能"
        case .logging: return "日志设置"
        case .data: return "数据管理"
        case .advanced: return "高级设置"
        }
    }

    var icon: String {
        switch self {
        case .basic: return "slider.horizontal.3"
        case .monitoring: return "eye.fill"
        case .logging: return "doc.text.fill"
        case .data: return "folder.fill"
        case .advanced: return "gearshape.2.fill"
        }
    }

    var color: Color {
        switch self {
        case .basic: return .blue
        case .monitoring: return .purple
        case .logging: return .orange
        case .data: return .indigo
        case .advanced: return .gray
        }
    }
}

// MARK: - 区域容器组件

struct SectionContainer<Content: View>: View {
    let section: SettingsSection
    let isVisible: Bool
    let content: Content

    init(section: SettingsSection, isVisible: Bool, @ViewBuilder content: () -> Content) {
        self.section = section
        self.isVisible = isVisible
        self.content = content()
    }

    var body: some View {
        VStack(spacing: 0) {
            // 区域分隔线（除了第一个区域）
            if section != .basic {
                HStack {
                    Rectangle()
                        .frame(height: 1)
                        .foregroundColor(Color(nsColor: .separatorColor))
                        .opacity(0.2)
                }
                .padding(.bottom, 32)
            }

            content
        }
    }
}

// MARK: - 标签栏组件

struct SectionTabButton: View {
    let section: SettingsSection
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            // 视觉内容容器
            VStack(spacing: 6) {
                // 图标
                Image(systemName: section.icon)
                    .font(.title3)
                    .foregroundStyle(isSelected ? LinearGradient(colors: [section.color], startPoint: .top, endPoint: .bottom) : LinearGradient(colors: [.secondary], startPoint: .top, endPoint: .bottom))
                    .frame(width: 24, height: 24)

                // 标题
                Text(section.title)
                    .font(.caption)
                    .fontWeight(isSelected ? .semibold : .medium)
                    .foregroundColor(isSelected ? section.color : .secondary)
                    .lineLimit(1)
                    .minimumScaleFactor(0.8)
            }
            .frame(minWidth: 60)
            .padding(.horizontal, 12)
            .padding(.vertical, 10)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(isSelected ? section.color.opacity(0.12) : Color.clear)
            )
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(isSelected ? section.color.opacity(0.25) : Color.clear, lineWidth: 1.5)
            )
            .scaleEffect(isSelected ? 1.0 : 0.95)
            .animation(.spring(response: 0.3, dampingFraction: 0.7, blendDuration: 0.2), value: isSelected)
        }
        .buttonStyle(.plain)
        // 扩大点击热区到符合 Apple HIG 的 44x44 点最小要求
        // 使用 padding 在视觉内容外增加透明的可点击区域
        .padding(.horizontal, 8) // 左右各增加 8 点
        .padding(.vertical, 6) // 上下各增加 6 点
        .contentShape(Rectangle()) // 确保整个扩展区域都可点击
    }
}

// MARK: - 现代化UI组件

// 现代化卡片容器
struct ModernCard<Content: View>: View {
    let content: Content

    init(@ViewBuilder content: () -> Content) {
        self.content = content()
    }

    var body: some View {
        content
            .padding(20)
            .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 16))
            .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 4)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(.quaternary, lineWidth: 1)
            )
    }
}

// 现代化区域标题
struct ModernSectionHeader: View {
    let title: String
    let subtitle: String
    let icon: String
    let color: Color

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundStyle(color.gradient)
                    .frame(width: 32, height: 32)
                    .background(color.opacity(0.1), in: Circle())

                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.title2)
                        .fontWeight(.bold)

                    Text(subtitle)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }

                Spacer()
            }
        }
    }
}

// 现代化设置行
struct ModernSettingRow<Content: View>: View {
    let title: String
    let subtitle: String
    let icon: String
    let color: Color
    let content: Content

    init(
        title: String,
        subtitle: String,
        icon: String,
        color: Color,
        @ViewBuilder content: () -> Content
    ) {
        self.title = title
        self.subtitle = subtitle
        self.icon = icon
        self.color = color
        self.content = content()
    }

    var body: some View {
        HStack(spacing: 16) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundStyle(color.gradient)
                .frame(width: 24, height: 24)

            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)

                Text(subtitle)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            content
        }
        .padding(.vertical, 4)
    }
}

// 现代化开关行
struct ModernToggleRow: View {
    let title: String
    let description: String
    let icon: String
    let color: Color
    @Binding var isOn: Bool

    var body: some View {
        HStack(spacing: 16) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundStyle(color.gradient)
                .frame(width: 24, height: 24)

            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)

                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }

            Spacer()

            Toggle("", isOn: $isOn)
                .labelsHidden()
                .scaleEffect(0.9)
        }
        .padding(.vertical, 4)
        .animation(.easeInOut(duration: 0.2), value: isOn)
    }
}

// 现代化数据大小卡片
struct ModernDataSizeCard: View {
    let title: String
    let size: Int64
    let icon: String
    let color: Color

    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundStyle(color.gradient)

            Text(Utils.formatFileSize(size))
                .font(.headline)
                .fontWeight(.bold)
                .lineLimit(1)
                .minimumScaleFactor(0.8)

            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(color.opacity(0.1), in: RoundedRectangle(cornerRadius: 12))
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(color.opacity(0.3), lineWidth: 1)
        )
    }
}

// 现代化按钮
struct ModernButton: View {
    let title: String
    let icon: String
    let style: ButtonStyle
    let action: () -> Void

    enum ButtonStyle {
        case primary, secondary, destructive

        var backgroundColor: Color {
            switch self {
            case .primary: return .accentColor
            case .secondary: return .secondary.opacity(0.1)
            case .destructive: return .red
            }
        }

        var foregroundColor: Color {
            switch self {
            case .primary: return .white
            case .secondary: return .primary
            case .destructive: return .white
            }
        }
    }

    var body: some View {
        Button(action: action) {
            HStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.caption)
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(style.backgroundColor, in: RoundedRectangle(cornerRadius: 8))
            .foregroundColor(style.foregroundColor)
        }
        .buttonStyle(.plain)
    }
}

// 现代化操作按钮
struct ModernActionButton: View {
    let title: String
    let subtitle: String
    let icon: String
    let color: Color
    let style: ActionStyle
    let action: () -> Void

    enum ActionStyle {
        case primary, secondary, destructive

        var backgroundColor: Color {
            switch self {
            case .primary: return .accentColor.opacity(0.1)
            case .secondary: return .secondary.opacity(0.1)
            case .destructive: return .red.opacity(0.1)
            }
        }

        var borderColor: Color {
            switch self {
            case .primary: return .accentColor.opacity(0.3)
            case .secondary: return .secondary.opacity(0.3)
            case .destructive: return .red.opacity(0.3)
            }
        }
    }

    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundStyle(color.gradient)
                    .frame(width: 24, height: 24)

                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)

                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding()
            .background(style.backgroundColor, in: RoundedRectangle(cornerRadius: 12))
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(style.borderColor, lineWidth: 1)
            )
        }
        .buttonStyle(.plain)
    }
}

// 现代化路径行
struct ModernPathRow: View {
    let title: String
    let path: String
    let icon: String
    let color: Color

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundStyle(color.gradient)
                    .frame(width: 24, height: 24)

                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()
            }

            Text(path)
                .font(.caption)
                .foregroundColor(.secondary)
                .textSelection(.enabled)
                .lineLimit(3)
                .truncationMode(.middle)
                .padding(.horizontal, 36)
        }
        .padding(.vertical, 4)
    }
}

// 现代化菜单按钮
struct ModernMenuButton: View {
    let title: String
    let icon: String
    let color: Color

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundStyle(color.gradient)
                .frame(width: 24, height: 24)

            Text(title)
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)

            Spacer()

            Image(systemName: "chevron.down")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(color.opacity(0.1), in: RoundedRectangle(cornerRadius: 12))
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(color.opacity(0.3), lineWidth: 1)
        )
    }
}

// 现代化展开标签
struct ModernDisclosureLabel: View {
    let title: String
    let icon: String
    let color: Color

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundStyle(color.gradient)
                .frame(width: 24, height: 24)

            Text(title)
                .font(.headline)
                .fontWeight(.semibold)

            Spacer()
        }
    }
}

// 监控状态概览卡片
struct MonitoringStatusCard: View {
    let config: MonitorConfig

    private var enabledFeatures: [String] {
        var features: [String] = []
        if config.enableBatteryMonitoring { features.append("电池") }
        if config.enableTemperatureMonitoring { features.append("温度") }
        if config.enableAppUsageTracking { features.append("应用") }
        if config.enableDetailedProcessInfo { features.append("进程") }
        return features
    }

    var body: some View {
        ModernCard {
            VStack(spacing: 12) {
                HStack {
                    Text("监控状态概览")
                        .font(.headline)
                        .fontWeight(.semibold)
                    Spacer()
                }

                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("已启用功能")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Text("\(enabledFeatures.count)/4")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.green)
                    }

                    Spacer()

                    VStack(alignment: .trailing, spacing: 4) {
                        Text("功能列表")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Text(enabledFeatures.isEmpty ? "无" : enabledFeatures.joined(separator: " • "))
                            .font(.caption)
                            .foregroundColor(.primary)
                            .multilineTextAlignment(.trailing)
                    }
                }

                ProgressView(value: Double(enabledFeatures.count), total: 4)
                    .progressViewStyle(LinearProgressViewStyle(tint: .green))
                    .scaleEffect(y: 0.8)
            }
        }
    }
}

// 日志统计卡片
struct LoggingStatsCard: View {
    @State private var logFileSize: Int64 = 0
    @State private var logLevel: LogLevel = .info

    var body: some View {
        ModernCard {
            VStack(spacing: 12) {
                HStack {
                    Text("日志状态")
                        .font(.headline)
                        .fontWeight(.semibold)
                    Spacer()
                }

                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("文件大小")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Text(Utils.formatFileSize(logFileSize))
                            .font(.subheadline)
                            .fontWeight(.semibold)
                    }

                    Spacer()

                    VStack(alignment: .trailing, spacing: 4) {
                        Text("当前级别")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        HStack(spacing: 4) {
                            Circle()
                                .fill(logLevel.color)
                                .frame(width: 8, height: 8)
                            Text(logLevel.displayName)
                                .font(.subheadline)
                                .fontWeight(.semibold)
                        }
                    }
                }
            }
        }
        .onAppear {
            logFileSize = Logger.getLogFileSize()
            logLevel = Logger.currentLevel
        }
    }
}

// MARK: - LogLevel 扩展

extension LogLevel {
    var color: Color {
        switch self {
        case .debug: return .gray
        case .info: return .blue
        case .warning: return .orange
        case .error: return .red
        case .success: return .green
        }
    }
}

// MARK: - 数据管理视图

struct DataManagementView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var scheduler = SystemMonitorScheduler.shared
    @State private var showingClearAlert = false
    @State private var showingExportSheet = false
    @State private var recentMetrics: [[String: Any]] = []
    @State private var appUsageSummary: [[String: Any]] = []

    var body: some View {
        VStack(spacing: 20) {
            // 数据统计
            dataStatsSection

            // 最近数据预览
            recentDataSection

            // 数据操作
            dataActionsSection

            Spacer()
        }
        .padding()
        .navigationTitle("数据管理")
        .toolbar {
            ToolbarItem(placement: .cancellationAction) {
                Button("关闭") {
                    dismiss()
                }
            }
        }
        .onAppear {
            loadData()
        }
        .alert("确认清除", isPresented: $showingClearAlert) {
            Button("清除", role: .destructive) {
                scheduler.clearAllData()
                loadData()
            }
            Button("取消", role: .cancel) {}
        } message: {
            Text("这将删除所有监控数据，此操作不可撤销。")
        }
    }

    private var dataStatsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("数据统计")
                .font(.headline)

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                EnhancedStatCard(
                    title: "总数据点",
                    value: "\(scheduler.totalDataPoints)",
                    icon: "chart.bar",
                    color: .blue
                )

                EnhancedStatCard(
                    title: "数据库大小",
                    value: Utils.formatFileSize(scheduler.databaseSize),
                    icon: "internaldrive",
                    color: .purple
                )

                EnhancedStatCard(
                    title: "最近7天",
                    value: "\(recentMetrics.count)条记录",
                    icon: "calendar",
                    color: .orange
                )

                EnhancedStatCard(
                    title: "应用记录",
                    value: "\(appUsageSummary.count)个应用",
                    icon: "app",
                    color: .green
                )
            }
        }
        .padding()
        .background(Color(.controlBackgroundColor))
        .cornerRadius(10)
    }

    private var recentDataSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("最近数据")
                .font(.headline)

            if recentMetrics.isEmpty {
                Text("暂无数据")
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding()
            } else {
                ScrollView {
                    LazyVStack(spacing: 8) {
                        ForEach(Array(recentMetrics.prefix(10).enumerated()), id: \.offset) { _, metric in
                            MetricRowView(metric: metric)
                        }
                    }
                }
                .frame(maxHeight: 200)
            }
        }
        .padding()
        .background(Color(.controlBackgroundColor))
        .cornerRadius(10)
    }

    private var dataActionsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("数据操作")
                .font(.headline)

            VStack(spacing: 8) {
                Button("导出最近7天数据") {
                    exportRecentData()
                }
                .buttonStyle(.bordered)
                .frame(maxWidth: .infinity)

                Button("清理30天前数据") {
                    scheduler.cleanupOldData()
                    loadData()
                }
                .buttonStyle(.bordered)
                .frame(maxWidth: .infinity)

                Button("清除所有数据") {
                    showingClearAlert = true
                }
                .buttonStyle(.borderedProminent)
                .tint(.red)
                .frame(maxWidth: .infinity)
            }
        }
        .padding()
        .background(Color(.controlBackgroundColor))
        .cornerRadius(10)
    }

    // MARK: - 私有方法

    private func loadData() {
        recentMetrics = scheduler.getRecentMetrics(days: 7)
        appUsageSummary = scheduler.getAppUsageSummary(days: 7)
    }

    private func exportRecentData() {
        let panel = NSSavePanel()
        panel.allowedContentTypes = [.json]
        panel.nameFieldStringValue = "system_metrics_\(Date().timeIntervalSince1970).json"

        panel.begin { response in
            if response == .OK, let url = panel.url {
                do {
                    let data = try JSONSerialization.data(withJSONObject: recentMetrics, options: .prettyPrinted)
                    try data.write(to: url)
                    Logger.info("数据已导出到: \(url.path)")
                } catch {
                    Logger.error("数据导出失败: \(error)")
                }
            }
        }
    }
}

// MARK: - 指标行视图

struct MetricRowView: View {
    let metric: [String: Any]

    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 2) {
                if let timestamp = metric["timestamp"] as? Date {
                    Text(timestamp, style: .time)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                HStack(spacing: 12) {
                    if let cpuUsage = metric["cpu_usage"] as? Double {
                        Text("CPU: \(String(format: "%.1f", cpuUsage))%")
                            .font(.caption)
                    }

                    if let memoryUsage = metric["memory_usage"] as? Double {
                        Text("内存: \(String(format: "%.1f", memoryUsage))%")
                            .font(.caption)
                    }

                    if let diskUsage = metric["disk_usage"] as? Double {
                        Text("磁盘: \(String(format: "%.1f", diskUsage))%")
                            .font(.caption)
                    }
                }
            }

            Spacer()

            if let batteryLevel = metric["battery_level"] as? Int {
                Text("\(batteryLevel)%")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(Color(.quaternarySystemFill))
        .cornerRadius(6)
    }
}

// MARK: - 预览

#Preview {
    MonitoringSettingsView()
}

// MARK: - 日志统计视图

struct LogStatsView: View {
    @State private var logStats: [String: Any] = [:]
    @State private var levelStats: [String: Int] = [:]
    @State private var recentErrors: [String] = []
    @State private var recentWarnings: [String] = []

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 基本统计
            if !logStats.isEmpty {
                VStack(alignment: .leading, spacing: 4) {
                    Text("基本信息")
                        .font(.subheadline)
                        .fontWeight(.medium)

                    if let totalLines = logStats["total_lines"] as? Int {
                        Text("总行数: \(totalLines)")
                            .font(.caption)
                    }

                    if let duration = logStats["log_duration_hours"] as? Double {
                        Text("时间跨度: \(String(format: "%.1f", duration))小时")
                            .font(.caption)
                    }
                }
            }

            // 级别统计
            if !levelStats.isEmpty {
                VStack(alignment: .leading, spacing: 4) {
                    Text("级别统计")
                        .font(.subheadline)
                        .fontWeight(.medium)

                    ForEach(levelStats.sorted(by: { $0.key < $1.key }), id: \.key) { level, count in
                        HStack {
                            Text(level)
                                .font(.caption)
                            Spacer()
                            Text("\(count)")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
            }

            // 最近错误
            if !recentErrors.isEmpty {
                VStack(alignment: .leading, spacing: 4) {
                    Text("最近错误")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.red)

                    ForEach(Array(recentErrors.enumerated()), id: \.offset) { _, error in
                        Text(error)
                            .font(.caption)
                            .foregroundColor(.red)
                            .lineLimit(2)
                    }
                }
            }

            // 最近警告
            if !recentWarnings.isEmpty {
                VStack(alignment: .leading, spacing: 4) {
                    Text("最近警告")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.orange)

                    ForEach(Array(recentWarnings.enumerated()), id: \.offset) { _, warning in
                        Text(warning)
                            .font(.caption)
                            .foregroundColor(.orange)
                            .lineLimit(2)
                    }
                }
            }

            // 导出统计信息
            if !logStats.isEmpty {
                VStack(alignment: .leading, spacing: 4) {
                    Text("导出信息")
                        .font(.subheadline)
                        .fontWeight(.medium)

                    let exportStats = Logger.getExportStats()
                    if let totalLines = exportStats["total_lines"] as? Int {
                        Text("可导出行数: \(totalLines)")
                            .font(.caption)
                    }

                    if let fileSizeMB = exportStats["file_size_mb"] as? Double {
                        Text("文件大小: \(String(format: "%.2f", fileSizeMB))MB")
                            .font(.caption)
                    }
                }
            }

            // 操作按钮
            HStack {
                Button("刷新统计") {
                    loadLogStats()
                }
                .buttonStyle(.bordered)
                .controlSize(.small)

                Button("快速导出") {
                    quickExportLog()
                }
                .buttonStyle(.bordered)
                .controlSize(.small)
            }
        }
        .onAppear {
            loadLogStats()
        }
    }

    private func loadLogStats() {
        logStats = Logger.getLogStats()
        levelStats = Logger.getLogLevelStats()
        recentErrors = Logger.getRecentErrors(limit: 3)
        recentWarnings = Logger.getRecentWarnings(limit: 3)
    }

    private func quickExportLog() {
        let panel = NSSavePanel()
        panel.allowedContentTypes = [.log, .plainText]
        panel.nameFieldStringValue = Logger.getSuggestedExportFileName(prefix: "quick_export")

        panel.begin { response in
            if response == .OK, let url = panel.url {
                // 根据错误数量决定导出策略
                if recentErrors.count > 0 {
                    // 有错误时导出错误和警告
                    if Logger.exportErrorsAndWarnings(to: url) {
                        Logger.info("错误和警告日志已快速导出")
                    }
                } else {
                    // 无错误时导出最近7天
                    if Logger.exportRecentLogs(to: url, days: 7) {
                        Logger.info("最近7天日志已快速导出")
                    }
                }
            }
        }
    }
}

#Preview {
    MonitoringSettingsView()
}

#Preview {
    DataManagementView()
}

#Preview {
    LogStatsView()
        .padding()
}
