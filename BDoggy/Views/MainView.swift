//
//  MenuBarView.swift
//  BDoggy
//
//  Created by K4 on 2024/12/9.
//

import Foundation
import SwiftUI

struct MainView: View {
    var model: SystemUsageModel
    @Binding var currentPage: NavigationTag
    @Environment(\.openWindow) private var openWindow
    @Environment(\.openSettings) private var openSettings
    // 使用 @StateObject 来管理 SystemUsageModel 的生命周期
    
    @Namespace private var namespace
    
    var body: some View {
        HStack(alignment: .top, spacing: 0) {
            VStack(alignment: .leading, spacing: 10) {
                ZStack(alignment: .leading) {
                    HStack(alignment: .center, spacing: 0) {
                        Image(systemName: "cpu")
                            .resizable()
                            .scaledToFit()
                            .frame(width: 25, height: 25)
                            .foregroundColor(Color("text"))
                            .padding(.horizontal, 12)
                        VStack(alignment: .leading, spacing: 2) {
                            Text("CPU: \(String(format: "%.2f", model.cpuTotal))%")
                                .font(.system(size: 18))
                                .foregroundColor(Color("text"))
                            Text("system: \(String(format: "%.2f", model.cpuSystem))%")
                                .alignmentGuide(.leading) { _ in -5 }
                                .font(.system(size: 10))
                                .fontWeight(.thin)
                                .foregroundColor(Color("text"))
                            Text("user: \(String(format: "%.2f", model.cpuUser))%")
                                .alignmentGuide(.leading) { _ in -5 }
                                .font(.system(size: 10))
                                .fontWeight(.thin)
                                .foregroundColor(Color("text"))
                            Text("idle: \(String(format: "%.2f", model.cpuIdle))%")
                                .alignmentGuide(.leading) { _ in -5 }
                                .font(.system(size: 10))
                                .fontWeight(.thin)
                                .foregroundColor(Color("text"))
                        }
                        .padding(.vertical, 10)
                    }
                    .frame(minWidth: 182, alignment: .leading)
                }
                .background(
                    // 背景带有阴影
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color("theme"))
                        .shadow(radius: 5, x: 0, y: 3)
                )
                ZStack {
                    HStack(spacing: 0) {
                        Image(systemName: "memorychip")
                            .resizable()
                            .scaledToFit()
                            .frame(width: 25, height: 25)
                            .foregroundColor(Color("text"))
                            .padding(.horizontal, 12)
                        VStack(alignment: .leading, spacing: 2) {
                            Text("Memory: \(String(format: "%.1f", model.usedMemoryPercentage))%")
                                .font(.system(size: 18))
                                .foregroundColor(Color("text"))
                            Text("\("Pressure".localized): \(String(format: "%.1f", model.memoryPressure))% (\(getPressureLevel(model.memoryPressure)))")
                                .alignmentGuide(.leading) { _ in -5 }
                                .font(.system(size: 10))
                                .fontWeight(getPressureWeight(model.memoryPressure))
                                .foregroundColor(getPressureColor(model.memoryPressure))
                            Text("Available: \(String(format: "%.1f", model.availableMemory))GB")
                                .alignmentGuide(.leading) { _ in -5 }
                                .font(.system(size: 10))
                                .fontWeight(.thin)
                                .foregroundColor(Color("text"))
                            Text("Used: \(String(format: "%.1f", model.usedMemory))GB")
                                .alignmentGuide(.leading) { _ in -5 }
                                .font(.system(size: 10))
                                .fontWeight(.thin)
                                .foregroundColor(Color("text"))
                            Text("Total: \(String(format: "%.1f", model.totalMemory))GB")
                                .alignmentGuide(.leading) { _ in -5 }
                                .font(.system(size: 10))
                                .fontWeight(.thin)
                                .foregroundColor(Color("text"))
                            
                        }.padding(.vertical, 10)
                    }.frame(minWidth: 182, alignment: .leading)
                }
                .background(
                    // 背景带有阴影
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color("theme"))
                        .shadow(radius: 5, x: 0, y: 3)
                )
                
                ZStack {
                    HStack(spacing: 0) {
                        Image(systemName: "battery.100percent.circle")
                            .resizable()
                            .scaledToFit()
                            .frame(width: 25, height: 25)
                            .foregroundColor(Color("text"))
                            .padding(.horizontal, 12)
                        VStack(alignment: .leading, spacing: 2) {
                            Text("Battery: \(String(format: "%.1f", model.temperature))°C")
                                .font(.system(size: 18))
                                .foregroundColor(Color("text"))
                            Text("Cycle Count: \(String(format: "%.2f", model.cycleCount))")
                                .font(.system(size: 10))
                                .fontWeight(.thin)
                                .foregroundColor(Color("text"))
                                .alignmentGuide(.leading) { _ in -5 }
                            Text("\("Healthy".localized): \(String(format: "%.1f", 100 - model.cycleLifePercentage))% (\(getHealthyLevel(100 - model.cycleLifePercentage)))")
                                .font(.system(size: 10))
                                .fontWeight(getHealthyWeight(100 - model.cycleLifePercentage))
                                .foregroundColor(getHealthyColor(100 - model.cycleLifePercentage))
                                .alignmentGuide(.leading) { _ in -5 }
                        }.padding(.vertical, 10)
                    }.frame(minWidth: 182, alignment: .leading)
                }
                .background(
                    // 背景带有阴影
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color("theme"))
                        .shadow(radius: 5, x: 0, y: 3)
                )
                
                ZStack {
                    HStack(spacing: 0) {
                        Image(systemName: "network")
                            .resizable()
                            .scaledToFit()
                            .frame(width: 25, height: 25)
                            .foregroundColor(Color("text"))
                            .padding(.horizontal, 12)
                        VStack(alignment: .leading, spacing: 2) {
                            Text("Network: \(model.networkType)")
                                .font(.system(size: 18))
                                .foregroundColor(Color("text"))
                            Text("Local IP: \(model.ipAddress)")
                                .font(.system(size: 10))
                                .fontWeight(.thin)
                                .foregroundColor(Color("text"))
                                .alignmentGuide(.leading) { _ in -5 }
                            Text("\("Upload".localized): \(String(format: "%.1f", model.uploadSpeed))Kb/s")
                                .font(.system(size: 10))
                                .fontWeight(getHealthyWeight(100 - model.cycleLifePercentage))
                                .foregroundColor(getHealthyColor(100 - model.cycleLifePercentage))
                                .alignmentGuide(.leading) { _ in -5 }
                            Text("\("Download".localized): \(String(format: "%.1f", model.downloadSpeed))Kb/s")
                                .font(.system(size: 10))
                                .fontWeight(getHealthyWeight(100 - model.cycleLifePercentage))
                                .foregroundColor(getHealthyColor(100 - model.cycleLifePercentage))
                                .alignmentGuide(.leading) { _ in -5 }
                        }.padding(.vertical, 10)
                    }.frame(minWidth: 182, alignment: .leading)
                }
                .background(
                    // 背景带有阴影
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color("theme"))
                        .shadow(radius: 5, x: 0, y: 3)
                )
                
                HStack(alignment: .center, spacing: 10) {
                    Button(action: {
                        currentPage = .Runner
                    }) {
                        Image(systemName: "figure.run")
                            .resizable()
                            .scaledToFit()
                            .frame(width: 30, height: 30)
                            .foregroundColor(Color("text"))
                            .padding(12)
                            .help("help_runner".localized)
                    }
                    .buttonStyle(CustomButtonStyle())
                    
                    Button(action: {
                        openWindow(id: "runner-store")
                    }) {
                        Image(systemName: "storefront.circle.fill")
                            .resizable()
                            .scaledToFit()
                            .frame(width: 30, height: 30)
                            .foregroundColor(Color("text"))
                            .padding(12)
                            .help("help_runner_store".localized)
                    }
                    .buttonStyle(CustomButtonStyle())
                    Button(action: {
                        openWindow(id: "runner-maker")
                    }) {
                        Image(systemName: "wand.and.stars")
                            .resizable()
                            .scaledToFit()
                            .frame(width: 30, height: 30)
                            .foregroundColor(Color("text"))
                            .padding(12)
                            .help("help_runner_maker".localized)
                    }
                    .buttonStyle(CustomButtonStyle())
                }
            }
            .padding(.trailing, 5)
            .padding(.leading, 10)
            VStack(spacing: 10) {
                Button(action: {
                    // 打开系统活动监视器
                    let activityMonitorPath = "/System/Applications/Utilities/Activity Monitor.app"
                    NSWorkspace.shared.open(URL(fileURLWithPath: activityMonitorPath))
                }) {
                    Image(systemName: "gauge.open.with.lines.needle.33percent")
                        .resizable()
                        .scaledToFit()
                        .frame(width: 30, height: 30)
                        .foregroundColor(Color("text"))
                        .padding(12)
                        .help("help_activity_monitor".localized)
                }
                .buttonStyle(CustomButtonStyle())
                Button(action: {
                    openWindow(id: "gif-composer")
                }) {
                    Image("GifMaker")
                        .resizable()
                        .scaledToFit()
                        .frame(width: 30, height: 30)
                        .foregroundColor(Color("text"))
                        .padding(12)
                        .help("gif_composer".localized)
                }
                .buttonStyle(CustomButtonStyle())
                Button(action: {
                    openWindow(id: "gif-splitter")
                }) {
                    Image(systemName: "scissors")
                        .resizable()
                        .scaledToFit()
                        .frame(width: 30, height: 30)
                        .foregroundColor(Color("text"))
                        .padding(12)
                        .help("help_gif_splitter".localized)
                }
                .buttonStyle(CustomButtonStyle())
                Button(action: {
                    showDevelopingFeatureAlert()
                }) {
                    Image("Ai")
                        .resizable()
                        .scaledToFit()
                        .frame(width: 30, height: 30)
                        .foregroundColor(Color("text"))
                        .padding(12)
                        .help("help_dev".localized)
                }
                .buttonStyle(CustomButtonStyle())
                Button(action: {
                    openWindow(id: "system-monitor")
                }) {
                    Image(systemName: "chart.line.uptrend.xyaxis")
                        .resizable()
                        .scaledToFit()
                        .frame(width: 30, height: 30)
                        .foregroundColor(Color("text"))
                        .padding(12)
                        .help("系统监控")
                }
                .buttonStyle(CustomButtonStyle())
                Button(action: {
                    showDevelopingFeatureAlert()
                }) {
                    Image(systemName: "wrench.and.screwdriver")
                        .resizable()
                        .scaledToFit()
                        .frame(width: 30, height: 30)
                        .foregroundColor(Color("text"))
                        .padding(12)
                        .help("help_dev".localized)
                }
                .buttonStyle(CustomButtonStyle())
                Button(action: {
                    currentPage = .More
                }) {
                    Image(systemName: "ellipsis")
                        .resizable()
                        .scaledToFit()
                        .frame(width: 30, height: 30)
                        .foregroundColor(Color("text"))
                        .padding(12)
                        .help("help_more".localized)
                }
                .buttonStyle(CustomButtonStyle())
            }
            .padding(.leading, 5)
            .padding(.trailing, 10)
        }
        .padding(.vertical, 10)
        .frame(minHeight: 400)
        .fixedSize()
    }
}

extension MainView {
    private func getPressureLevel(_ pressure: Double) -> String {
        switch pressure {
        case 0...30:
            return "Low".localized
        case 31...70:
            return "Medium".localized
        default:
            return "High".localized
        }
    }
    
    private func getPressureColor(_ pressure: Double) -> Color {
        switch pressure {
        case 0...30:
            return Color("text")
        case 31...70:
            return .yellow
        default:
            return .red
        }
    }
    
    private func getPressureWeight(_ pressure: Double) -> Font.Weight {
        switch pressure {
        case 0...30:
            return .thin
        case 31...70:
            return .thin
        default:
            return .bold
        }
    }
    
    private func getHealthyLevel(_ cycleLifePercentage: Double) -> String {
        switch cycleLifePercentage {
        case 0...30:
            return "Low".localized
        case 31...70:
            return "Medium".localized
        default:
            return "High".localized
        }
    }
    
    private func getHealthyColor(_ cycleLifePercentage: Double) -> Color {
        switch cycleLifePercentage {
        case 0...30:
            return .red
        case 31...70:
            return .yellow
        default:
            return Color("text")
        }
    }
    
    private func getHealthyWeight(_ cycleLifePercentage: Double) -> Font.Weight {
        switch cycleLifePercentage {
        case 0...30:
            return .bold
        case 31...70:
            return .thin
        default:
            return .thin
        }
    }
    
    /// 显示"功能开发中"的提示框
    private func showDevelopingFeatureAlert() {
        let alert = NSAlert()
        alert.messageText = "feature_developing_title".localized
        alert.informativeText = "feature_developing_message".localized
        alert.alertStyle = .informational
        alert.addButton(withTitle: "ok".localized)
        alert.runModal()
    }
}
