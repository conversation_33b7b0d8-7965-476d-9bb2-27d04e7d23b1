////
////  SystemMonitorPageView.swift
////  BDoggy
////
////  Created by System Monitor on 2024/12/11.
////
//import Charts
//import Foundation
//import SwiftUI
//
//struct SystemMonitorPageView: View {
//    @StateObject private var monitorManager = SystemMonitorManager.shared
//    @StateObject private var permissionManager = PrivacyPermissionManager.shared
//    @State private var selectedTab = 0
//    @State private var showingSettings = false
//    @State private var showingDataClearAlert = false
//    
//    var body: some View {
//        NavigationView {
//            VStack(spacing: 0) {
//                // 顶部状态栏
//                statusBar
//                
//                Divider()
//                
//                // 主要内容区域
//                TabView(selection: $selectedTab) {
//                    // 实时监控标签页
//                    realTimeMonitoringView
//                        .tabItem {
//                            Image(systemName: "chart.line.uptrend.xyaxis")
//                            Text("实时监控")
//                        }
//                        .tag(0)
//                    
//                    // 历史趋势标签页
//                    historicalTrendsView
//                        .tabItem {
//                            Image(systemName: "chart.xyaxis.line")
//                            Text("历史趋势")
//                        }
//                        .tag(1)
//                    
//                    // 系统信息标签页
//                    systemInfoView
//                        .tabItem {
//                            Image(systemName: "info.circle")
//                            Text("系统信息")
//                        }
//                        .tag(2)
//                }
//            }
//        }
//        .navigationTitle("系统监控")
//        .toolbar {
//            ToolbarItem(placement: .primaryAction) {
//                Button(action: { showingSettings = true }) {
//                    Image(systemName: "gear")
//                }
//            }
//        }
//        .sheet(isPresented: $showingSettings) {
//            SystemMonitorSettingsView()
//        }
//        .alert("清除所有数据", isPresented: $showingDataClearAlert) {
//            Button("清除", role: .destructive) {
//                _ = monitorManager.clearAllData()
//            }
//            Button("取消", role: .cancel) {}
//        } message: {
//            Text("此操作将永久删除所有监控数据，无法撤销。")
//        }
//        .onAppear {
//            permissionManager.checkAllPermissions()
//        }
//    }
//    
//    // MARK: - 状态栏
//
//    private var statusBar: some View {
//        HStack {
//            // 监控状态指示器
//            HStack(spacing: 8) {
//                Circle()
//                    .fill(monitorManager.isMonitoring ? Color.green : Color.red)
//                    .frame(width: 8, height: 8)
//                
//                Text(monitorManager.isMonitoring ? "监控中" : "已停止")
//                    .font(.caption)
//                    .foregroundColor(.secondary)
//            }
//            
//            Spacer()
//            
//            // 最后采集时间
//            if let lastCollection = monitorManager.lastCollectionTime {
//                Text("最后更新: \(lastCollection, formatter: timeFormatter)")
//                    .font(.caption)
//                    .foregroundColor(.secondary)
//            }
//            
//            Spacer()
//            
//            // 控制按钮
//            HStack(spacing: 12) {
//                Button(monitorManager.isMonitoring ? "停止" : "开始") {
//                    if monitorManager.isMonitoring {
//                        monitorManager.stopMonitoring()
//                    } else {
//                        monitorManager.startMonitoring()
//                    }
//                }
//                .buttonStyle(.bordered)
//                
//                Button("立即采集") {
//                    monitorManager.collectOnce()
//                }
//                .buttonStyle(.bordered)
//                .disabled(!monitorManager.isMonitoring)
//            }
//        }
//        .padding(.horizontal)
//        .padding(.vertical, 8)
//        .background(Color(NSColor.controlBackgroundColor))
//    }
//    
//    // MARK: - 实时监控视图
//
//    private var realTimeMonitoringView: some View {
//        ScrollView {
//            LazyVGrid(columns: [
//                GridItem(.flexible()),
//                GridItem(.flexible())
//            ], spacing: 16) {
//                // CPU 使用率卡片
//                MetricCardView(
//                    title: "CPU 使用率",
//                    value: getCurrentCPUUsage(),
//                    unit: "%",
//                    icon: "cpu",
//                    color: .blue
//                )
//                
//                // 内存使用率卡片
//                MetricCardView(
//                    title: "内存使用率",
//                    value: getCurrentMemoryUsage(),
//                    unit: "%",
//                    icon: "memorychip",
//                    color: .orange
//                )
//                
//                // 磁盘使用率卡片
//                MetricCardView(
//                    title: "磁盘使用率",
//                    value: getCurrentDiskUsage(),
//                    unit: "%",
//                    icon: "internaldrive",
//                    color: .purple
//                )
//                
//                // 电池状态卡片
//                if let batteryLevel = getCurrentBatteryLevel() {
//                    MetricCardView(
//                        title: "电池电量",
//                        value: Double(batteryLevel),
//                        unit: "%",
//                        icon: "battery.100",
//                        color: .green
//                    )
//                }
//            }
//            .padding()
//        }
//    }
//    
//    // MARK: - 历史趋势视图
//
//    private var historicalTrendsView: some View {
//        ScrollView {
//            VStack(spacing: 20) {
//                // CPU 使用率趋势图
//                TrendChartView(
//                    title: "CPU 使用率趋势",
//                    data: monitorManager.getCPUUsageTrend(),
//                    color: .blue
//                )
//                
//                // 内存使用率趋势图
//                TrendChartView(
//                    title: "内存使用率趋势",
//                    data: monitorManager.getMemoryUsageTrend(),
//                    color: .orange
//                )
//            }
//            .padding()
//        }
//    }
//    
//    // MARK: - 系统信息视图
//
//    private var systemInfoView: some View {
//        ScrollView {
//            VStack(alignment: .leading, spacing: 16) {
//                let systemInfo = SystemInfoCollector.getCompleteSystemInfo()
//                
//                InfoSectionView(title: "硬件信息") {
//                    InfoRowView(label: "设备型号", value: systemInfo["deviceModel"] as? String ?? "未知")
//                    InfoRowView(label: "CPU", value: systemInfo["cpuName"] as? String ?? "未知")
//                    InfoRowView(label: "CPU 核心数", value: "\(systemInfo["cpuCoreCount"] as? Int ?? 0)")
//                    InfoRowView(label: "总内存", value: String(format: "%.1f GB", systemInfo["totalMemoryGB"] as? Double ?? 0))
//                    InfoRowView(label: "总磁盘", value: String(format: "%.1f GB", systemInfo["totalDiskGB"] as? Double ?? 0))
//                }
//                
//                InfoSectionView(title: "系统信息") {
//                    InfoRowView(label: "macOS 版本", value: systemInfo["macOSVersion"] as? String ?? "未知")
//                    InfoRowView(label: "系统启动时间", value: formatDate(systemInfo["systemBootTime"] as? Date))
//                    InfoRowView(label: "运行时间", value: SystemInfoCollector.formatUptime(systemInfo["systemUptimeSeconds"] as? TimeInterval ?? 0))
//                }
//                
//                InfoSectionView(title: "监控统计") {
//                    InfoRowView(label: "采集次数", value: "\(monitorManager.collectionCount)")
//                    InfoRowView(label: "数据库大小", value: monitorManager.getDatabaseSize())
//                    InfoRowView(label: "数据保留", value: "\(monitorManager.config.dataRetentionDays) 天")
//                }
//                
//                // 数据管理按钮
//                VStack(spacing: 12) {
//                    Button("清理过期数据") {
//                        monitorManager.cleanupOldData()
//                    }
//                    .buttonStyle(.bordered)
//                    
//                    Button("清除所有数据") {
//                        showingDataClearAlert = true
//                    }
//                    .buttonStyle(.bordered)
//                    .foregroundColor(.red)
//                    
//                    Button("在 Finder 中显示数据文件夹") {
//                        permissionManager.showDataFolderInFinder()
//                    }
//                    .buttonStyle(.bordered)
//                }
//                .frame(maxWidth: .infinity)
//            }
//            .padding()
//        }
//    }
//    
//    // MARK: - 辅助方法
//
//    private func getCurrentCPUUsage() -> Double {
//        return SystemMetricsCollector.getCPUMetrics().usage
//    }
//    
//    private func getCurrentMemoryUsage() -> Double {
//        return SystemMetricsCollector.getMemoryMetrics().usagePercent
//    }
//    
//    private func getCurrentDiskUsage() -> Double {
//        return SystemMetricsCollector.getDiskMetrics().usagePercent
//    }
//    
//    private func getCurrentBatteryLevel() -> Int? {
//        return SystemMetricsCollector.getBatteryMetrics().level
//    }
//    
//    private func formatDate(_ date: Date?) -> String {
//        guard let date = date else { return "未知" }
//        let formatter = DateFormatter()
//        formatter.dateStyle = .medium
//        formatter.timeStyle = .short
//        return formatter.string(from: date)
//    }
//    
//    private var timeFormatter: DateFormatter {
//        let formatter = DateFormatter()
//        formatter.timeStyle = .medium
//        return formatter
//    }
//}
//
//// MARK: - 预览
//
//struct SystemMonitorPageView_Previews: PreviewProvider {
//    static var previews: some View {
//        SystemMonitorPageView()
//            .frame(width: 800, height: 600)
//    }
//}
