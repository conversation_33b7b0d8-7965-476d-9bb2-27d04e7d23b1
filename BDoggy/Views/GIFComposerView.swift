import Foundation
import SwiftUI
import UniformTypeIdentifiers

struct GIFComposerView: View {
    @StateObject private var viewModel = GIFComposerViewModel()
    @Environment(\.colorScheme) private var colorScheme
    @State private var tortoiseScale: CGFloat = 1.0
    @State private var hareScale: CGFloat = 1.0
    @State private var isDragging: Bool = false
    @State private var lastMovedIndex: Int? = nil
    @State private var isNameAnimating: Bool = false
    
    var body: some View {
        ZStack {
            // 背景
            Color(NSColor.windowBackgroundColor)
                .ignoresSafeArea()
            
            VStack(spacing: 0) {
                // 主内容区域
                ScrollView {
                    VStack(spacing: 25) {
                        // GIF预览区域
                        VStack(spacing: 15) {
                            ZStack {
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(colorScheme == .dark ?
                                        Color.black.opacity(0.2) :
                                        Color.white)
                                    .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
                                
                                if viewModel.isLoading {
                                    ProgressView("gif_composer_loading".localized)
                                        .frame(width: 320, height: 320)
                                } else if let previewImage = viewModel.currentPreviewFrame {
                                    VStack(spacing: 10) {
                                        Image(nsImage: previewImage)
                                            .resizable()
                                            .aspectRatio(contentMode: .fit)
                                            .frame(width: 320, height: 260)
                                            .cornerRadius(8)
                                        
                                        // 预览控制按钮
                                        Button(action: {
                                            if viewModel.isPreviewPlaying {
                                                viewModel.stopAnimation()
                                            } else {
                                                viewModel.startAnimation()
                                            }
                                        }) {
                                            Text(viewModel.isPreviewPlaying ?
                                                "gif_composer_preview_stop".localized :
                                                "gif_composer_preview_start".localized)
                                                .frame(width: 100, height: 30)
                                                .font(.headline)
                                        }
                                        .buttonStyle(.borderedProminent)
                                        .tint(viewModel.isPreviewPlaying ? .red : .green)
                                        .padding(.bottom, 15)
                                    }
                                } else {
                                    VStack(spacing: 15) {
                                        Image(systemName: "photo.on.rectangle.angled")
                                            .resizable()
                                            .aspectRatio(contentMode: .fit)
                                            .frame(width: 80, height: 80)
                                            .foregroundColor(.gray)
                                        
                                        Text("gif_composer_select_images".localized)
                                            .font(.headline)
                                            .foregroundColor(.gray)
                                    }
                                    .frame(width: 320, height: 320)
                                }
                            }
                            .frame(height: 320)
                            
                            // 显示图片数量信息
                            if !viewModel.selectedImages.isEmpty {
                                HStack(spacing: 5) {
                                    Spacer()
                                    Text("gif_composer_images_count".localized(with: viewModel.selectedImages.count))
                                        .font(.headline)
                                        .padding(.horizontal, 12)
                                        .padding(.vertical, 6)
                                        .background(
                                            Capsule()
                                                .fill(Color.blue.opacity(0.1))
                                        )
                                        .foregroundColor(.blue)
                                    Spacer()
                                }
                            }
                        }
                        .padding(.horizontal)
                        
                        // 图片列表和排序
                        if !viewModel.selectedImages.isEmpty {
                            VStack(alignment: .leading, spacing: 15) {
                                HStack {
                                    Text("gif_composer_image_list".localized)
                                        .font(.headline)
                                    
                                    if !viewModel.isPreviewPlaying {
                                        Text("gif_composer_drag_to_reorder".localized)
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                            .padding(.horizontal, 8)
                                            .padding(.vertical, 4)
                                            .background(Color.blue.opacity(0.1))
                                            .cornerRadius(4)
                                    }
                                }
                                
                                ScrollViewReader { proxy in
                                        
                                    ScrollView(.horizontal, showsIndicators: true) {
                                        HStack(spacing: 15) {
                                            ForEach(viewModel.selectedImages.indices, id: \.self) { index in
                                                VStack(spacing: 8) {
                                                    // 图片预览
                                                    Image(nsImage: viewModel.selectedImages[index].image)
                                                        .resizable()
                                                        .aspectRatio(contentMode: .fit)
                                                        .frame(width: 100, height: 100)
                                                        .cornerRadius(8)
                                                        .overlay(
                                                            RoundedRectangle(cornerRadius: 8)
                                                                .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                                                        )
                                                                                        
                                                    // 排序和删除按钮
                                                    HStack(spacing: 8) {
                                                        Button(action: {
                                                            viewModel.moveImageUp(at: index)
                                                            
                                                            withAnimation {
                                                                proxy.scrollTo(index - 1)
                                                            }
                                                            
                                                            // 触发动画
                                                            animateNameAfterMove(index: index - 1)
                                                        }) {
                                                            Image(systemName: "arrow.left")
                                                                .frame(width: 24, height: 24)
                                                        }
                                                        .buttonStyle(.borderless)
                                                        .disabled(index == 0 || viewModel.isPreviewPlaying)
                                                        .opacity((index == 0 || viewModel.isPreviewPlaying) ? 0.3 : 1.0)
                                                                                            
                                                        Button(action: {
                                                            viewModel.moveImageDown(at: index)
                                                            
                                                            withAnimation {
                                                                proxy.scrollTo(index + 1)
                                                            }
                                                            
                                                            // 触发动画
                                                            animateNameAfterMove(index: index + 1)
                                                        }) {
                                                            Image(systemName: "arrow.right")
                                                                .frame(width: 24, height: 24)
                                                        }
                                                        .buttonStyle(.borderless)
                                                        .disabled(index == viewModel.selectedImages.count - 1 || viewModel.isPreviewPlaying)
                                                        .opacity((index == viewModel.selectedImages.count - 1 || viewModel.isPreviewPlaying) ? 0.3 : 1.0)
                                                                                            
                                                        Button(action: {
                                                            viewModel.removeImage(at: index)
                                                        }) {
                                                            Image(systemName: "trash")
                                                                .frame(width: 24, height: 24)
                                                        }
                                                        .buttonStyle(.borderless)
                                                        .disabled(viewModel.isPreviewPlaying)
                                                        .opacity(viewModel.isPreviewPlaying ? 0.3 : 1.0)
                                                    }
                                                                                        
                                                    // 图片名称
                                                    Text(viewModel.selectedImages[index].name)
                                                        .font(.caption)
                                                        .lineLimit(1)
                                                        .foregroundColor(lastMovedIndex == index && isNameAnimating ? .red : .secondary)
                                                        .scaleEffect(lastMovedIndex == index && isNameAnimating ? 1.3 : 1.0)
                                                        .animation(.easeInOut(duration: 0.3).repeatCount(1, autoreverses: true), value: isNameAnimating && lastMovedIndex == index)
                                                    // 图片序号
                                                    Text("#\(index + 1)")
                                                        .font(.caption)
                                                        .foregroundColor(.secondary)
                                                }
                                                .id(index)
                                                .padding(8)
                                                .background(
                                                    RoundedRectangle(cornerRadius: 12)
                                                        .fill(colorScheme == .dark ?
                                                            Color(NSColor.controlBackgroundColor).opacity(0.3) :
                                                            Color(NSColor.controlBackgroundColor))
                                                )
                                                // 添加拖拽功能
                                                .onDrag {
                                                    // 禁用预览状态下的拖拽
                                                    guard !viewModel.isPreviewPlaying else { return NSItemProvider() }
                                                                                        
                                                    // 设置正在拖拽状态
                                                    isDragging = true
                                                                                        
                                                    // 创建拖拽数据
                                                    let provider = NSItemProvider(object: String(index) as NSString)
                                                    return provider
                                                }
                                                .onDrop(of: [UTType.text], isTargeted: nil) { providers in
                                                    // 禁用预览状态下的拖放
                                                    guard !viewModel.isPreviewPlaying else { return false }
                                                                                        
                                                    // 重置拖拽状态
                                                    isDragging = false
                                                                                        
                                                    // 获取拖拽的源索引
                                                    guard let provider = providers.first else { return false }
                                                                                        
                                                    provider.loadObject(ofClass: NSString.self) { item, _ in
                                                        guard let sourceIndexString = item as? NSString,
                                                              let sourceIndex = Int(sourceIndexString as String) else { return }
                                                                                            
                                                        // 在主线程执行UI更新
                                                        DispatchQueue.main.async {
                                                            // 调用视图模型的移动方法
                                                            viewModel.moveImage(from: sourceIndex, to: index)
                                                            
                                                            // 触发动画
                                                            animateNameAfterMove(index: index)
                                                        }
                                                    }
                                                                                        
                                                    return true
                                                }
                                            }
                                        }
                                        .padding(.vertical, 8)
                                    }
                                    .frame(height: 180)
                                }
                                
                                // 预览状态提示
                                if viewModel.isPreviewPlaying {
                                    Text("gif_composer_preview_tip".localized)
                                        .font(.headline)
                                        .foregroundColor(.red)
                                        .padding(.top, 5)
                                }
                            }
                            .padding()
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(colorScheme == .dark ?
                                        Color(NSColor.controlBackgroundColor).opacity(0.3) :
                                        Color(NSColor.controlBackgroundColor))
                            )
                            .padding(.horizontal)
                        }
                        
                        // 帧间隔设置和保存选项
                        VStack(spacing: 20) {
                            // 保存设置
                            if !viewModel.selectedImages.isEmpty {
                                // 帧间隔设置
                                VStack(alignment: .leading, spacing: 15) {
                                    Text("gif_composer_frame_interval".localized())
                                        .font(.headline)
                                        
                                    VStack(alignment: .leading, spacing: 8) {
                                        HStack(spacing: 5) {
                                            Text("30 ms")
                                                .font(.caption)
                                                .foregroundColor(.secondary)
                                            Slider(value: $viewModel.frameInterval, in: 30 ... 1000, step: 10)
                                                .onChange(of: viewModel.frameInterval) { _, _ in
                                                    viewModel.updatePreviewSpeed()
                                                }
                                                .frame(maxWidth: .infinity)
                                                
                                            Text("1000 ms")
                                                .font(.caption)
                                                .foregroundColor(.secondary)
                                        }
                                        HStack(spacing: 5) {
                                            Spacer()
                                            
                                            Text("gif_composer_frame_interval_ms".localized(with: Int(viewModel.frameInterval)))
                                                .font(.headline)
                                                .padding(.horizontal, 12)
                                                .padding(.vertical, 6)
                                                .background(
                                                    Capsule()
                                                        .fill(Color.blue.opacity(0.1))
                                                )
                                                .foregroundColor(.blue)
                                            
                                            Spacer()
                                        }
                                    }
                                }
                                
                                // GIF高级配置选项
                                VStack(alignment: .leading, spacing: 15) {
                                    Text("gif_composer_advanced_settings".localized())
                                        .font(.headline)
                                    
                                    // 循环次数设置
                                    VStack(alignment: .leading, spacing: 8) {
                                        Text("gif_composer_loop_count".localized)
                                            .font(.subheadline)
                                            .foregroundColor(.secondary)
                                        
                                        HStack {
                                            Picker("", selection: $viewModel.loopCount) {
                                                Text("gif_composer_loop_infinite".localized).tag(0)
                                                ForEach(1 ... 10, id: \.self) { count in
                                                    Text("\(count)")
                                                }
                                            }
                                            .pickerStyle(MenuPickerStyle())
                                            .padding(.vertical, 5) // 添加垂直空间
                                            .frame(width: 150)
                                            
                                            Spacer()
                                            
                                            if viewModel.loopCount == 0 {
                                                Text("gif_composer_loop_infinite".localized)
                                                    .font(.headline)
                                                    .padding(.horizontal, 12)
                                                    .padding(.vertical, 6)
                                                    .background(
                                                        Capsule()
                                                            .fill(Color.blue.opacity(0.1))
                                                    )
                                                    .foregroundColor(.blue)
                                            } else {
                                                Text("gif_composer_loop_times".localized(with: viewModel.loopCount))
                                                    .font(.headline)
                                                    .padding(.horizontal, 12)
                                                    .padding(.vertical, 6)
                                                    .background(
                                                        Capsule()
                                                            .fill(Color.blue.opacity(0.1))
                                                    )
                                                    .foregroundColor(.blue)
                                            }
                                        }
                                    }
                                    
                                    // 颜色深度设置
                                    VStack(alignment: .leading, spacing: 8) {
                                        Text("gif_composer_color_depth".localized)
                                            .font(.subheadline)
                                            .foregroundColor(.secondary)
                                        
                                        Picker("", selection: $viewModel.colorDepth) {
                                            Text("8 bit").font(.title).tag(8)
                                            Text("5 bit").font(.title2).tag(5)
                                            Text("4 bit").font(.headline).tag(4)
                                        }
                                        .pickerStyle(SegmentedPickerStyle())
                                    }
                                }
                                VStack(alignment: .leading, spacing: 15) {
                                    Text("gif_composer_save_settings".localized)
                                        .font(.headline)
                                    
                                    // 保存文件名输入
                                    VStack(alignment: .leading, spacing: 8) {
                                        Text("gif_composer_file_name".localized)
                                            .font(.subheadline)
                                            .foregroundColor(.secondary)
                                        
                                        TextField("gif_composer_file_name_placeholder".localized, text: $viewModel.saveFileName)
                                            .textFieldStyle(.plain)
                                            .padding(10)
                                            .frame(height: 30)
                                            .foregroundColor(Color(hex: "333333"))
                                            .background(Color.white)
                                            .cornerRadius(4)
                                            .shadow(radius: 1)
                                    }
                                    
                                    // 保存位置选择
                                    VStack(alignment: .leading, spacing: 8) {
                                        HStack {
                                            Text("gif_composer_save_location".localized)
                                                .font(.subheadline)
                                                .foregroundColor(.secondary)
                                            Text("gif_composer_default_location_tips".localized)
                                                .font(.caption)
                                                .foregroundColor(.secondary)
                                                .padding(.horizontal, 8)
                                                .padding(.vertical, 4)
                                                .background(Color.blue.opacity(0.1))
                                                .cornerRadius(4)
                                        }
                                        HStack {
                                            HStack(spacing: 10) {
                                                Image(systemName: "folder")
                                                    .resizable()
                                                    .scaledToFit()
                                                    .frame(width: 20, height: 20)
                                                    .foregroundColor(.blue)
                                                    .font(.system(size: 10))
                                                
                                                Text(viewModel.saveDirectoryURL?.path ?? "gif_composer_default_location".localized)
                                                    .lineLimit(1)
                                                    .truncationMode(.middle)
                                                    .font(.system(size: 12))
                                            }
                                            .padding(8)
                                            .frame(maxWidth: .infinity, alignment: .leading)
                                            .background(Color(NSColor.textBackgroundColor))
                                            .cornerRadius(6)
                                            
                                            Button(action: {
                                                viewModel.selectSaveDirectory()
                                            }) {
                                                Image(systemName: "folder.badge.plus")
                                                    .resizable()
                                                    .scaledToFit()
                                                    .frame(width: 20, height: 20)
                                                    .padding(4)
                                            }
                                            .buttonStyle(.borderless)
                                            .background(Color.blue.opacity(0.1))
                                            .cornerRadius(6)
                                        }
                                    }
                                }
                            }
                            
                            HStack(spacing: 15) {
                                // 创建GIF按钮
                                if !viewModel.selectedImages.isEmpty {
                                    Button(action: {
                                        viewModel.createAndSaveGIF()
                                    }) {
                                        HStack {
                                            Image(systemName: "square.and.arrow.down")
                                            Text("gif_composer_create_gif".localized).font(.headline)
                                        }
                                        .frame(maxWidth: .infinity)
                                        .padding(.vertical, 10)
                                    }
                                    .buttonStyle(.borderedProminent)
                                    .disabled(viewModel.saveFileName.isEmpty)
                                    
                                    // 清空按钮
                                    Button(action: {
                                        viewModel.clean()
                                    }) {
                                        HStack {
                                            Image(systemName: "trash")
                                            Text("gif_composer_clear".localized).font(.headline)
                                        }
                                        .frame(maxWidth: .infinity)
                                        .padding(.vertical, 10)
                                    }
                                    .buttonStyle(.bordered)
                                }
                            }
                            
                            // 选择图片按钮
                            Button(action: {
                                viewModel.selectImages()
                            }) {
                                HStack {
                                    Image(systemName: "photo.badge.plus")
                                    Text("gif_composer_select_images_button".localized).font(.headline)
                                }
                                .frame(maxWidth: .infinity)
                                .padding(.vertical, 12)
                            }
                            .buttonStyle(.borderedProminent)
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(colorScheme == .dark ?
                                    Color(NSColor.controlBackgroundColor).opacity(0.3) :
                                    Color(NSColor.controlBackgroundColor))
                        )
                        .padding(.horizontal)
                    }
                    .padding(.vertical)
                }
            }
            .alert(isPresented: $viewModel.showErrorAlert) {
                Alert(title: Text("gif_composer_alert_title".localized), message: Text(viewModel.errorMessage), dismissButton: .default(Text("gif_composer_ok".localized)))
            }
            .onDisappear {
                Logger.debug("GIFComposer视图消失")
            }
            .onReceive(NotificationCenter.default.publisher(for: .windowWillCloseGIFComposer)) { _ in
                Logger.debug("GIF合成工具窗口关闭")
                viewModel.clean()
            }
        }
    }
    
    // 在moveImage方法调用后触发动画
    func animateNameAfterMove(index: Int) {
        lastMovedIndex = index
        isNameAnimating = true
        
        // 1秒后重置动画状态
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            isNameAnimating = false
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                lastMovedIndex = nil
            }
        }
    }
}
