//
//  EnhancedSystemMonitorView.swift
//  BDoggy
//
//  Created by K4 on 2025/1/11.
//

import SwiftUI

/// 增强版系统监控视图
/// 提供完整的系统监控功能界面
struct EnhancedSystemMonitorView: View {
    @State private var scheduler = SystemMonitorScheduler.shared
    @State private var permissionManager = PermissionManager.shared
    @State private var showingPermissionAlert = false
    @State private var showingSettings = false
    @State private var showingDataManagement = false
    
    var body: some View {
        NavigationSplitView {
            // 左侧主内容
            ScrollView {
                VStack(spacing: 16) {
                    headerSection
                    permissionSection
                    monitoringControlSection
                    dataStatsSection
                    quickActionsSection
                }
                .padding()
            }
            .navigationTitle("系统监控")
            .toolbar {
                ToolbarItem(placement: .primaryAction) {
                    HStack(spacing: 8) {
                        Button {
                            showingSettings = true
                            showingDataManagement = false
                        } label: {
                            Label("设置", systemImage: "gear")
                        }
                        .buttonStyle(.bordered)
                        
                        Button {
                            showingDataManagement = true
                            showingSettings = false
                        } label: {
                            Label("数据管理", systemImage: "folder")
                        }
                        .buttonStyle(.bordered)
                    }
                }
            }
            .frame(minWidth: 350, idealWidth: 350) // 控制左侧宽度
        } detail: {
            // 右侧详情内容
            Group {
                if showingSettings {
                    MonitoringSettingsView()
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else if showingDataManagement {
                    DataManagementView()
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else {
                    VStack {
                        Image(systemName: "chart.line.uptrend.xyaxis")
                            .font(.system(size: 60))
                            .foregroundColor(.secondary)
                        Text("选择一个功能开始")
                            .font(.title2)
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                }
            }
        }
        .alert("权限需求", isPresented: $showingPermissionAlert) {
            Button("打开设置") {
//                permissionManager.requestAccessibilityPermission()
                permissionManager.openAccessibilitySettings()
            }
            Button("取消", role: .cancel) {}
        } message: {
            Text("系统监控需要辅助功能权限来跟踪应用使用情况。请在系统偏好设置中授予权限。")
        }
    }
    
    // MARK: - 视图组件
    
    private var headerSection: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: "chart.line.uptrend.xyaxis")
                    .font(.title)
                    .foregroundStyle(.blue.gradient)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("系统监控")
                        .font(.title2)
                        .fontWeight(.semibold)
                    
                    if let lastCollection = scheduler.lastCollectionTime {
                        Text("最后采集: \(lastCollection, style: .relative)前")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                // 状态指示器
                VStack(alignment: .trailing, spacing: 4) {
                    HStack(spacing: 6) {
                        Circle()
                            .fill(scheduler.isMonitoring ? .green : .red)
                            .frame(width: 10, height: 10)
                        
                        Text(scheduler.isMonitoring ? "运行中" : "已停止")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(scheduler.isMonitoring ? .green : .red)
                    }
                    
                    if scheduler.isMonitoring {
                        Text("正在采集数据...")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
            }
        }
        .padding()
        .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))
    }
    
    private var permissionSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "lock.shield")
                    .foregroundColor(.orange)
                Text("权限状态")
                    .font(.headline)
            }
            
            VStack(spacing: 12) {
                PermissionStatusRow(
                    title: "辅助功能",
                    description: "用于跟踪应用使用情况",
                    isGranted: permissionManager.hasAccessibilityPermission,
                    isRequired: true
                ) {
                    if !permissionManager.hasAccessibilityPermission {
                        showingPermissionAlert = true
                    }
                }
                
                PermissionStatusRow(
                    title: "完全磁盘访问",
                    description: "用于获取详细系统信息",
                    isGranted: permissionManager.hasFullDiskAccess,
                    isRequired: false
                ) {
                    permissionManager.openFullDiskAccessSettings()
                }
            }
        }
        .padding()
        .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))
    }
    
    private var monitoringControlSection: some View {
        VStack(spacing: 16) {
            HStack {
                Image(systemName: "play.circle")
                    .foregroundColor(.blue)
                Text("监控控制")
                    .font(.headline)
            }
            
            VStack(spacing: 12) {
                HStack(spacing: 12) {
                    Button(action: {
                        if scheduler.isMonitoring {
                            scheduler.stopMonitoring()
                        } else {
                            if permissionManager.hasAccessibilityPermission {
                                scheduler.startMonitoring()
                            } else {
                                showingPermissionAlert = true
                            }
                        }
                    }) {
                        HStack {
                            Image(systemName: scheduler.isMonitoring ? "stop.circle.fill" : "play.circle.fill")
                            Text(scheduler.isMonitoring ? "停止监控" : "开始监控")
                        }
                        .frame(maxWidth: .infinity)
                    }
                    .buttonStyle(.borderedProminent)
                    .controlSize(.large)
                    .disabled(!permissionManager.hasAccessibilityPermission && !scheduler.isMonitoring)
                }
                
                // 采集间隔设置
                HStack {
                    Text("采集间隔")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Picker("采集间隔", selection: Binding(
                        get: { scheduler.config.collectionInterval },
                        set: { newValue in
                            scheduler.config.collectionInterval = newValue
                        }
                    )) {
                        ForEach(MonitorConfig.CollectionInterval.allCases, id: \.rawValue) { interval in
                            Text(interval.displayName)
                                .tag(interval.rawValue)
                        }
                    }
                    .pickerStyle(.menu)
                    .frame(width: 200)
                }
                .padding(.horizontal, 8)
            }
        }
        .padding()
        .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))
    }
    
    private var dataStatsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "chart.bar")
                    .foregroundColor(.green)
                Text("数据统计")
                    .font(.headline)
            }
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                EnhancedStatCard(
                    title: "数据点",
                    value: "\(scheduler.totalDataPoints)",
                    icon: "chart.bar.fill",
                    color: .blue
                )
                
                EnhancedStatCard(
                    title: "数据库大小",
                    value: Utils.formatFileSize(scheduler.databaseSize),
                    icon: "internaldrive.fill",
                    color: .purple
                )
                
                EnhancedStatCard(
                    title: "采集间隔",
                    value: Utils.formatInterval(scheduler.config.collectionInterval),
                    icon: "clock.fill",
                    color: .orange
                )
                
                EnhancedStatCard(
                    title: "权限状态",
                    value: permissionManager.hasAccessibilityPermission ? "已授权" : "需要权限",
                    icon: "lock.shield.fill",
                    color: permissionManager.hasAccessibilityPermission ? .green : .red
                )
            }
        }
        .padding()
        .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))
    }
    
    private var quickActionsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "bolt")
                    .foregroundColor(.yellow)
                Text("快速操作")
                    .font(.headline)
            }
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                QuickActionButton(
                    title: "查看图表",
                    icon: "chart.xyaxis.line",
                    color: .blue
                ) {
                    // TODO: 打开图表视图
                }
                
                QuickActionButton(
                    title: "导出数据",
                    icon: "square.and.arrow.up",
                    color: .green
                ) {
                    // TODO: 导出数据
                }
                
                QuickActionButton(
                    title: "清理日志",
                    icon: "trash",
                    color: .red
                ) {
                    Logger.clearLogFile()
                }
            }
        }
        .padding()
        .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 12))
    }
}

// MARK: - 权限状态行组件

struct PermissionStatusRow: View {
    let title: String
    let description: String
    let isGranted: Bool
    let isRequired: Bool
    let action: () -> Void
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 2) {
                HStack {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.medium)
                    
                    if isRequired {
                        Text("必需")
                            .font(.caption)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(.red.opacity(0.2))
                            .foregroundColor(.red)
                            .cornerRadius(4)
                    }
                }
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            HStack(spacing: 8) {
                Image(systemName: isGranted ? "checkmark.circle.fill" : "xmark.circle.fill")
                    .foregroundColor(isGranted ? .green : .red)
                
                if !isGranted {
                    Button("授权") {
                        action()
                    }
                    .buttonStyle(.bordered)
                    .controlSize(.small)
                }
            }
        }
    }
}

// MARK: - 增强的统计卡片

struct EnhancedStatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundStyle(color.gradient)
            
            Text(value)
                .font(.headline)
                .fontWeight(.semibold)
                .lineLimit(1)
                .minimumScaleFactor(0.8)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(color.opacity(0.1), in: RoundedRectangle(cornerRadius: 8))
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke(color.opacity(0.3), lineWidth: 1)
        )
    }
}

// MARK: - 快速操作按钮

struct QuickActionButton: View {
    let title: String
    let icon: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 6) {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundStyle(color.gradient)
                
                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
                    .multilineTextAlignment(.center)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
        }
        .buttonStyle(.bordered)
        .controlSize(.large)
    }
}

// MARK: - 预览

#Preview {
    EnhancedSystemMonitorView()
}
