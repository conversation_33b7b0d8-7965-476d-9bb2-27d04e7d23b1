//
//  AboutView.swift
//  BDoggy
//
//  Created by K4 on 2024/12/20.
//
import ServiceManagement
import SwiftUI

struct SettingsView: View {
    @AppStorage("autoLaunch") private var autoLaunch = false
    @AppStorage("language") private var language = getDefaultLanguage()
    @State private var showRestartAlert = false
    @State private var selectedLanguage = "en"
    
    private let languages = [
        ("en", "English"),
        ("zh-Hant", "繁體中文"),
        ("ja", "日本語"),
        ("es", "Español")
    ]
    
    // 获取默认语言设置
    private static func getDefaultLanguage() -> String {
        // 获取系统语言
        let systemLanguage = Locale.current.language.languageCode?.identifier ?? "en"
        
        // 支持的语言代码
        let supportedLanguages = ["en", "zh-Hant", "ja", "es"]
        
        // 尝试匹配系统语言
        if systemLanguage == "zh" {
            return "zh-Hant" // 繁体中文
        } else if supportedLanguages.contains(systemLanguage) {
            return systemLanguage
        }
        
        // 默认返回英文
        return "en"
    }
    
    var body: some View {
        TabView {
            List {
                // 自动启动设置
                Section {
                    VStack(alignment: .leading, spacing: 16) {
                        HStack {
                            VStack(alignment: .leading, spacing: 4) {
                                Text("settings_launch".localized)
                                    .font(.system(size: 13, weight: .medium))
                                Text("settings_launch_description".localized)
                                    .font(.system(size: 12))
                                    .foregroundColor(.secondary)
                            }
                            
                            Spacer()
                            
                            Toggle("", isOn: $autoLaunch)
                                .toggleStyle(.switch)
                                .onChange(of: autoLaunch) { _, newState in
                                    if newState {
                                        try? SMAppService.mainApp.register()
                                    } else {
                                        try? SMAppService.mainApp.unregister()
                                    }
                                }
                        }
                        .padding(.vertical, 4)
                    }
                    .padding(.vertical, 8)
                }
                
                // 语言设置
                Section {
                    VStack(alignment: .leading, spacing: 16) {
                        HStack {
                            VStack(alignment: .leading, spacing: 4) {
                                Text("settings_language".localized)
                                    .font(.system(size: 13, weight: .medium))
                                Text("settings_language_description".localized)
                                    .font(.system(size: 12))
                                    .foregroundColor(.secondary)
                            }
                            
                            Spacer()
                            
                            Picker("", selection: $selectedLanguage) {
                                ForEach(languages, id: \.0) { lang in
                                    Text(lang.1).tag(lang.0)
                                }
                            }
                            .frame(width: 120)
                            .onChange(of: selectedLanguage) { oldValue, newValue in
                                if oldValue != newValue {
                                    showRestartAlert = true
                                }
                            }
                        }
                        .padding(.vertical, 4)
                    }
                    .padding(.vertical, 8)
                }
            }
            .listStyle(InsetListStyle())
            .tabItem {
                Label("settings_general".localized, systemImage: "gearshape")
            }
        }
        .frame(width: 500, height: 300)
        .onAppear {
            // 如果是首次启动或语言设置为空，则使用系统语言
            if UserDefaults.standard.string(forKey: "language") == nil {
                language = Self.getDefaultLanguage()
            }
            selectedLanguage = language
        }
        .alert("settings_restart_title".localized, isPresented: $showRestartAlert) {
            Button("settings_restart_now".localized) {
                language = selectedLanguage
                restartApp()
            }
            Button("settings_restart_later".localized) {
                language = selectedLanguage
                showRestartAlert = false
            }
            Button("settings_cancel".localized, role: .cancel) {
                selectedLanguage = language
                showRestartAlert = false
            }
        } message: {
            Text("settings_restart_message".localized)
        }
    }
    
    private func restartApp() {
        let url = URL(fileURLWithPath: Bundle.main.resourcePath!)
        let path = url.deletingLastPathComponent().deletingLastPathComponent().absoluteString
        let task = Process()
        task.launchPath = "/usr/bin/open"
        task.arguments = [path]
        task.launch()
        NSApplication.shared.terminate(nil)
    }
}
