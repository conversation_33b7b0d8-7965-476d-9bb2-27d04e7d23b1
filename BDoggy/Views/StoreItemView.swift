//
//  StoreItemView.swift
//  BDoggy
//
//  Created by K4 on 2025/2/13.
//

import Foundation
import SwiftUI

struct StoreItemView: View {
    @ObservedObject var item: StoreItem // 监听单个 StoreItem 的状态变化
    @StateObject var viewModel: StoreViewModel
    var body: some View {
        HStack {
            AsyncImage(url: URL(string: item.downloadUrls[0])) { image in
                image.image?
                    .resizable()
                    .scaledToFit()
                    .frame(width: 35, height: 35)
            }
            .padding(.vertical, 10)
            .padding(.horizontal, 8)
            VStack(alignment: .leading) {
                Text(item.title)
                    .font(.headline)
                Text(item.description ?? "no_description".localized)
                    .font(.subheadline)
                    .foregroundColor(.gray)
            }
            Spacer()
            if item.isPurchased {
                if item.isDownloading {
                    ProgressView() // 显示下载状态
                } else if !item.isDownloaded {
                    Button("download".localized) {
                        // 下载
                        viewModel.download(store: item)
                    }
                    .buttonStyle(.borderedProminent)
                } else {
                    Text("downloaded".localized)
                        .font(.subheadline)
                        .foregroundColor(.gray)
                }
            } else {
                Text("¥\(item.price)")
                Button("purchase".localized) {
                    // 购买逻辑
                }
                .buttonStyle(BorderedButtonStyle())
            }
        }
        .listRowSeparator(.hidden)
        .padding(.vertical, 8)
        .padding(.horizontal, 10)
    }
}
