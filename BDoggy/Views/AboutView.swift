//
//  AboutView.swift
//  BDoggy
//
//  Created by K4 on 2024/12/20.
//
import Cocoa
import SwiftUI

struct AboutView: View {
    @Binding var currentPage: NavigationTag
    @Environment(\.openWindow) private var openWindow
    let onBack: () -> Void
    private let pageUrl = "https://www.bdoggy.icu/faq"

    var body: some View {
        VStack(spacing: 0) {
            HStack {
                Button(action: {
                    onBack()
                }) {
                    Label("back".localized, systemImage: "chevron.left")
                        .labelStyle(DefaultLabelStyle())
                        .font(.system(size: 15))
                        .padding(10)
                }.buttonStyle(NoBorderButtonStyle())
                Spacer()
            }
            VStack(spacing: 0) {
                if let image = NSImage(named: NSImage.Name("AppIcon")) {
                    Image(nsImage: image)
                        .resizable()
                        .frame(width: 32, height: 32)
                        .cornerRadius(10)
                        .padding()
                } else {
                    Image(systemName: "app.fill")
                        .resizable()
                        .frame(width: 32, height: 32)
                        .cornerRadius(10)
                }
                Text(appName ?? "App")
                    .font(.title2)
                    .bold()
                    .padding(.bottom, 8)
                Text("Version \(appVersion ?? "0.0.0")")
                    .font(.subheadline)
                    .monospaced()
                    .padding(.bottom, 10)
                Text("support_page".localized)
                    .font(.subheadline)
                    .foregroundStyle(Color(hex: "#999999"))
                    .padding(.bottom, 5)
                // 创建一个 Link 组件
                Link(destination: URL(string: pageUrl)!) {
                    Text(pageUrl)
                        .font(.subheadline)
                        .padding(.bottom, 8)
                        .foregroundColor(Color.blue)
                        .underline(true)
                }

                // 添加贡献者按钮
                Button(action: {
                    openWindow(id: "contributors")
                }) {
                    Text("contributors".localized)
                        .font(.subheadline)
                        .padding(.vertical, 5)
                        .padding(.horizontal, 10)
                        .background(Color.blue.opacity(0.1))
                        .cornerRadius(5)
                }
                .buttonStyle(PlainButtonStyle())
                .padding(.top, 5)
                .padding(.bottom, 8)

                Spacer()
                Text("copyright".localized)
                    .font(.subheadline)
                    .monospaced()
                    .padding(.bottom, 15)
            }
        }
        .onAppear {}
        .frame(width: 250, height: 280)
        .shadow(radius: 10)
        .padding(.bottom, 5)
        .padding(.horizontal, 5)
        .edgesIgnoringSafeArea(.all)
        .fixedSize()
    }

    // 获取应用名称
    private var appName: String? {
        return Bundle.main.object(forInfoDictionaryKey: "CFBundleName") as? String ??
            Bundle.main.object(forInfoDictionaryKey: "CFBundleDisplayName") as? String
    }

    // 获取应用图标
    private var appIcon: Image? {
        if let icons = Bundle.main.infoDictionary?["CFBundleIcons"] as? [String: Any],
           let primaryIcon = icons["CFBundlePrimaryIcon"] as? [String: Any],
           let iconFiles = primaryIcon["CFBundleIconFiles"] as? [String],
           let lastIcon = iconFiles.last,
           let uiImage = NSImage(named: lastIcon)
        {
            return Image(nsImage: uiImage)
        }
        return nil
    }

    // 获取应用版本号
    private var appVersion: String? {
        return Bundle.main.object(forInfoDictionaryKey: "CFBundleShortVersionString") as? String
    }
}
