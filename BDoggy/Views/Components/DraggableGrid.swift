import SwiftUI

struct DraggableGrid<Content: View, T: Identifiable>: View {
    let items: [T]
    let columns: [GridItem]
    let spacing: CGFloat
    let itemWidth: CGFloat
    let content: (T) -> Content
    let onReorder: (Int, Int) -> Void
    
    @State private var draggedItem: T.ID?
    @State private var draggedOffset: CGSize = .zero
    
    init(
        items: [T],
        columns: [GridItem],
        spacing: CGFloat = 16,
        itemWidth: CGFloat = 120,
        @ViewBuilder content: @escaping (T) -> Content,
        onReorder: @escaping (Int, Int) -> Void
    ) {
        self.items = items
        self.columns = columns
        self.spacing = spacing
        self.itemWidth = itemWidth
        self.content = content
        self.onReorder = onReorder
    }
    
    @State private var containerWidth: CGFloat = 0
    
    var body: some View {
        GeometryReader { geometry in
            ScrollView {
                LazyVGrid(columns: columns, spacing: spacing) {
                    ForEach(Array(items.enumerated()), id: \.element.id) { index, item in
                        content(item)
                            .offset(draggedItem == item.id ? draggedOffset : .zero)
                            .zIndex(draggedItem == item.id ? 999 : Double(index))
                            .gesture(
                                DragGesture()
                                    .onChanged { value in
                                        draggedItem = item.id
                                        draggedOffset = value.translation
                                    }
                                    .onEnded { value in
                                        if let currentIndex = items.firstIndex(where: { $0.id == item.id }) {
                                            let targetIndex = calculateTargetIndex(
                                                draggedItem: currentIndex,
                                                dragOffset: value.translation,
                                                itemWidth: itemWidth,
                                                itemSpacing: spacing
                                            )
                                            
                                            if currentIndex != targetIndex {
                                                onReorder(currentIndex, targetIndex)
                                            }
                                        }
                                        
                                        draggedItem = nil
                                        draggedOffset = .zero
                                    }
                            )
                    }
                }
            }
            .onAppear {
                containerWidth = geometry.size.width
            }
            .onChange(of: geometry.size.width) { _, newWidth in
                containerWidth = newWidth
            }
        }
    }
    
    private func calculateTargetIndex(draggedItem: Int, dragOffset: CGSize, itemWidth: CGFloat, itemSpacing: CGFloat) -> Int {
        let columnsCount = max(1, Int(floor(containerWidth / (itemWidth + spacing))))
        let currentRow = draggedItem / columnsCount
        let currentCol = draggedItem % columnsCount
        
        // 计算行和列的偏移
        let rowOffset = Int((dragOffset.height / (itemWidth + spacing)).rounded())
        let colOffset = Int((dragOffset.width / (itemWidth + spacing)).rounded())
        
        // 计算目标行和列
        let targetRow = max(0, min((items.count - 1) / columnsCount, currentRow + rowOffset))
        let targetCol = max(0, min(columnsCount - 1, currentCol + colOffset))
        
        // 计算最终索引
        let targetIndex = min(items.count - 1, (targetRow * columnsCount) + targetCol)
        return targetIndex
    }
}
