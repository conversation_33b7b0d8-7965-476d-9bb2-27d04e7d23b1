//
//  TouchTargetDemo.swift
//  BDoggy
//
//  Created by K4 on 2025/1/11.
//

import SwiftUI

/// 点击热区演示视图
struct TouchTargetDemo: View {
    @State private var showTouchAreas = false
    @State private var selectedSection: SettingsSection = .basic
    @State private var tapCount = 0
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                // 控制开关
                VStack(spacing: 16) {
                    Text("点击热区演示")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Toggle("显示点击区域边界", isOn: $showTouchAreas)
                        .toggleStyle(.switch)
                        .padding(.horizontal, 40)
                    
                    Text("点击次数: \(tapCount)")
                        .font(.headline)
                        .foregroundColor(.secondary)
                }
                .padding()
                .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 16))
                
                // 标签栏演示
                VStack(spacing: 16) {
                    Text("优化后的标签栏")
                        .font(.headline)
                    
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 12) {
                            ForEach(SettingsSection.allCases, id: \.self) { section in
                                DemoSectionTabButton(
                                    section: section,
                                    isSelected: selectedSection == section,
                                    showTouchArea: showTouchAreas
                                ) {
                                    selectedSection = section
                                    tapCount += 1
                                }
                            }
                        }
                        .padding(.horizontal, 20)
                        .padding(.vertical, 12)
                    }
                    .background(.regularMaterial)
                }
                
                // 对比演示
                VStack(spacing: 16) {
                    Text("点击热区对比")
                        .font(.headline)
                    
                    HStack(spacing: 30) {
                        VStack(spacing: 8) {
                            Text("优化前")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            
                            OldSectionTabButton(
                                section: .basic,
                                isSelected: true,
                                showTouchArea: showTouchAreas
                            ) {
                                tapCount += 1
                            }
                        }
                        
                        VStack(spacing: 8) {
                            Text("优化后")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            
                            DemoSectionTabButton(
                                section: .basic,
                                isSelected: true,
                                showTouchArea: showTouchAreas
                            ) {
                                tapCount += 1
                            }
                        }
                    }
                }
                .padding()
                .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 16))
                
                Spacer()
                
                // 说明文字
                VStack(alignment: .leading, spacing: 8) {
                    Text("改进说明:")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Text("• 点击热区扩大到至少 44x44 点")
                    Text("• 保持视觉设计不变")
                    Text("• 提高触控板操作的准确性")
                    Text("• 减少点击失误")
                }
                .font(.caption)
                .foregroundColor(.secondary)
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding()
                .background(.quaternary.opacity(0.5), in: RoundedRectangle(cornerRadius: 12))
            }
            .padding()
            .navigationTitle("点击热区优化")
        }
    }
}

// MARK: - 演示组件

/// 带有可视化点击区域的标签按钮
struct DemoSectionTabButton: View {
    let section: SettingsSection
    let isSelected: Bool
    let showTouchArea: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            // 视觉内容容器
            VStack(spacing: 6) {
                // 图标
                Image(systemName: section.icon)
                    .font(.title3)
                    .foregroundStyle(isSelected ? LinearGradient(colors: [section.color], startPoint: .top, endPoint: .bottom) : LinearGradient(colors: [.secondary], startPoint: .top, endPoint: .bottom))
                    .frame(width: 24, height: 24)

                // 标题
                Text(section.title)
                    .font(.caption)
                    .fontWeight(isSelected ? .semibold : .medium)
                    .foregroundColor(isSelected ? section.color : .secondary)
                    .lineLimit(1)
                    .minimumScaleFactor(0.8)
            }
            .frame(minWidth: 60)
            .padding(.horizontal, 12)
            .padding(.vertical, 10)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(isSelected ? section.color.opacity(0.12) : Color.clear)
            )
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(isSelected ? section.color.opacity(0.25) : Color.clear, lineWidth: 1.5)
            )
            .scaleEffect(isSelected ? 1.0 : 0.95)
            .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
        }
        .buttonStyle(.plain)
        // 扩大点击热区
        .padding(.horizontal, 8)
        .padding(.vertical, 6)
        .contentShape(Rectangle())
        // 可视化点击区域
        .overlay(
            Rectangle()
                .stroke(Color.red.opacity(showTouchArea ? 0.5 : 0), lineWidth: 2)
                .animation(.easeInOut(duration: 0.3), value: showTouchArea)
        )
    }
}

/// 原始的标签按钮（用于对比）
struct OldSectionTabButton: View {
    let section: SettingsSection
    let isSelected: Bool
    let showTouchArea: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 6) {
                Image(systemName: section.icon)
                    .font(.title3)
                    .foregroundStyle(isSelected ? LinearGradient(colors: [section.color], startPoint: .top, endPoint: .bottom) : LinearGradient(colors: [.secondary], startPoint: .top, endPoint: .bottom))
                    .frame(width: 24, height: 24)

                Text(section.title)
                    .font(.caption)
                    .fontWeight(isSelected ? .semibold : .medium)
                    .foregroundColor(isSelected ? section.color : .secondary)
                    .lineLimit(1)
                    .minimumScaleFactor(0.8)
            }
            .frame(minWidth: 60)
            .padding(.horizontal, 12)
            .padding(.vertical, 10)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(isSelected ? section.color.opacity(0.12) : Color.clear)
            )
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(isSelected ? section.color.opacity(0.25) : Color.clear, lineWidth: 1.5)
            )
        }
        .buttonStyle(.plain)
        // 可视化点击区域（较小）
        .overlay(
            Rectangle()
                .stroke(Color.blue.opacity(showTouchArea ? 0.5 : 0), lineWidth: 2)
                .animation(.easeInOut(duration: 0.3), value: showTouchArea)
        )
    }
}

// MARK: - 预览

#Preview {
    TouchTargetDemo()
}
