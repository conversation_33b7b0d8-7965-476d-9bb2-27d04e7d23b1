import Foundation
import SwiftUI
import UniformTypeIdentifiers

struct GIFSplitterView: View {
    @StateObject private var viewModel = GIFSplitterViewModel()
    @State private var showingFileChooser = false
    @Environment(\.colorScheme) private var colorScheme
    @State private var tortoiseScale: CGFloat = 1.0
    @State private var hareScale: CGFloat = 1.0
    
    var body: some View {
        ZStack {
            // 背景
            Color(NSColor.windowBackgroundColor)
                .ignoresSafeArea()
            
            VStack(spacing: 0) {
                // 主内容区域
                ScrollView {
                    VStack(spacing: 25) {
                        // GIF预览区域
                        VStack(spacing: 15) {
                            ZStack {
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(colorScheme == .dark ?
                                        Color.black.opacity(0.2) :
                                        Color.white)
                                    .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
                                
                                if viewModel.isLoading {
                                    ProgressView("gif_splitter_loading".localized)
                                        .frame(width: 320, height: 320)
                                } else if let previewImage = viewModel.currentPreviewFrame {
                                    Image(nsImage: previewImage)
                                        .resizable()
                                        .aspectRatio(contentMode: .fit)
                                        .frame(width: 320, height: 320)
                                        .cornerRadius(8)
                                } else {
                                    VStack(spacing: 15) {
                                        Image(systemName: "photo.on.rectangle.angled")
                                            .resizable()
                                            .aspectRatio(contentMode: .fit)
                                            .frame(width: 80, height: 80)
                                            .foregroundColor(.gray)
                                        
                                        Text("gif_splitter_select_gif".localized)
                                            .font(.headline)
                                            .foregroundColor(.gray)
                                    }
                                    .frame(width: 320, height: 320)
                                }
                            }
                            .frame(height: 320)
                            
                            // 显示帧数信息
                            if !viewModel.frames.isEmpty {
                                HStack(spacing: 8) {
                                    Spacer()
                                    Text("gif_splitter_frames_count".localized(with: viewModel.frames.count))
                                        .font(.headline)
                                        .padding(.horizontal, 12)
                                        .padding(.vertical, 6)
                                        .background(
                                            Capsule()
                                                .fill(Color.blue.opacity(0.1))
                                        )
                                        .foregroundColor(.blue)
                                  
                                    Text("gif_splitter_frame_interval".localized(with: viewModel.originSpeed))
                                        .font(.headline)
                                        .padding(.horizontal, 12)
                                        .padding(.vertical, 6)
                                        .background(
                                            Capsule()
                                                .fill(Color.blue.opacity(0.1))
                                        )
                                        .foregroundColor(.blue)
                                    Spacer()
                                }
                            }
                        }
                        .padding(.horizontal)
                        
                        // 预览速度控制
                        if !viewModel.frames.isEmpty {
                            VStack(spacing: 10) {
                                Text("gif_splitter_preview_control".localized)
                                    .font(.headline)
                                    .frame(maxWidth: .infinity, alignment: .leading)
                                
                                HStack(spacing: 15) {
                                    Image(systemName: "tortoise")
                                        .foregroundColor(.gray)
                                        .scaleEffect(tortoiseScale)
                                        .animation(.spring(response: 0.3, dampingFraction: 0.5), value: tortoiseScale)
                                        .onTapGesture {
                                            if viewModel.previewSpeed > 0.5 {
                                                viewModel.previewSpeed -= 0.5
                                                viewModel.updatePreviewSpeed()
                                            } else {
                                                tortoiseScale = 1.3
                                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                                                    tortoiseScale = 1.0
                                                }
                                            }
                                        }
                                    
                                    Slider(value: Binding(
                                        get: { viewModel.previewSpeed },
                                        set: {
                                            viewModel.previewSpeed = $0
                                            // 检查是否达到极限值并触发动画
                                            if $0 >= 3 {
                                                hareScale = 1.3
                                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                                                    hareScale = 1.0
                                                }
                                            } else if $0 <= 0.5 {
                                                tortoiseScale = 1.3
                                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                                                    tortoiseScale = 1.0
                                                }
                                            }
                                        }
                                    ), in: 0.5 ... 3, step: 0.5)
                                        .onChange(of: viewModel.previewSpeed) { _, _ in
                                            viewModel.updatePreviewSpeed()
                                        }
                                    
                                    Image(systemName: "hare")
                                        .foregroundColor(.gray)
                                        .scaleEffect(hareScale)
                                        .animation(.spring(response: 0.3, dampingFraction: 0.5), value: hareScale)
                                        .onTapGesture {
                                            if viewModel.previewSpeed < 3.0 {
                                                viewModel.previewSpeed += 0.5
                                                viewModel.updatePreviewSpeed()
                                            } else {
                                                hareScale = 1.3
                                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                                                    hareScale = 1.0
                                                }
                                            }
                                        }
                                    
                                    Text("\(viewModel.previewSpeed, specifier: "%.1f")X")
                                        .monospacedDigit()
                                        .frame(width: 40)
                                }
                            }
                            .padding()
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(colorScheme == .dark ?
                                        Color(NSColor.controlBackgroundColor).opacity(0.3) :
                                        Color(NSColor.controlBackgroundColor))
                            )
                            .padding(.horizontal)
                        }
                        
                        // 操作区域
                        VStack(spacing: 20) {
                            if !viewModel.frames.isEmpty {
                                // 保存设置区域
                                VStack(alignment: .leading, spacing: 15) {
                                    Text("gif_splitter_save_settings".localized)
                                        .font(.headline)
                                    
                                    // 保存文件夹名称输入
                                    VStack(alignment: .leading, spacing: 8) {
                                        Text("gif_splitter_folder_name".localized)
                                            .font(.subheadline)
                                            .foregroundColor(.secondary)
                                        
                                        TextField("gif_splitter_folder_name_placeholder".localized, text: $viewModel.saveFolderName)
                                            .textFieldStyle(.plain)
                                            .padding(10)
                                            .frame(height: 30)
                                            .foregroundColor(Color(hex: "333333"))
                                            .background(Color.white)
                                            .cornerRadius(4)
                                            .shadow(radius: 1)
                                    }
                                    
                                    // 保存位置选择
                                    VStack(alignment: .leading, spacing: 8) {
                                        Text("gif_splitter_save_location".localized)
                                            .font(.subheadline)
                                            .foregroundColor(.secondary)
                                        
                                        HStack {
                                            HStack(spacing: 10) {
                                                Image(systemName: "folder")
                                                    .resizable()
                                                    .scaledToFit()
                                                    .frame(width: 20, height: 20)
                                                    .foregroundColor(.blue)
                                                    .font(.system(size: 10))
                                                
                                                Text(viewModel.saveDirectoryURL?.path ?? "gif_splitter_default_location".localized)
                                                    .lineLimit(1)
                                                    .truncationMode(.middle)
                                                    .font(.system(size: 12))
                                            }
                                            .padding(8)
                                            .frame(maxWidth: .infinity, alignment: .leading)
                                            .background(Color(NSColor.textBackgroundColor))
                                            .cornerRadius(6)
                                            
                                            Button(action: {
                                                viewModel.selectSaveDirectory()
                                            }) {
                                                Image(systemName: "folder.badge.plus")
                                                    .resizable()
                                                    .scaledToFit()
                                                    .frame(width: 20, height: 20)
                                                    .padding(4)
                                            }
                                            .buttonStyle(.borderless)
                                            .background(Color.blue.opacity(0.1))
                                            .cornerRadius(6)
                                        }
                                    }
                                    
                                    HStack(spacing: 15) {
                                        // 保存按钮
                                        Button(action: {
                                            viewModel.saveFrames()
                                        }) {
                                            HStack {
                                                Image(systemName: "square.and.arrow.down")
                                                Text("gif_splitter_save_frames".localized).font(.headline)
                                            }
                                            .frame(maxWidth: .infinity)
                                            .padding(.vertical, 10)
                                        }
                                        .buttonStyle(.borderedProminent)
                                        .disabled(viewModel.saveFolderName.isEmpty)
                                        
                                        // 清空按钮
                                        Button(action: {
                                            viewModel.clean()
                                        }) {
                                            HStack {
                                                Image(systemName: "trash")
                                                Text("gif_splitter_clear".localized).font(.headline)
                                            }
                                            .frame(maxWidth: .infinity)
                                            .padding(.vertical, 10)
                                        }
                                        .buttonStyle(.bordered)
                                    }
                                }
                            }
                            
                            // 文件选择按钮
                            Button(action: {
                                viewModel.selectGIF()
                            }) {
                                HStack {
                                    Image(systemName: "photo.badge.plus")
                                    Text((!viewModel.frames.isEmpty ? "gif_splitter_reselect_gif_file".localized : "gif_splitter_select_gif_file".localized)).font(.headline)
                                }
                                .frame(maxWidth: .infinity)
                                .padding(.vertical, 12)
                            }
                            .buttonStyle(.borderedProminent)
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(colorScheme == .dark ?
                                    Color(NSColor.controlBackgroundColor).opacity(0.3) :
                                    Color(NSColor.controlBackgroundColor))
                        )
                        .padding(.horizontal)
                    }
                    .padding(.vertical)
                }
            }
            .frame(minWidth: 600, minHeight: 700)
            .alert(isPresented: $viewModel.showErrorAlert) {
                Alert(title: Text("gif_splitter_alert_title".localized), message: Text(viewModel.errorMessage), dismissButton: .default(Text("gif_splitter_ok".localized)))
            }
            .onDisappear {
                Logger.debug("GIFSplitter视图消失")
            }
            .onReceive(NotificationCenter.default.publisher(for: .windowWillCloseGIFSplitter)) { _ in
                Logger.debug("GIF拆帧工具窗口关闭")
                viewModel.clean()
            }
        }
    }
}
