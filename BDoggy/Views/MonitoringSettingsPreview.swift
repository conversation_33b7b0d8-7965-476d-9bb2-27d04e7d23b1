//
//  MonitoringSettingsPreview.swift
//  BDoggy
//
//  Created by K4 on 2025/1/11.
//

import SwiftUI

/// 监控设置预览和对比视图
struct MonitoringSettingsPreview: View {
    @State private var showingModernView = true

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 切换按钮
                Picker("视图模式", selection: $showingModernView) {
                    Text("单页面滚动设计").tag(true)
                    Text("分栏布局设计").tag(false)
                }
                .pickerStyle(.segmented)
                .padding()

                // 设计特点说明
                modernDesignFeatures

                Spacer()

                // 打开设置按钮
                Button("打开监控设置") {
                    // 这里可以打开实际的设置视图
                }
                .buttonStyle(.borderedProminent)
                .controlSize(.large)
            }
            .navigationTitle("设计预览")
        }
    }
    
    private var modernDesignFeatures: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("单页面滚动设计特点")
                .font(.title2)
                .fontWeight(.bold)

            FeatureRow(
                icon: "scroll.fill",
                title: "单页面滚动",
                description: "所有设置项在一个流畅的垂直滚动视图中展示"
            )

            FeatureRow(
                icon: "menubar.rectangle",
                title: "顶部标签栏",
                description: "水平滚动的标签栏，支持快速跳转到不同区域"
            )

            FeatureRow(
                icon: "wand.and.stars",
                title: "流畅动画",
                description: "平滑的滚动动画和标签切换过渡效果"
            )

            FeatureRow(
                icon: "rectangle.stack.fill",
                title: "区域分隔",
                description: "清晰的视觉分隔线和适当的间距设计"
            )

            FeatureRow(
                icon: "hand.draw.fill",
                title: "手势友好",
                description: "支持自然的滑动手势浏览所有内容"
            )

            FeatureRow(
                icon: "gauge.with.dots.needle.bottom.50percent",
                title: "进度指示",
                description: "底部进度条显示当前浏览的区域位置"
            )
        }
        .padding()
        .background(.regularMaterial, in: RoundedRectangle(cornerRadius: 16))
        .padding(.horizontal)
    }
}

struct FeatureRow: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundStyle(.blue.gradient)
                .frame(width: 24, height: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
            
            Spacer()
        }
    }
}

// MARK: - 设计对比演示

struct DesignComparisonView: View {
    var body: some View {
        HStack(spacing: 20) {
            // 原始设计示例
            VStack(alignment: .leading, spacing: 12) {
                Text("原始设计")
                    .font(.headline)
                    .fontWeight(.bold)
                
                VStack(spacing: 8) {
                    // 模拟原始的简单行布局
                    HStack {
                        Image(systemName: "battery.100")
                            .foregroundColor(.green)
                        Text("电池监控")
                            .fontWeight(.medium)
                        Spacer()
                        Toggle("", isOn: .constant(true))
                            .labelsHidden()
                    }
                    .padding(.vertical, 2)
                    
                    HStack {
                        Image(systemName: "thermometer")
                            .foregroundColor(.red)
                        Text("温度监控")
                            .fontWeight(.medium)
                        Spacer()
                        Toggle("", isOn: .constant(false))
                            .labelsHidden()
                    }
                    .padding(.vertical, 2)
                }
                .padding()
                .background(Color(.controlBackgroundColor))
                .cornerRadius(8)
            }
            
            // 现代化设计示例
            VStack(alignment: .leading, spacing: 12) {
                Text("现代化设计")
                    .font(.headline)
                    .fontWeight(.bold)
                
                ModernCard {
                    VStack(spacing: 16) {
                        ModernToggleRow(
                            title: "电池监控",
                            description: "监控电池电量、健康状态和充电信息",
                            icon: "battery.100",
                            color: .green,
                            isOn: .constant(true)
                        )
                        
                        Divider()
                        
                        ModernToggleRow(
                            title: "温度监控",
                            description: "监控CPU和系统温度变化",
                            icon: "thermometer",
                            color: .red,
                            isOn: .constant(false)
                        )
                    }
                }
            }
        }
        .padding()
    }
}

// MARK: - 预览

#Preview("设计预览") {
    MonitoringSettingsPreview()
}

#Preview("设计对比") {
    DesignComparisonView()
        .frame(width: 800, height: 400)
}

#Preview("单页面滚动设置") {
    MonitoringSettingsView()
}

#Preview("标签栏演示") {
    VStack(spacing: 0) {
        // 模拟标签栏
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(SettingsSection.allCases, id: \.self) { section in
                    SectionTabButton(
                        section: section,
                        isSelected: section == .basic
                    ) {
                        // 演示用空操作
                    }
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 12)
        }
        .background(.regularMaterial)

        // 模拟内容区域
        Rectangle()
            .fill(.quaternary)
            .overlay(
                VStack(spacing: 8) {
                    Text("滚动内容区域")
                        .font(.title2)
                        .foregroundColor(.secondary)

                    Text("✨ 点击热区已优化至 44x44 点")
                        .font(.caption)
                        .foregroundColor(.green)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 4)
                        .background(.green.opacity(0.1), in: RoundedRectangle(cornerRadius: 8))
                }
            )
    }
    .frame(width: 600, height: 400)
}

#Preview("点击热区演示") {
    TouchTargetDemo()
}
