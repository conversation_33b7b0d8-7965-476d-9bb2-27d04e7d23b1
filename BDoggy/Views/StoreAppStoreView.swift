//
//  AboutView.swift
//  BDoggy
//
//  Created by K4 on 2024/12/20.
//
import Foundation
import SwiftUI

struct StoreAppStoreView: View {
    @State private var purchaseManager = PurchaseManager.shared
    private let restorePurchasedItem = {}

    private var gifURL: URL? {
        Bundle.main.url(forResource: "timemachine", withExtension: "gif")
    }

    var body: some View {
        VStack(spacing: 0) {
            List {
                HStack {
                    if let url = gifURL {
                        GIFImage(url: url, loopCount: 0) // loopCount: 0 表示无限循环
                            .frame(width: 35, height: 35) // 可以根据需要调整尺寸
                            .padding(.vertical, 10)
                            .padding(.horizontal, 8)
                    } else {
                        // 加载失败时显示占位图
                        Image(systemName: "photo")
                            .resizable()
                            .scaledToFit()
                            .frame(width: 35, height: 35)
                            .padding(.vertical, 10)
                            .padding(.horizontal, 8)
                    }
                    VStack(alignment: .leading) {
                        Text("restore_purchased".localized)
                            .font(.headline)
                        Text("")
                            .font(.subheadline)
                            .foregroundColor(.gray)
                    }
                    Spacer()
                    Button("restore".localized) {
                        Task {
                            await purchaseManager.restorePurchases()
                        }
                    }
                    .buttonStyle(.borderedProminent)
                }
                .listRowSeparator(.hidden)
                .padding(.vertical, 8)
                .padding(.horizontal, 10)
                Section(header: Text("unpurchased_items".localized)) {
                    ForEach($purchaseManager.unPurchaseItems) { $item in
                        StoreAppStoreItemView(viewModel: purchaseManager, item: $item)
                    }
                }

                Section(header: Text("purchased_items".localized)) {
                    ForEach($purchaseManager.purchasedItems) { $item in
                        StoreAppStoreItemView(viewModel: purchaseManager, item: $item)
                    }
                }
            }
            .cornerRadius(10)
            .listRowInsets(EdgeInsets(top: 0, leading: 0, bottom: 0, trailing: 0))
            .listStyle(PlainListStyle()) // 使用 PlainListStyle 以确保没有额外的装饰
        }
        .shadow(radius: 4)
        .padding(10)
        .alert("error".localized, isPresented: $purchaseManager.showErrorAlert) {
            Button("ok".localized) {
                purchaseManager.showErrorAlert = false
            }
        } message: {
            Text(purchaseManager.errorMessage)
        }
    }
}
