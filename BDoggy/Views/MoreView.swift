import AppKit
import SwiftUI

struct MoreView: View {
    @Environment(\.openWindow) private var openWindow
    @Environment(\.openSettings) private var openSettings
    @Binding var currentPage: NavigationTag
    let onBack: () -> Void

    var body: some View {
        VStack(spacing: 0) {
            HStack {
                Button(action: {
                    onBack()
                }) {
                    Label("back".localized, systemImage: "chevron.left")
                        .labelStyle(DefaultLabelStyle())
                        .font(.system(size: 15))
                        .padding(10)
                }.buttonStyle(NoBorderButtonStyle())
                Spacer()
            }
            Spacer()

//            Button(action: {
//                openWindow(id: "runner-maker")
//            }) {
//                HStack(spacing: 0) {
//                    Image(systemName: "wand.and.stars")
//                        .resizable()
//                        .scaledToFit()
//                        .frame(width: 24, height: 24)
//                    Text("runner_maker".localized)
//                        .padding(.leading, 15)
//                        .font(.system(size: 15))
//                    Spacer()
//                }
//                .padding(.leading, 8)
//                .frame(height: 50)
//            }
//            .buttonStyle(NoBorderButtonStyle())

            But<PERSON>(action: {
                openSettings()
            }) {
                HStack(spacing: 0) {
                    Image(systemName: "gear")
                        .resizable()
                        .scaledToFit()
                        .frame(width: 24, height: 24)
                    Text("settings".localized)
                        .padding(.leading, 15)
                        .font(.system(size: 15))
                    Spacer()
                }
                .padding(.leading, 8)
                .frame(height: 50)
            }
            .buttonStyle(NoBorderButtonStyle())

            Button(action: {
                currentPage = .About
            }) {
                HStack(spacing: 0) {
                    Image(systemName: "info.circle")
                        .resizable()
                        .scaledToFit()
                        .frame(width: 24, height: 24)
                    Text("about".localized)
                        .padding(.leading, 15)
                        .font(.system(size: 15))
                    Spacer()
                }
                .padding(.leading, 8)
                .frame(height: 50)
            }
            .buttonStyle(NoBorderButtonStyle())

            Button(action: {
                // 打开App Store页面
                if let url = URL(string: "https://apps.apple.com/app/bdoggy/id6742220542") {
                    NSWorkspace.shared.open(url)
                }
            }) {
                HStack(spacing: 0) {
                    Image(systemName: "arrowshape.up.circle.fill")
                        .resizable()
                        .scaledToFit()
                        .frame(width: 24, height: 24)
                    Text("Check Update".localized)
                        .padding(.leading, 15)
                        .font(.system(size: 15))
                    Spacer()
                }
                .padding(.leading, 8)
                .frame(height: 50)
            }
            .buttonStyle(NoBorderButtonStyle())

            Button(action: {
                // 退出应用程序
                NSApp.terminate(nil)
            }) {
                HStack(spacing: 0) {
                    Image(systemName: "door.left.hand.open")
                        .resizable()
                        .scaledToFit()
                        .frame(width: 24, height: 24)
                    Text("quit_app".localized)
                        .padding(.leading, 15)
                        .font(.system(size: 15))
                    Spacer()
                }
                .padding(.leading, 8)
                .frame(height: 50)
            }
            .buttonStyle(NoBorderButtonStyle())
        }
        .frame(width: 250, height: 250)
        .shadow(radius: 10)
        .padding(.bottom, 5)
        .padding(.horizontal, 5)
        .fixedSize()
    }
}
