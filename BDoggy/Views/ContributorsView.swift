//
//  ContributorsView.swift
//  BDoggy
//
//  Created by K4 on 2024/12/20.
//

import SwiftUI

struct Contributor: Identifiable, Decodable {
    let id = UUID()
    let name: String
    let role: String
    let avatarName: String?
    let githubUrl: String?
    let assets: String?
    
    // 计算属性，用于显示贡献内容
    var contributions: String {
        return assets ?? ""
    }
    
    // 自定义解码器，因为JSON中没有id字段
    enum CodingKeys: String, CodingKey {
        case name, role, avatarName, githubUrl, assets
    }
}

struct ContributorsView: View {
    // 贡献者数据
    @State private var contributors: [Contributor] = []
    
    var body: some View {
        VStack {
            Text("contributors_title".localized)
                .font(.title2)
                .bold()
                .padding(.top, 20)
                .padding(.bottom, 10)
            
            Text("contributors_description".localized)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 20)
                .padding(.bottom, 20)
            
            ScrollView {
                VStack(spacing: 15) {
                    ForEach(contributors) { contributor in
                        ContributorRow(contributor: contributor)
                    }
                }
                .padding(.horizontal, 20)
            }
            
            Spacer()
            
            Text("thank_contributors".localized)
                .font(.caption)
                .foregroundColor(.secondary)
                .padding(.bottom, 20)
        }
        .frame(minWidth: 400, minHeight: 500)
        .onAppear {
            loadContributors()
        }
    }
    
    // 加载贡献者数据
    private func loadContributors() {
        guard let url = Bundle.main.url(forResource: "contributors", withExtension: "json") else {
            print("无法找到contributors.json文件")
            return
        }
        
        do {
            let data = try Data(contentsOf: url)
            let decoder = JSONDecoder()
            contributors = try decoder.decode([Contributor].self, from: data)
        } catch {
            print("解析contributors.json失败: \(error.localizedDescription)")
            // 加载失败时使用默认数据
            contributors = [
                Contributor(name: "K4", role: "Developer", avatarName: "AppIcon", githubUrl: "https://www.bdoggy.icu", assets: "Core Development"),
                Contributor(name: "BDoggy Team", role: "Design", avatarName: nil, githubUrl: "https://www.bdoggy.icu", assets: "UI/UX Design")
            ]
        }
    }
}

struct ContributorRow: View {
    let contributor: Contributor
    
    var body: some View {
        HStack(spacing: 15) {
            // 头像
            if let avatarName = contributor.avatarName, let image = NSImage(named: NSImage.Name(avatarName)) {
                Image(nsImage: image)
                    .resizable()
                    .frame(width: 50, height: 50)
                    .clipShape(Circle())
            } else {
                Image(systemName: "person.circle.fill")
                    .resizable()
                    .frame(width: 50, height: 50)
                    .foregroundColor(.gray)
            }
            
            VStack(alignment: .leading, spacing: 5) {
                Text(contributor.name)
                    .font(.headline)
                
                Text(contributor.role)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                Text("Contributions:\(contributor.contributions)")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            if let githubUrl = contributor.githubUrl {
                Link(destination: URL(string: githubUrl)!) {
                    Image(systemName: "link.circle.fill")
                        .font(.title2)
                        .foregroundColor(.blue)
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(10)
    }
}
