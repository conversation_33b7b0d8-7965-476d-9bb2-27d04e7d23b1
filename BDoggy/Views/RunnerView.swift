//
//  AboutView.swift
//  BDoggy
//
//  Created by K4 on 2024/12/20.
//
import Foundation
import SwiftUI

struct RunnerView: View {
    @ObservedObject var viewModel: RunnersViewModel
    @Binding var currentPage: NavigationTag
    let onBack: () -> Void
    
    var body: some View {
        VStack(spacing: 0) {
            HStack {
                Button(action: {
                    onBack()
                }) {
                    Label("back".localized, systemImage: "chevron.left")
                        .labelStyle(DefaultLabelStyle())
                        .font(.system(size: 15))
                        .padding(10)
                }
                .contentShape(Rectangle()) // 添加这行，确保整个区域可点击
                .buttonStyle(NoBorderButtonStyle())
                Spacer()
            }
            
            if viewModel.isLoading {
                ProgressView("loading".localized)
                    .padding()
                    .frame(maxHeight: .infinity)
            } else if let error = viewModel.error {
                Text("error".localized + ": \(error.localizedDescription)")
                    .foregroundColor(.red)
                    .frame(maxHeight: .infinity)
                
            } else if !viewModel.runners.isEmpty {
                List {
                    ForEach(viewModel.runners) { item in
                        HStack(spacing: 0) {
                            if item.dirName == viewModel.usedIcon {
                                Image(systemName: "checkmark")
                                    .resizable()
                                    .foregroundStyle(Color.orange)
                                    .scaledToFit()
                                    .frame(width: 15, height: 15)
                            } else {
                                Rectangle()
                                    .frame(width: 15, height: 15)
                                    .opacity(0)
                            }
                            Text(item.groupName)
                                .padding(.leading, 10)
                                .font(.headline)
                            Spacer()
                            Image(nsImage: NSImage(contentsOf: item.fileURLs[0]) ?? NSImage(named: "placeholder")!)
                                .resizable()
                                .scaledToFit()
                                .frame(width: 35, height: 35)
                        }
                        .padding(.vertical, 8)
                        //                        .listRowInsets(EdgeInsets())
                        .contentShape(Rectangle()) // 添加这行，确保整个区域可点击
                        .padding(.horizontal, 0)
                        .onTapGesture {
                            viewModel.doChange(from: item.dirName)
                        }
                    }
                }
                .cornerRadius(10)
                .listStyle(PlainListStyle()) // 使用 PlainListStyle 以确保没有额外的装饰
                
            } else {
                Text("no_data_available".localized)
                    .padding()
            }
        }
        .onAppear {
            if (viewModel.runners.isEmpty && !viewModel.isLoading) || viewModel.isUpdate {
                Task {
                    await viewModel.fetchRunners()
                }
            }
        }
        .frame(width: 250, height: 450)
        .shadow(radius: 10)
        .padding(.bottom, 5)
        .padding(.horizontal, 5)
        .toolbar(.hidden) // **隐藏顶部 Toolbar**
        .fixedSize()
    }
}
