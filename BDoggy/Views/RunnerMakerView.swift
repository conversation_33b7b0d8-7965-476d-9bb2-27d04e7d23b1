import SwiftUI
import UniformTypeIdentifiers

struct RunnerMakerView: View {
    @Environment(\.openWindow) private var openWindow
    @StateObject private var viewModel = RunnerMakerViewModel()
    @State private var showingImagePicker: Bool = false
    @State private var selectedRunner: UUID? // 用于跟踪选中的跑者
    @State private var draggedItem: Int?
    @State private var draggedOffset: CGSize = .zero
    // 在 View 顶部添加状态变量
    @State private var showPurchasePopover = false
    private let totalMaxSlider: Double = 5
    private let totalMinSlider: Double = 0
    // 添加到 View 的属性中
    @State private var tortoiseScale: CGFloat = 1.0
    @State private var hareScale: CGFloat = 1.0
    
    var body: some View {
        HStack {
            // 左侧注册的跑者列表
            VStack(alignment: .leading) {
                Text("registered_runners".localized)
                    .font(.headline)
                List {
                    ForEach($viewModel.runners) { $item in
                        RunnerMakerItemView(item: $item, viewModel: viewModel)
                            .listRowBackground(item.id == selectedRunner ? Color.accentColor.opacity(0.2) : Color.clear)
                            .contentShape(Rectangle())
                            .onTapGesture {
                                selectedRunner = item.id
                                viewModel.loadEditRunner(item: item)
                            }
                    }
                }
                .frame(width: 200)
                .cornerRadius(10)
                .listStyle(PlainListStyle())
                HStack {
                    HStack(spacing: 16) {
                        Button(action: {
                            viewModel.clean()
                        }) {
                            Image(systemName: "plus.circle.fill")
                                .resizable()
                                .scaledToFit()
                                .frame(width: 20, height: 20)
                        }
                        .buttonStyle(.borderless)
                        
                        Button(action: {
                            if let selectedRunner = selectedRunner,
                               let selectedItem = viewModel.runners.first(where: { $0.id == selectedRunner })
                            {
                                Task {
                                    await viewModel.deleteRunner(item: selectedItem)
                                }
                            } else {
                                viewModel.errorMessage = "please_select_runner".localized
                                viewModel.showErrorAlert = true
                            }
                        }) {
                            Image(systemName: "minus.circle.fill")
                                .resizable()
                                .scaledToFit()
                                .frame(width: 20, height: 20)
                        }
                        .buttonStyle(.borderless)
                        .disabled(selectedRunner == nil)
                        .opacity(selectedRunner == nil ? 0.5 : 1.0)
                    }
                }
            }
            .padding()
            .onAppear {
                viewModel.fetchRunners()
            }
            
            VStack(spacing: 0) {
                // 主要内容区域
                VStack(spacing: 24) {
                    // 名称输入区域
                    GroupBox {
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Text("runner_name".localized)
                                    .font(.headline)
                                    .frame(width: 100, alignment: .leading)
                                TextField("type_name".localized, text: $viewModel.runnerName)
                                    .textFieldStyle(.plain)
                                    .padding(10)
                                    .frame(height: 30)
                                    .foregroundColor(Color(hex: "333333"))
                                    .background(Color.white)
                                    .cornerRadius(4)
                                    .shadow(radius: 1)
                                    .onChange(of: viewModel.runnerName) { _, newValue in
                                        // 只允许字母、数字、下划线和中文字符
                                        let filtered = newValue.filter { char in
                                            char.isLetter || char.isNumber || char == "_" ||
                                                (char >= "\u{4E00}" && char <= "\u{9FA5}")
                                        }
                                        if filtered != newValue {
                                            viewModel.runnerName = filtered
                                        }
                                    }
                            }
                            
                            Text("preconditions".localized)
                                .padding(.vertical, 2)
                                .foregroundStyle(Color.gray)
                                .font(.body)
                            Label("format_png".localized, systemImage: "doc")
                                .font(.subheadline)
                                .padding(.leading, 15)
                                .foregroundStyle(Color.gray)
                            Label("size_limit".localized, systemImage: "doc.text.magnifyingglass")
                                .font(.subheadline)
                                .padding(.leading, 15)
                                .foregroundStyle(Color.gray)
                            Label("height_limit".localized, systemImage: "arrow.up.and.down")
                                .font(.subheadline)
                                .padding(.leading, 15)
                                .foregroundStyle(Color.gray)
                            Label("width_limit".localized, systemImage: "arrow.left.and.right")
                                .font(.subheadline)
                                .padding(.leading, 15)
                                .foregroundStyle(Color.gray)
                            Label("frame_limit".localized, systemImage: "lessthanorequalto")
                                .font(.subheadline)
                                .padding(.leading, 15)
                                .foregroundStyle(Color.gray)
                        }
                        .padding(8)
                    }
                    
                    // 帧编辑区域
                    GroupBox {
                        VStack(spacing: 12) {
                            // 标题和按钮
                            HStack {
                                Text("frames".localized)
                                    .font(.headline)
                                Spacer()
                                HStack(spacing: 16) {
                                    Button(action: {
                                        showingImagePicker = true
                                    }) {
                                        Image(systemName: "plus.circle.fill")
                                            .resizable()
                                            .scaledToFit()
                                            .frame(width: 20, height: 20)
                                    }
                                    .buttonStyle(.borderless)
                                    
                                    Button(action: {
                                        if !viewModel.frames.isEmpty {
                                            viewModel.frames.removeLast()
                                        }
                                    }) {
                                        Image(systemName: "minus.circle.fill")
                                            .resizable()
                                            .scaledToFit()
                                            .frame(width: 20, height: 20)
                                    }
                                    .buttonStyle(.borderless)
                                    .disabled(viewModel.frames.isEmpty)
                                    .opacity(viewModel.frames.isEmpty ? 0.5 : 1.0)
                                }
                            }
                            
                            // 图片网格
                            DraggableGrid(
                                items: viewModel.frames.indices.map { FrameItem(id: $0, frame: viewModel.frames[$0]) },
                                columns: [GridItem(.adaptive(minimum: 120))],
                                spacing: 16,
                                itemWidth: 120
                            ) { item in
                                VStack(spacing: 8) {
                                    ZStack(alignment: .topTrailing) {
                                        Image(nsImage: item.frame.image)
                                            .resizable()
                                            .scaledToFit()
                                            .frame(height: 36)
                                            .padding()
                                            .background(Color.gray.opacity(0.1))
                                            .cornerRadius(8)
                                        
                                        // 添加删除按钮
                                        Button(action: {
                                            if let index = viewModel.frames.firstIndex(where: { $0.url == item.frame.url }) {
                                                viewModel.frames.remove(at: index)
                                            }
                                        }) {
                                            Image(systemName: "xmark.circle.fill")
                                                .foregroundColor(.red)
                                                .background(Color.white.clipShape(Circle()))
                                        }
                                        .buttonStyle(.borderless)
                                        .offset(x: 8, y: -8)
                                    }
                                    
                                    Text(item.frame.name)
                                        .font(.caption)
                                        .lineLimit(2)
                                        .foregroundColor(Color("text"))
                                        .multilineTextAlignment(.center)
                                }
                                .frame(maxWidth: .infinity)
                                .padding(.vertical, 8)
                                .background(Color("theme"))
                                .cornerRadius(10)
                                .shadow(color: .gray.opacity(0.1), radius: 2, x: 0, y: 1)
                            } onReorder: { fromIndex, toIndex in
                                let item = viewModel.frames.remove(at: fromIndex)
                                viewModel.frames.insert(item, at: toIndex)
                            }
                            .frame(minHeight: 200)
                        }
                        .padding(8)
                    }
                    
                    // 预览区域
                    GroupBox {
                        VStack(spacing: 12) {
                            HStack {
                                Text("preview".localized)
                                    .font(.headline)
                                Spacer()
                            }
                            
                            HStack {
                                if let previewFrame = viewModel.currentPreviewFrame {
                                    Image(nsImage: previewFrame)
                                        .resizable()
                                        .scaledToFit()
                                        .frame(maxWidth: 80, maxHeight: 50)
                                        .padding()
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 5)
                                                .stroke(Color("text"), lineWidth: 1)
                                        )
                                } else {
                                    Image(systemName: "photo")
                                        .resizable()
                                        .scaledToFit()
                                        .frame(maxWidth: 80, maxHeight: 50)
                                        .padding()
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 5)
                                                .stroke(Color("text"), lineWidth: 1)
                                        )
                                }
                                Image(systemName: "tortoise")
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: 40, height: 40)
                                    .scaleEffect(tortoiseScale)
                                    .animation(.spring(response: 0.3, dampingFraction: 0.5), value: tortoiseScale)
                                    .padding()
                                    .onTapGesture {
                                        if viewModel.previewSpeed < totalMaxSlider {
                                            viewModel.previewSpeed += 1
                                            viewModel.previewSpeedChange()
                                        } else {
                                            tortoiseScale = 1.3
                                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                                                tortoiseScale = 1.0
                                            }
                                        }
                                    }
                                    .disabled(viewModel.frames.isEmpty)
                                Slider(value: Binding(
                                    get: { totalMaxSlider - viewModel.previewSpeed + totalMinSlider },
                                    set: {
                                        let newValue = totalMaxSlider - $0 + totalMinSlider
                                        viewModel.previewSpeed = newValue
                                        // 检查是否达到极限值并触发动画
                                        if newValue >= totalMaxSlider {
                                            tortoiseScale = 1.3
                                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                                                tortoiseScale = 1.0
                                            }
                                        } else if newValue <= totalMinSlider {
                                            hareScale = 1.3
                                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                                                hareScale = 1.0
                                            }
                                        }
                                    }
                                ), in: totalMinSlider ... totalMaxSlider, step: 1)
                                    .frame(maxWidth: .infinity)
                                    .padding()
                                    .disabled(viewModel.frames.isEmpty)
                                    .onChange(of: viewModel.previewSpeed) { _, _ in
                                        viewModel.previewSpeedChange()
                                    }
                                Image(systemName: "hare")
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: 40, height: 40)
                                    .scaleEffect(hareScale)
                                    .animation(.spring(response: 0.3, dampingFraction: 0.5), value: hareScale)
                                    .padding()
                                    .onTapGesture {
                                        if viewModel.previewSpeed > totalMinSlider {
                                            viewModel.previewSpeed -= 1
                                            viewModel.previewSpeedChange()
                                        } else {
                                            hareScale = 1.3
                                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                                                hareScale = 1.0
                                            }
                                        }
                                    }
                                    .disabled(viewModel.frames.isEmpty)
                            }
                        }
                        .padding(8)
                    }
                }
                .padding()
                
                // 底部操作区
                GroupBox {
                    HStack {
                        Spacer()
                        Image(systemName: viewModel.isEnableCreate ? "lock.open.fill" : "lock.fill")
                            .resizable()
                            .scaledToFit()
                            .frame(width: 24, height: 24)
                            .foregroundColor(viewModel.isEnableCreate ? Color("text") : Color.accentColor)
                            .help(viewModel.isEnableCreate ? "permission_granted".localized : "purchase_permission_hint".localized)
                            .onTapGesture {
                                if !viewModel.isEnableCreate {
                                    showPurchasePopover = true
                                }
                            }
                            .popover(isPresented: $showPurchasePopover, arrowEdge: .top) {
                                VStack(alignment: .leading, spacing: 12) {
                                    Text("purchase_tip".localized)
                                        .font(.headline)
                                    Text("purchase_maker_permission".localized)
                                        .font(.body)
                                        .foregroundColor(.secondary)
                                    Button("go_to_store".localized) {
                                        showPurchasePopover = false
                                        openWindow(id: "runner-store")
                                    }
                                    .buttonStyle(.borderedProminent)
                                }
                                .padding()
                                .frame(width: 250)
                            }
                            .padding(.horizontal, 10)
                        Button("register".localized) {
                            viewModel.saveAndRefresh()
                        }
                        .buttonStyle(.borderedProminent)
                        .controlSize(.large)
                        .disabled(!viewModel.isEnableCreate)
                        .opacity(viewModel.isEnableCreate ? 1.0 : 0.5)
                    }
                    .padding(8)
                }
                .padding()
            }
        }
        .frame(minWidth: 800, minHeight: 800)
        .fileImporter(
            isPresented: $showingImagePicker,
            allowedContentTypes: Constants.ImageExtensions.map { UTType(filenameExtension: $0)! },
            allowsMultipleSelection: true
        ) { result in
            handleSelectedImages(result)
        }
        .alert("error".localized, isPresented: $viewModel.showErrorAlert) {
            Button("ok".localized) {
                viewModel.showErrorAlert = false
            }
        } message: {
            Text(viewModel.errorMessage)
        }
        .onDisappear {
            Logger.debug("RunnerMaker视图消失")
        }
        .onReceive(NotificationCenter.default.publisher(for: .windowWillCloseRunnerMaker)) { _ in
            Logger.debug("RunnerMaker窗口关闭")
            viewModel.cleanupUnusedResources()
        }
    }
    
    private func handleSelectedImages(_ result: Result<[URL], Error>) {
        do {
            let urls = try result.get()
            Logger.debug("handleSelectedImages", urls)
            // 检查总数量限制
            if viewModel.frames.count + urls.count > 30 {
                viewModel.errorMessage = "error_max_images".localized
                viewModel.showErrorAlert = true
                return
            }
            
            for url in urls {
                // 检查文件扩展名
                guard Constants.ImageExtensions.contains(url.pathExtension.lowercased()) else {
                    viewModel.errorMessage = String(format: "error_format".localized,
                                                    Constants.ImageExtensions.joined(separator: ", "))
                    viewModel.showErrorAlert = true
                    continue
                }
                
                // 检查文件大小
                do {
                    guard let fileSize = try FileManager.default.attributesOfItem(atPath: url.path)[.size] as? Int64,
                          fileSize <= 30 * 1024
                    else {
                        viewModel.errorMessage = "error_size".localized
                        viewModel.showErrorAlert = true
                        continue
                    }
                } catch {
                    Logger.error("无法获取文件大小: \(error.localizedDescription)")
                    viewModel.errorMessage = "无法访问文件: \(error.localizedDescription)"
                    viewModel.showErrorAlert = true
                    continue
                }
                
                // 安全访问沙盒外资源
                guard url.startAccessingSecurityScopedResource() else {
                    Logger.error("无法访问安全作用域资源")
                    viewModel.errorMessage = "无法访问文件，请重新选择"
                    viewModel.showErrorAlert = true
                    continue
                }
                
                defer { url.stopAccessingSecurityScopedResource() }
                
                // 使用可选绑定安全加载图片
                guard let image = NSImage(contentsOf: url) else {
                    viewModel.errorMessage = "无法加载图片，格式可能不受支持"
                    viewModel.showErrorAlert = true
                    continue
                }
                
                // 检查图片尺寸
                let imageSize = image.size
                guard imageSize.height >= 10 && imageSize.height <= 100 else {
                    viewModel.errorMessage = "error_height".localized
                    viewModel.showErrorAlert = true
                    continue
                }
                guard imageSize.width >= 10 && imageSize.width <= 100 else {
                    viewModel.errorMessage = "error_width".localized
                    viewModel.showErrorAlert = true
                    continue
                }
                
                // 添加图片到帧列表
                viewModel.frames.append((image: image, name: url.lastPathComponent, url: url))
            }
            
            // 批量处理完成后再上传
            if !viewModel.frames.isEmpty {
                viewModel.uploadFrames()
            }
        } catch {
            Logger.error("选择图片时出错: \(error.localizedDescription)")
            viewModel.errorMessage = String(format: "error_image_selection".localized, error.localizedDescription)
            viewModel.showErrorAlert = true
        }
    }
}

// 添加 FrameItem 结构体
private struct FrameItem: Identifiable {
    let id: Int
    let frame: (image: NSImage, name: String, url: URL)
}
