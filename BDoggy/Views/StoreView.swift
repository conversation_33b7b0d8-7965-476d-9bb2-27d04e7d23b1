//
//  AboutView.swift
//  BDoggy
//
//  Created by K4 on 2024/12/20.
//
import Foundation
import SwiftUI

struct StoreView: View {
    @StateObject private var viewModel = StoreViewModel()
    var body: some View {
        VStack(spacing: 0) {
            List {
                Section(header: Text("unpurchased_items".localized)) {
                    ForEach(viewModel.unPurchaseItems) { item in

                        StoreItemView(item: item, viewModel: viewModel)
                    }
                }

                Section(header: Text("purchased_items".localized)) {
                    ForEach(viewModel.purchasedItems) { item in

                        StoreItemView(item: item, viewModel: viewModel)
                    }
                }
                Section(header: Text("downloaded_items".localized)) {
                    ForEach(viewModel.downloadedItems) { item in

                        StoreItemView(item: item, viewModel: viewModel)
                    }
                }
            }
            .cornerRadius(10)
            .listRowInsets(EdgeInsets(top: 0, leading: 0, bottom: 0, trailing: 0))
            .listStyle(PlainListStyle()) // 使用 PlainListStyle 以确保没有额外的装饰
        }
        .onAppear {
            viewModel.fetchStores()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .shadow(radius: 4)
        .padding(10)
    }
}
