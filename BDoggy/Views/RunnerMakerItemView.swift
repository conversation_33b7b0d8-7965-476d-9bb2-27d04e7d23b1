//
//  StoreItemView.swift
//  BDoggy
//
//  Created by K4 on 2025/2/13.
//

import Foundation
import SwiftUI

// 修改描述文本
struct RunnerMakerItemView: View {
    @Binding var item: FrameGroup // 监听单个 StoreItem 的状态变化
    @State var viewModel: RunnerMakerViewModel
    var body: some View {
        HStack {
            AsyncImage(url: item.fileURLs[0]) { image in
                image.image?
                    .resizable()
                    .scaledToFit()
                    .frame(width: 35, height: 35)
            }
            .padding(.vertical, 10)
            .padding(.horizontal, 8)
            VStack(alignment: .leading) {
                Text(item.groupName)
                    .font(.headline)
                Text("runner_description".localized)
                    .font(.subheadline)
                    .foregroundColor(.gray)
            }
            Spacer()
        }
        .listRowSeparator(.hidden)
        .padding(.vertical, 8)
        .padding(.horizontal, 10)
    }
}
