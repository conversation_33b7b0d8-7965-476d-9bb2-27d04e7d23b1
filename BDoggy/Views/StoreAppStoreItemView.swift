//
//  StoreItemView.swift
//  BDoggy
//
//  Created by K4 on 2025/2/13.
//

import Foundation
import SwiftUI

struct StoreAppStoreItemView: View {
    var viewModel: PurchaseManager
    @Binding var item: ProductData // 监听单个 ProductData 的状态变化

    private func displayURL(path: String?, ext: String?) -> URL? {
        Bundle.main.url(forResource: path, withExtension: ext)
    }

    var body: some View {
        HStack {
            if item.displayIconExtension == "gif" {
                if item.isDisplayIconRemote,
                   let iconURL = URL(string: item.displayIcon)
                {
                    AsyncGIFImage(url: iconURL, loopCount: 0) {
                        Logger.debug("图片失败")
                    }
                    .frame(width: 35, height: 35)
                    .padding(.vertical, 10)
                    .padding(.horizontal, 8)
                } else if let iconURL = displayURL(path: item.displayIconPathWithoutExtension, ext: item.displayIconExtension) {
                    GIFImage(url: iconURL, loopCount: 0) // loopCount: 0 表示无限循环
                        .frame(width: 35, height: 35) // 可以根据需要调整尺寸
                        .padding(.vertical, 10)
                        .padding(.horizontal, 8)
                } else {
                    // 处理 URL 无效或非 GIF 的情况
                    Image(systemName: "photo") // 显示默认图片
                        .frame(width: 35, height: 35)
                        .padding(.vertical, 10)
                        .padding(.horizontal, 8)
                }
            } else {
                if item.isDisplayIconRemote,
                   let iconURL = URL(string: item.displayIcon)
                {
                    AsyncImage(url: iconURL) { phase in
                        switch phase {
                        case .empty:
                            ProgressView() // 加载中显示进度指示器
                        case .success(let image):
                            image
                                .resizable()
                                .scaledToFit()
                                .frame(width: 35, height: 35)
                        case .failure:
                            Image(systemName: "photo") // 加载失败显示默认图片
                                .resizable()
                                .scaledToFit()
                                .frame(width: 35, height: 35)
                        @unknown default:
                            Image(systemName: "photo")
                                .resizable()
                                .scaledToFit()
                                .frame(width: 35, height: 35)
                        }
                    }
                    .padding(.vertical, 10)
                    .padding(.horizontal, 8)
                } else {
                    // 处理 URL 无效或非 GIF 的情况
                    Image(systemName: "photo") // 显示默认图片
                        .frame(width: 35, height: 35)
                        .padding(.vertical, 10)
                        .padding(.horizontal, 8)
                }
            }

            VStack(alignment: .leading) {
                Text(item.displayName)
                    .font(.headline)
                Text(item.description)
                    .font(.subheadline)
                    .foregroundColor(.gray)
            }
            Spacer()
            if item.isPurchased {
                switch item.productType {
                case "service":

                    Text("available".localized)
                        .font(.subheadline)
                        .foregroundColor(.gray)
                case "runner":
                    if item.isDownloading {
                        ProgressView() // 显示下载状态
                    } else if !item.isDownloaded {
                        Button("download".localized) {
                            // 下载
                            viewModel.download(product: item)
                        }
                        .buttonStyle(.borderedProminent)
                    } else {
                        Text("downloaded".localized)
                            .font(.subheadline)
                            .foregroundColor(.gray)
                    }
                default:
                    Text("unavailable".localized)
                        .font(.subheadline)
                        .foregroundColor(.gray)
                }

            } else {
                Text(item.price)
                Button("purchase".localized) {
                    // 购买逻辑
                    Task {
                        do {
                            try await viewModel.purchaseProduct(productID: item.id)
                        } catch let error as PurchaseError {
                            viewModel.errorMessage = error.errorDescription
                            viewModel.showErrorAlert = true
                        }
                    }
                }
                .buttonStyle(BorderedButtonStyle())
            }
        }
        .listRowSeparator(.hidden)
        .padding(.vertical, 8)
        .padding(.horizontal, 10)
    }
}
