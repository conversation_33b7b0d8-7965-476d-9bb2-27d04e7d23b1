#!/bin/bash

# 设置资源目录
RESOURCES_DIR="/Users/<USER>/macosPrograms/BDoggy/BDoggy/Resources"

# 初始化计数器
TOTAL_FILES=0
PROCESSED_FILES=0

echo "开始扫描并移除图片文件的 Resource Fork..."
echo "资源目录: $RESOURCES_DIR"

# 递归查找所有图片文件
while IFS= read -r file; do
    ((TOTAL_FILES++))
    
    # 检查文件是否有 Resource Fork
    if [ "$(xattr "$file" 2>/dev/null)" != "" ]; then
        echo "处理文件: $file"
        xattr -cr "$file"
        
        # 检查命令是否成功执行
        if [ $? -eq 0 ]; then
            ((PROCESSED_FILES++))
        else
            echo "警告: 无法移除 Resource Fork: $file"
        fi
    else
        echo "跳过文件(无 Resource Fork): $file"
    fi
done < <(find "$RESOURCES_DIR" -type f \( -name "*.png" -o -name "*.jpg" -o -name "*.jpeg" -o -name "*.gif" \))

echo "----------------------------------------"
echo "扫描完成!"
echo "总共扫描图片文件: $TOTAL_FILES"
echo "成功移除 Resource Fork 的文件: $PROCESSED_FILES"
echo "----------------------------------------"