//
//  FrameIntervalCalculatorTests.swift
//  BDoggyTests
//
//  Created by K4 on 2024/12/10.
//

@testable import BDoggy
import XCTest

final class FrameIntervalCalculatorTests: XCTestCase {
    var calculator: FrameIntervalCalculator!
    var timerManager: GCDTimerManager!
    let frameCount = 28
    
    override func setUpWithError() throws {
        // 使用与应用相同的参数初始化计算器
        calculator = FrameIntervalCalculator(
            totalMaxInterval: 4000,
            frameMinInterval: Constants.frameMinInterval
        )
        timerManager = GCDTimerManager(interval: 100, executionQueue: .main)
    }
    
    override func tearDownWithError() throws {
        calculator = nil
        timerManager = nil
    }
    
    func testCPUUsageSimulation() throws {
        // 创建期望，等待所有测试完成
        let expectation = XCTestExpectation(description: "完成所有CPU使用率测试")
        
        // 创建一个信号量来控制测试的顺序执行
        let semaphore = DispatchSemaphore(value: 0)
        
        // 创建一个队列来按顺序执行测试
        let testQueue = DispatchQueue(label: "com.doggy.BDoggy.testQueue")
        testQueue.async {
            var startTime = Date().timeIntervalSince1970
            
            // 使用定时器执行任务
            self.timerManager.startTimer {
                let currentTime = Date().timeIntervalSince1970
                let elapsedTime = currentTime - startTime
                print("CPU使用率测试耗时:\(elapsedTime * 1000)")
                semaphore.signal()
            }
            // 模拟从1%到100%的CPU使用率
            for cpuUsage in 1 ... 100 {
                // 计算帧间隔
                let interval = self.calculator.calculateInterval(
                    totalUsage: Double(cpuUsage),
                    frameCount: self.frameCount
                )
                
                // 将秒转换为毫秒
                // let milliseconds = Int(interval * 1000)
                let milliseconds = interval
                startTime = Date().timeIntervalSince1970
                print("CPU使用率: \(cpuUsage)%, 帧间隔: \(interval)毫秒")
                // 更新定时器间隔
                self.timerManager.updateIntervalMilliseconds(to: milliseconds)
                
                // 等待任务执行完成
                semaphore.wait()
                
                // 添加小延迟，避免测试太快
//                Thread.sleep(forTimeInterval: 0.5)
            }
            
            // 停止定时器，准备下一次测试
            self.timerManager.stopTimer()
            // 所有测试完成
            expectation.fulfill()
        }
        
        // 等待所有测试完成，设置30秒超时
        wait(for: [expectation], timeout: 30.0)
    }
    
    func testFrameIntervalCalculation() {
        // 测试边界条件
        // 1. CPU使用率为0%
        let minInterval = calculator.calculateInterval(totalUsage: 0, frameCount: frameCount)
        XCTAssertEqual(minInterval, Constants.totalMaxInterval / frameCount)
        
        // 2. CPU使用率为100%
        let maxInterval = calculator.calculateInterval(totalUsage: 100, frameCount: frameCount)
        XCTAssertEqual(maxInterval, Constants.frameMinInterval)
        
        // 3. 中间值测试
        let midInterval = calculator.calculateInterval(totalUsage: 50, frameCount: frameCount)
        XCTAssertEqual(midInterval, Int(Double(Constants.totalMaxInterval) * 0.5 / Double(frameCount)))
    }
    
    func testTimerPrecision() {
        // 创建期望
        let expectation = XCTestExpectation(description: "测试定时器精度")
        
        // 设置一个较短的间隔（50毫秒）
        let intervalMs = 50
        timerManager.updateIntervalMilliseconds(to: intervalMs)
        
        var executionTimes: [TimeInterval] = []
        let executionCount = 10
        var count = 0
        
        let startTime = Date().timeIntervalSince1970
        
        timerManager.startTimer {
            let currentTime = Date().timeIntervalSince1970
            let elapsedTime = currentTime - startTime
            executionTimes.append(elapsedTime)
            
            count += 1
            print("执行次数: \(count), 经过时间: \(elapsedTime)秒")
            
            if count >= executionCount {
                self.timerManager.stopTimer()
                
                // 计算平均间隔和标准差
                var totalInterval: TimeInterval = 0
                var intervals: [TimeInterval] = []
                
                for i in 1 ..< executionTimes.count {
                    let interval = executionTimes[i] - executionTimes[i - 1]
                    intervals.append(interval)
                    totalInterval += interval
                }
                
                let avgInterval = totalInterval / Double(intervals.count)
                
                // 计算标准差
                var variance: Double = 0
                for interval in intervals {
                    let diff = interval - avgInterval
                    variance += diff * diff
                }
                variance /= Double(intervals.count)
                let stdDev = sqrt(variance)
                
                print("平均间隔: \(avgInterval * 1000)毫秒, 标准差: \(stdDev * 1000)毫秒")
                print("理论间隔: \(intervalMs)毫秒")
                
                // 验证平均间隔是否接近预期值（允许10%的误差）
                let expectedInterval = Double(intervalMs) / 1000.0
                let tolerance = expectedInterval * 0.1
                XCTAssertEqual(avgInterval, expectedInterval, accuracy: tolerance)
                
                expectation.fulfill()
            }
        }
        
        wait(for: [expectation], timeout: Double(executionCount) * Double(intervalMs) / 1000.0 * 2)
    }
}
