// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		C91C563D2D768D0D0084C378 /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = C91C563C2D768D0D0084C378 /* IOKit.framework */; };
		C97C529F2D670FE500E5B6CB /* StoreKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = C97C529E2D670FE500E5B6CB /* StoreKit.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		C92FD1BA2DB24859008F53CC /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = C98631012D0696F900BDFF9D /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = C98631082D0696F900BDFF9D;
			remoteInfo = BDoggy;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		C91C563C2D768D0D0084C378 /* IOKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = IOKit.framework; path = System/Library/Frameworks/IOKit.framework; sourceTree = SDKROOT; };
		C92FD1B62DB24859008F53CC /* BDoggyTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = BDoggyTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		C9479BD12D7FFB5500C1F27D /* BDoggy.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = BDoggy.app; sourceTree = BUILT_PRODUCTS_DIR; };
		C97C529E2D670FE500E5B6CB /* StoreKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = StoreKit.framework; path = System/Library/Frameworks/StoreKit.framework; sourceTree = SDKROOT; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		C97DACA62D190B48000749A2 /* Exceptions for "BDoggy" folder in "BDoggy" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = C98631082D0696F900BDFF9D /* BDoggy */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		C92FD1B72DB24859008F53CC /* BDoggyTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = BDoggyTests;
			sourceTree = "<group>";
		};
		C97DAC972D190B48000749A2 /* BDoggy */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				C97DACA62D190B48000749A2 /* Exceptions for "BDoggy" folder in "BDoggy" target */,
			);
			path = BDoggy;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		C92FD1B32DB24859008F53CC /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C98631062D0696F900BDFF9D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C97C529F2D670FE500E5B6CB /* StoreKit.framework in Frameworks */,
				C91C563D2D768D0D0084C378 /* IOKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		C97C529D2D670FE500E5B6CB /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				C91C563C2D768D0D0084C378 /* IOKit.framework */,
				C97C529E2D670FE500E5B6CB /* StoreKit.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		C98631002D0696F900BDFF9D = {
			isa = PBXGroup;
			children = (
				C97DAC972D190B48000749A2 /* BDoggy */,
				C92FD1B72DB24859008F53CC /* BDoggyTests */,
				C97C529D2D670FE500E5B6CB /* Frameworks */,
				C9479BD12D7FFB5500C1F27D /* BDoggy.app */,
				C92FD1B62DB24859008F53CC /* BDoggyTests.xctest */,
			);
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		C92FD1B52DB24859008F53CC /* BDoggyTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C92FD1BC2DB24859008F53CC /* Build configuration list for PBXNativeTarget "BDoggyTests" */;
			buildPhases = (
				C92FD1B22DB24859008F53CC /* Sources */,
				C92FD1B32DB24859008F53CC /* Frameworks */,
				C92FD1B42DB24859008F53CC /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				C92FD1BB2DB24859008F53CC /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				C92FD1B72DB24859008F53CC /* BDoggyTests */,
			);
			name = BDoggyTests;
			packageProductDependencies = (
			);
			productName = BDoggyTests;
			productReference = C92FD1B62DB24859008F53CC /* BDoggyTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		C98631082D0696F900BDFF9D /* BDoggy */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C98631182D0696FB00BDFF9D /* Build configuration list for PBXNativeTarget "BDoggy" */;
			buildPhases = (
				C98631052D0696F900BDFF9D /* Sources */,
				C98631062D0696F900BDFF9D /* Frameworks */,
				C98631072D0696F900BDFF9D /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				C97DAC972D190B48000749A2 /* BDoggy */,
			);
			name = BDoggy;
			packageProductDependencies = (
			);
			productName = BDoggy;
			productReference = C9479BD12D7FFB5500C1F27D /* BDoggy.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		C98631012D0696F900BDFF9D /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1600;
				LastUpgradeCheck = 1600;
				TargetAttributes = {
					C92FD1B52DB24859008F53CC = {
						CreatedOnToolsVersion = 16.0;
						TestTargetID = C98631082D0696F900BDFF9D;
					};
					C98631082D0696F900BDFF9D = {
						CreatedOnToolsVersion = 16.0;
					};
				};
			};
			buildConfigurationList = C98631042D0696F900BDFF9D /* Build configuration list for PBXProject "BDoggy" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hans",
				ja,
				es,
				"zh-Hant",
			);
			mainGroup = C98631002D0696F900BDFF9D;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				C97DACAC2D191E1B000749A2 /* XCRemoteSwiftPackageReference "ZIPFoundation" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = C98631002D0696F900BDFF9D;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				C98631082D0696F900BDFF9D /* BDoggy */,
				C92FD1B52DB24859008F53CC /* BDoggyTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		C92FD1B42DB24859008F53CC /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C98631072D0696F900BDFF9D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		C92FD1B22DB24859008F53CC /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C98631052D0696F900BDFF9D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		C92FD1BB2DB24859008F53CC /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = C98631082D0696F900BDFF9D /* BDoggy */;
			targetProxy = C92FD1BA2DB24859008F53CC /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		C92FD1BD2DB24859008F53CC /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = DW8HSJN94S;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.doggy.BDoggyTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/BDoggy.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/BDoggy";
			};
			name = Debug;
		};
		C92FD1BE2DB24859008F53CC /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = DW8HSJN94S;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.doggy.BDoggyTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/BDoggy.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/BDoggy";
			};
			name = Release;
		};
		C98631162D0696FB00BDFF9D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		C98631172D0696FB00BDFF9D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		C98631192D0696FB00BDFF9D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				"ASSETCATALOG_COMPILER_APPICON_NAME[sdk=macosx*]" = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CODE_SIGN_ENTITLEMENTS = BDoggy/BDoggy.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 7;
				DEVELOPMENT_ASSET_PATHS = "\"BDoggy/Preview Content\"";
				DEVELOPMENT_TEAM = DW8HSJN94S;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = BDoggy/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = BDoggy;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.utilities";
				INFOPLIST_KEY_LSBackgroundOnly = YES;
				INFOPLIST_KEY_LSUIElement = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "© 2024-2028 Knight4 LIU";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 1.3.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.doggy.BDoggy;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		C986311A2D0696FB00BDFF9D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CODE_SIGN_ENTITLEMENTS = BDoggy/BDoggy.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 7;
				DEVELOPMENT_ASSET_PATHS = "\"BDoggy/Preview Content\"";
				DEVELOPMENT_TEAM = DW8HSJN94S;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = BDoggy/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = BDoggy;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.utilities";
				INFOPLIST_KEY_LSBackgroundOnly = YES;
				INFOPLIST_KEY_LSUIElement = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "© 2024-2028 Knight4 LIU";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 1.3.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.doggy.BDoggy;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		C92FD1BC2DB24859008F53CC /* Build configuration list for PBXNativeTarget "BDoggyTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C92FD1BD2DB24859008F53CC /* Debug */,
				C92FD1BE2DB24859008F53CC /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C98631042D0696F900BDFF9D /* Build configuration list for PBXProject "BDoggy" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C98631162D0696FB00BDFF9D /* Debug */,
				C98631172D0696FB00BDFF9D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C98631182D0696FB00BDFF9D /* Build configuration list for PBXNativeTarget "BDoggy" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C98631192D0696FB00BDFF9D /* Debug */,
				C986311A2D0696FB00BDFF9D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		C97DACAC2D191E1B000749A2 /* XCRemoteSwiftPackageReference "ZIPFoundation" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/weichsel/ZIPFoundation.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 0.9.19;
			};
		};
/* End XCRemoteSwiftPackageReference section */
	};
	rootObject = C98631012D0696F900BDFF9D /* Project object */;
}
